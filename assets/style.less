/*------- normalize -------*/
*{margin: 0; padding: 0; border: 0; outline: none; -webkit-tap-highlight-color: transparent; box-sizing: border-box; -webkit-font-smoothing: antialiased;}
::before, ::after{box-sizing: border-box;}
img{max-width: 100%; height: auto;}
html{font-family: sans-serif; /* 1 */ -ms-text-size-adjust: 100%; /* 2 */ -webkit-text-size-adjust: 100%; /* 2 */ scroll-behavior: smooth; scroll-padding-top: 80px;}
article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary{display: block;}
ins{text-decoration: none; font-weight: normal; color: @textColor;}
audio, canvas, progress, video{display: inline-block; /* 1 */}
audio:not([controls]){display: none; height: 0;}
progress{vertical-align: baseline;}
[hidden], template{display: none;}
a{background-color: transparent; -webkit-text-decoration-skip: objects; /* 2 */}
a:active, a:hover{outline: 0; -webkit-tap-highlight-color: transparent;}
abbr[title]{border-bottom: 1px dotted;}
b, strong{font-weight: bold;}
dfn{font-style: italic;}
mark{background: #ff0; color: #000;}
small{font-size: 80%;}
sub, sup{font-size: 75%; line-height: 0; position: relative; vertical-align: baseline;}
sup{top: -0.5em;}
sub{bottom: -0.25em;}
svg:not(:root){overflow: hidden;}
hr{box-sizing: border-box; height: 0; border-bottom: 1px solid #ccc; margin-bottom: 10px; border-top-style: none; border-right-style: none; border-left-style: none;}
pre{overflow: auto;}
pre.debug{font-size: 14px !important;}
code, kbd, pre, samp{font-family: monospace, monospace; font-size: 1em;}
button, input, optgroup, select, textarea{color: inherit; /* 1 */ font: inherit; /* 2 */ margin: 0; /* 3 */}
button{overflow: visible;}
button, select{text-transform: none;}
button, html input[type="button"], input[type="reset"], input[type="submit"]{-webkit-appearance: button; /* 2 */ cursor: pointer; /* 3 */}
button[disabled], html input[disabled]{cursor: default;}
button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner{border-style: none; padding: 0;}
input{line-height: normal; border-radius: 0; box-shadow: none;}
input[type="checkbox"], input[type="radio"]{box-sizing: border-box; /* 1 */ padding: 0; /* 2 */}
input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button{height: auto;}
[type="search"]::-webkit-search-cancel-button, [type="search"]::-webkit-search-decoration{-webkit-appearance: none;}
input[type=text], input[type=email], input[type=password], input[type=tel], input[type=search]{-webkit-appearance: none;}
input[type=number] {-moz-appearance:textfield; -webkit-appearance: textfield; -ms-appearance: textfield;}
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {-webkit-appearance: none;}
::-webkit-input-placeholder{color: inherit;}
fieldset{border: none; margin: 0; padding: 0;}
textarea{overflow: auto; resize: vertical;}
optgroup{font-weight: bold;}
table{border-collapse: collapse; border-spacing: 0;}
input:-webkit-autofill, textarea:-webkit-autofill, select:-webkit-autofill{-webkit-box-shadow: 0 0 0 30px #fff inset; box-shadow: 0 0 0 30px #fff inset;} /* set browser autocomplete bg yellow color to white */
/*------- /normalize -------*/

@import "_vars.less";
@import "_mixins.less";

/*------- fonts -------*/
@font-face {
	font-family: 'Adelle';
	src: url('fonts/adellesans-regular.woff') format('woff'),  url('fonts/adellesans-regular.woff2') format('woff2');
	font-weight: normal;
	font-style: normal;
	font-display: block;
}
@font-face {
	font-family: 'Adelle';
	src: url('fonts/adellesans-bold.woff') format('woff'),  url('fonts/adellesans-bold.woff2') format('woff2');
	font-weight: bold;
	font-style: normal;
	font-display: block;
}
@font-face {
    font-family: 'icomoon';
    src: url('fonts/icomoon.woff?lvrwwy') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}
/*------- /fonts -------*/

/*------- helpers -------*/
.display-n{display: none;}
.display-ib{display: inline-block;}
.display-b{display: block;}
.display-t{display: table;}
.display-tc{display: table-cell;}
.align-vt{vertical-align: top;}
.align-vm{vertical-align: middle;}
.align-l{text-align: left;}
.align-r{text-align: right;}
.align-c{text-align: center;}
.fz0{font-size: 0;}
.fs-i{font-style: italic;}
.fw-b{font-weight: bold;}
.fw-n{font-weight: normal;}
.float-l, .float-left{float: left;}
.float-r, .float-right{float: right;}
.pos-r{position: relative;}
.pos-a{position: absolute;}
.pos-s{position: static;}
.strong{font-weight: bold;}
.italic{font-style: italic;}
.uppercase{text-transform: uppercase;}
.first{margin-left: 0 !important;}
.last{margin-right: 0 !important;}
.image-left, .alignleft{float: left; margin: 5px 20px 10px 0px;}
.image-right, .alignright{float: right; margin: 5px 0px 10px 20px;}
.align-left{text-align: left;}
.align-right{text-align: right;}
.center{text-align: center;}
.underline{text-decoration: underline;}
.nounderline{text-decoration: none;}
.rounded{border-radius: @borderRadius;}
.df{display: flex;}
.aic{align-items: center;}
.jcc{justify-content: center;}
.fg1{flex-grow: 1;}

.red{color: @red;}
.green{color: @green;}
.light-green{color: @lightGreen;}
.light-orange{color: #E4C571;}
.orange{color: @orange;}

.shadow { content:""; position: absolute; left: 35px; right: 35px; top: 20px; bottom: 5px; box-shadow: 0px 10px 65px rgba(0,0,0,.6); }
.lloader {
	background-image: url(images/loader.svg); background-color: #fff; background-repeat: no-repeat; background-position: center center; 
	img { opacity: 0; .transition(opacity); }
	&.loaded {
		background-color: #fff; background-image: none;
		img { opacity: 1; }
	}
}
.box-shadow-yellow{box-shadow: 0 15px 30px 0 rgba(228,197,113,0.2);}
.box-shadow-gray{box-shadow: 0 10px 30px 0 rgba(0,0,0,0.1);}

.gradient-green{.gradient(#ABC075,@green);}
.gradient-green-hover{.gradient(#ABC075/1.1,@green/1.1);}

.gradient-red{.gradient(@red*1.1,@red);}
.gradient-red-hover{.gradient(@red,@red/1.1);}

.gradient-orange{.gradient(#E2926E,#F4743B);}

.tel, .tel a, a[href^=tel]{
	color: @textColor; cursor: default; text-decoration: none;
	&:hover{text-decoration: none;}
}
.list{
	list-style: none; padding: 0; margin: 0 0 15px 20px;
	@media (max-width: @t){margin-left: 0;}
	li{
		position: relative; padding: 2px 0 2px 18px;
		&:before{.pseudo(6px, 6px); background: @green; border-radius: 200px; top: 10px; left: 0;}
	}
}
.first-title{margin-top: 0; padding-top: 0;}
.wrapper{width: var(--pageWidth); max-width: 1480px; margin: auto;}
.wrapper2{
	max-width: 920px; margin: auto;
	@media (max-width: @t){width: 730px;}
	@media (max-width: @tp){width: 690px;}
	@media (max-width: @m){width: auto;}
}
.page-wrapper{
	position: relative; overflow: hidden;
	@media (max-width: @m){
		margin: auto; position: relative;
	}
}
.intro-text{font-size: 19px; line-height: 1.6;}
.extra{
	background: @darkGreen url(images/bg-green.jpg); color: #fff; padding: 40px 50px; margin: 10px 0 45px; line-height: 1.7;
	a{
		color: #fff;
		&:hover{color: #fff;}
	}
	@media (max-width: @tp){padding: 25px 35px;}
	@media (max-width: @m){padding: 15px 20px; line-height: 1.5; margin-bottom: 20px;}
}
.main-offset-sm{
	.header{padding-bottom: 100px;}
	.main{margin-top: -100px;}
	.wrapper-bottom{padding-top: 180px;}
	.bottom{margin-top: -100px;}
	@media (max-width: @t){
		.bottom{margin-top: 0;}
		.wrapper-bottom{padding-top: 65px;}
	}
	@media (max-width: @tp){
		.wrapper-bottom{padding-top: 55px;}
	}
	@media (max-width: @m){
		.wrapper-bottom{padding-top: 0;}
		.header{padding-bottom: 0;}
		.bottom, .main{margin: 0;}
	}
}
.image-wrapper{
	margin-bottom: 5px; margin-top: 5px;
	img{display: block;}
}
.image-title{text-align: center; color: @darkGreen; font-size: 14px; padding: 10px 0 0; opacity: .6;}

.toggle-icon{
	display: inline-block; width: 14px; height: 14px; position: relative; flex-grow: 0; flex-shrink: 0;
	&:after, &:before{.pseudo(100%,2px); left: 0; top: 0; background: @green;}
	&:before{width: 2px; height: 100%; left: 6px;}
	&:after{top: 6px;}
}
.circle-icon{
	&:before{.pseudo(var(--circleSize),var(--circleSize)); .icon-organic-food(); font: 22px/20px @fonti; display: flex; align-items: center; justify-content: center; color: #fff; .gradient-green; border-radius: 100px; top: 0; left: 0;}	
}
/*------- /helpers -------*/

/*------- selectors -------*/
/*
::-webkit-scrollbar { -webkit-appearance: none; width: 5px; }
::-webkit-scrollbar-thumb {
	background-color: @lightGray; border-radius: 5px;
	box-shadow: 0 0 1px rgba(255,255,255,.5);
}
*/
body{color: @textColor; background: #fff; .font(var(--fontSize), var(--lineHeight), @font);}
@media screen and (min-width: @t) {
	body{background: #fff url(images/bg.jpg);}
}
body.white-bg{background: #fff;}
a{
	color: @linkColor; text-decoration: underline; .transition();
	&:hover{text-decoration: underline; color: @linkHoverColor;}
}
ul, ol{margin: 0; padding: 0;}
h1, h2, h3, h4{
	font-weight: bold; line-height: 1.2; padding-bottom: 15px; padding-top: 20px; color: var(--green2);
	a, a:hover{text-decoration: none;}
}
h1{font-size: 44px; padding-top: 0;}
h2{font-size: 32px; line-height: 1.2;}
h3{font-size: 28px; line-height: 1.2;}
h4{font-size: 22px; line-height: 1.2;}
p{padding-bottom: 15px;}
@media screen and (max-width: @l) {
	h1{font-size: 38px;}
	h2{font-size: 36px;}
	h3{font-size: 28px;}
	h4{font-size: 22px;}
}
@media screen and (max-width: @t) {
	h1{font-size: 34px;}
}
@media (max-width: @tp){
	h1{font-size: 32px;}
	h2{font-size: 24px;}
	h3{font-size: 20px;}
	h4{font-size: 18px;}
}
@media (max-width: @m){
	h1{font-size: 24px;}
	h2{font-size: 20px;}
	h3{font-size: 16px;}
	h4{font-size: 14px;}
}
/*------- /selectors -------*/

/*------- forms -------*/
label{padding: 0 0 4px 0; display: inline-block;}
input, textarea, select, input[type="number"]{
	-webkit-appearance: none; -moz-appearance: none; background-color: transparent; border-radius: @borderRadius; padding: 0 25px; border: 1px solid @borderColor; font-size: 14px; height: 54px; font-family: @font; line-height: normal; .transition(border-color); background-color: #fff; box-shadow: none;
	@media (max-width: @m){
		padding: 0 15px; height: 47px;
	}
}
select{
	-moz-appearance: none; -o-appearance:none; -webkit-appearance: none; -ms-appearance: none; background-image: url(images/icons/arrow-down.svg); background-repeat: no-repeat; background-position: right 25px center; background-size: 12px auto; padding-right: 50px;
	padding-right: 30px; background-position: right 15px center;
}
select::-ms-expand {display: none;}
input:disabled, textarea:disabled, input:disabled+label, .disabled{cursor: not-allowed !important; color: #ccc;}
input:hover, textarea:hover, select:hover, input:focus, textarea:focus, select:focus{border-color: @borderColor/1.2; outline: 0;}
input[type=submit], button{border: none; display: inline-block;}
input[type=checkbox], input[type=radio]{padding: 0; height: auto; border: none;}
textarea{height: 130px; padding-top: 10px; padding-bottom: 10px; line-height: 19px;}
legend{
	font-size: 16px; line-height: 18px; font-weight: bold;
	a{text-decoration: none;}
}

input[type=checkbox], input[type=radio]{position: absolute; left: -9999px; display: inline;}
input[type=checkbox] + label, input[type=radio] + label{cursor: pointer; position: relative; padding: 1px 0 0 30px; line-height: 1.5; font-size: 14px; text-align: left;}

input[type=checkbox] + label:before{.pseudo(20px, 20px); background-color: #fff; border-radius: @borderRadius; text-indent: 2px; color: #fff; border: 1px solid darken(@borderColor,50%); left: 0; text-align: center; top: 0; .icon-check(); font: 8px/18px @fonti; .transition(all);}
input[type=radio] + label:before{.pseudo(20px, 20px); background-color: #fff; border-radius: 200px; color: #fff; border: 1px solid @borderColor; left: 0; text-align: center; top: 0;}

input[type=checkbox]:checked + label:before{.gradient-green; border-color: @lightGreen;}
input[type=radio]:checked + label:before{border-color: @lightGreen;}
input[type=radio]:checked + label:after{.pseudo(12px,12px); .gradient-green; border-radius: 100px; top: 4px; left: 4px; border-color: @lightGreen;}

.form-label{
	p, .field{
		position: relative; padding-bottom: 15px;
		@media (max-width: @m){padding-bottom: 10px;}
	}
	.field-newsletter, .field-accept_terms{padding-bottom: 8px;}
	label{
		position: absolute; top: 14px; left: 25px; padding: 0; cursor: text; z-index: 40; font-size: 14px; width: auto; text-align: 	left; .transition(all);
		@media (max-width: @t){top: 16px;}
		@media (max-width: @m){font-size: 14px; top: 12px;}
	}
	.focus, .ffl-floated{
		label{top: 6px; font-size: 12px; color: #898989;}
		input, select{padding-top: 13px;}
		textarea{padding-top: 25px;}
	}
	input:-webkit-autofill{padding-top: 13px;}
	input:-webkit-autofill + label{top: 6px; font-size: 12px; color: #898989;}
	input, select{display: block; width: 100%;}
	select{position: relative; z-index: 20;}
	input[type=checkbox]+label, input[type=radio]+label{position: relative; left: auto; top: auto; font-size: 14px; line-height: 20px; color: @textColor;}
	textarea{display: block; width: 100%; height: 110px;}
	input[type=radio]:disabled + label{color: #ccc;}
	@media (max-width: @m){
		label{left: 15px;}
		select{font-size: 14px;}
	}
}
/*------- /forms -------*/

/*------- tables -------*/
table{
	 border-spacing:0; border-top: 1px dotted @borderColor; border-left: 1px dotted @borderColor; font-size: 16px;
	th, td{border-right: 1px dotted @borderColor; border-bottom: 1px dotted @borderColor; padding: 10px 15px;}
}
.table{
	max-width: 100%; border-spacing: 0; margin: 10px 0px 20px; border: none; border-radius: @borderRadius; overflow: hidden; font-size: 14px;
	th{font-weight: bold; text-align: left; padding: 10px 20px; border: 0; background: @darkGreen; color: #fff;}
	td{border-right: 1px solid @borderColor; border-bottom: 0; padding: 10px 20px;}
	tbody tr:nth-child(even){background: #F3F4F5;}
}
.table-row{display: table; width: 100%;}
.table-col{display: table-cell;}
.table-wrapper {
	overflow-x: scroll; -webkit-overflow-scrolling:touch; width: 100%; position: relative; margin-bottom: 20px; overflow: auto;
	//table{min-width: 580px;}
	&:before{ .pseudo(30px,28px); background: url(images/arrow.gif) no-repeat; background-size: cover; bottom: 0; right: 4px;}
	&.ios{
		padding-bottom: 36px;
		&:before{ .pseudo(30px,28px); background: url(images/arrow.gif) no-repeat; background-size: cover; bottom: 0; right: 4px;}
	}
	.table{margin-bottom: 35px;}
}
/*------- /tables -------*/

/*------- info messages -------*/
.error{
	color: @errorColor; display: block; padding: 5px 0 0 25px; font-size: 13px; line-height: 19px;
	p{padding-bottom: 0;}
	@media (max-width: @m){padding-left: 15px; font-size: 12px;}
}
.global-error, .global-success, .global-warning{
	font-size: 14px; margin: 0 0 15px 0; line-height: 1.6; padding: 15px 15px 16px 60px; border-radius: @borderRadius; background: @errorColor; color: #fff; position: relative;
	&:before{.icon-danger(); font: 20px/20px @fonti; color: #fff; position: absolute; left: 25px; top: 17px;}
}
.global-success{
	background-color: @successColor;
	&:before{.icon-check(); font-size: 17px; line-height: 17px; top: 18px}
}
.field_error_input, .field_error_input_radio{
	border-color: @errorColor; background: #fff url(images/icons/danger.svg) no-repeat right 25px center; background-size: 20px auto; padding-right: 50px;
	@media (max-width: @m){background-position: right 15px center;}
}
/*------- /info messages -------*/

/*------- swiper -------*/
.swiper-navigation{position: absolute; top: 0; left: 0; right: 0; bottom: 0;}
.swiper-container{position: relative;}
.swiper-button{
	position: absolute; top: 30%; background: none; z-index: 40; display: flex; justify-content: center; align-items: center; padding: 0; font-size: 0; width: 50px; height: 80px; color: @textColor; .transition(opacity);
	&:before{.icon-arrow-right(); font: 65px/65px @fonti; color: #fff; .transition(all);}
	&:after{display: none;}
	&:hover{
		background: none;
		&:before{color: @green;}
		@media (max-width: @m){
			&:before{color: #fff;}
		}
	}
	@media (max-width: 1700px){
		&:before{font-size: 50px; line-height: 1;}
	}
	@media (max-width: @t){
		width: 48px; height: 48px; 
		&:after{font-size: 12px;}	
	}
}
.swiper-button-prev{
	left: -85px;
	&:before{.scaleX(-1);}
	@media (max-width: @l){left: -65px;}
	@media (max-width: @t){left: 20px;}
	@media (max-width: @tp){left: 10px;}
	@media (max-width: @m){left: 0;}
}
.swiper-button-next{
	right: -85px;
	@media (max-width: @l){right: -65px;}
	@media (max-width: @t){right: 20px;}
	@media (max-width: @tp){right: 10px;}
	@media (max-width: @m){right: 0;}
}
.slick-arrow2{
	.swiper-button{
		background: #fff; border-radius: @borderRadius; top: calc(~"50% - 27px"); width: 55px; height: 55px; box-shadow: 0px 0px 20px rgba(0,0,0,.2); .transition(all);
		&:before{font-size: 20px; line-height: 10px; color: @textColor;}
		&:hover{
			opacity: 1; background: @green;
			&:before{color: #fff;}
		}
		@media (max-width: @t){width: 45px; height: 45px;}
		@media (max-width: @tp){
			width: 38px; height: 38px;
			&:before{font-size: 15px;}
		}
	}
	.swiper-button-prev{left: 10px;}
	.swiper-button-next{right: 10px;}
}
.slick-arrow3{
	.swiper-button{
		&:before{font-size: 35px; color: @textColor;}
		&:hover:before{color: @green;}
	}
}
.swiper-button-disabled{opacity: .5; cursor: default;}
.swiper-navigation-lock{display: none;}
/*------- /swiper -------*/

/*------- slick -------*/
.slick-slider{position: relative; display: block; box-sizing: border-box; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; -ms-touch-action: pan-y; touch-action: pan-y; -webkit-tap-highlight-color: transparent; }
.slick-list{
	position: relative; overflow: hidden; display: block;
	&:focus{outline: none; }
	&.dragging{cursor: pointer; cursor: hand; }
}
.slick-slider .slick-track, .slick-slider .slick-list{-webkit-transform: translate3d(0, 0, 0); -moz-transform: translate3d(0, 0, 0); -ms-transform: translate3d(0, 0, 0); -o-transform: translate3d(0, 0, 0); transform: translate3d(0, 0, 0);}
.slick-track{
	position: relative; left: 0; top: 0; display: block; margin-left: auto; margin-right: auto;
	&:before, &:after {content: ""; display: table; }
	&:after {clear: both; }
	.slick-loading & {visibility: hidden; }
}
.slick-slide {
	float: left; height: 100%; min-height: 1px; display: none;
	/*&>div{width: calc(~"100% + 1px");}*/
	img {display: block; }
	&.slick-loading img {display: none; }
	&.dragging img {pointer-events: none; }
	.slick-initialized & {display: block; }
	.slick-loading & {visibility: hidden; }
	.slick-vertical & {display: block; height: auto; }
}
.slick-arrow{
	position: absolute; top: 30%; background: none; z-index: 40; display: flex; justify-content: center; align-items: center; padding: 0; font-size: 0; width: 50px; height: 80px; color: @textColor; .transition(opacity);
	&:before{.icon-arrow-right(); font: 65px/65px @fonti; color: #fff; .transition(all);}
	&:after{display: none;}
	&:hover{
		background: none;
		&:before{color: @green;}
		@media (max-width: @m){
			&:before{color: #fff;}
		}
	}
	&.slick-disabled{
		cursor: default; opacity: 0;
		&:hover{
			&:before{color: @textColor;}
		}
	}
	@media (max-width: @l){
		&:before{font-size: 45px; line-height: 45px;}
	}
	@media (max-width: @t){
		&:before{font-size: 30px;}
	}
	@media (max-width: @tp){
		&:before{font-size: 25px;}
	}
}
.slick-prev{
	left: -85px;
	&:before{.scaleX(-1);}
	@media (max-width: @l){left: -65px;}
	@media (max-width: @t){left: 20px;}
	@media (max-width: @tp){left: 10px;}
	@media (max-width: @m){left: 0;}
}
.slick-next{
	right: -85px;
	@media (max-width: @l){right: -65px;}
	@media (max-width: @t){right: 20px;}
	@media (max-width: @tp){right: 10px;}
	@media (max-width: @m){right: 0;}
}
.slick-arrow2{
	.slick-arrow{
		background: #fff; border-radius: @borderRadius; top: calc(~"50% - 27px"); width: 55px; height: 55px; box-shadow: 0px 0px 20px rgba(0,0,0,.2); .transition(all);
		&:before{font-size: 20px; line-height: 10px; color: @textColor;}
		&:hover{
			opacity: 1; background: @green;
			&:before{color: #fff;}
		}
		@media (max-width: @t){width: 45px; height: 45px;}
		@media (max-width: @tp){
			width: 38px; height: 38px;
			&:before{font-size: 15px;}
		}
	}
	.slick-prev{left: 10px;}
	.slick-next{right: 10px;}
}
.slick-arrow3{
	.slick-arrow{
		&:before{font-size: 35px; color: @textColor;}
		&:hover:before{color: @green;}
	}
}
.slick-arrow.slick-hidden {display: none;}
.slick-dots{
	list-style: none; padding: 0 15px; display: flex; align-items: center; text-align: center; position: relative; z-index: 10;
	li{
		display: flex; justify-content: center; align-items: center; vertical-align: top; position: relative; width: 21px; height: 21px; padding: 0!important;
		&:before{display: none!important;}
	}
	button{background:#fff; border: 0; border-radius: 100px; padding: 0; min-width: 0; width: 12px; height: 12px; display: block; text-indent: -99999999px;
		&:hover{background: #fff;}
	}
	.slick-active, .glide__bullet--active{
		button{.gradient-green;}
	}
}
.slick-carousel{
	.slick-prev{left: -55px;}
	.slick-next{right: -55px;}
	.slick-list{
		padding: 0 0 110px 1px; display: flex; margin-left: -1px; width: calc(~"100% - -1px");
		@media (max-width: @t){padding-bottom: 50px; width: calc(~"100% - -5px");}
	}
	.slick-track{display: flex;}
	&.slick-initialized .slick-slide{display: flex;}
	.cp{
		.transition(border-color); margin-top: 0;
		&:hover{border-color: @borderColor/1.1;}
	}
	.cp-addtocart{
		box-shadow: none; border: 1px solid @borderColor/1.1;
		@media (max-width: @t){border: 0;}
	}
}

.glide{position: relative;width: 100%;box-sizing: border-box;}
.glide * {box-sizing: inherit;}
.glide__track{overflow: hidden;}
.glide__slides{position: relative;width: 100%;list-style: none;backface-visibility: hidden;transform-style: preserve-3d;touch-action: pan-Y;overflow: hidden;padding: 0;white-space: nowrap;display: flex;flex-wrap: nowrap;will-change: transform;}
.glide__slides--dragging{user-select: none;}
.glide__slide{width: 100%;height: 100%;flex-shrink: 0;white-space: normal;user-select: none;-webkit-touch-callout: none;-webkit-tap-highlight-color: transparent;}
.glide__slide a{user-select: none;-webkit-user-drag: none;-moz-user-select: none;-ms-user-select: none;}
.glide__arrows{-webkit-touch-callout: none; user-select: none;}
.glide__bullets{-webkit-touch-callout: none; user-select: none;}
.glide--rtl{direction: rtl;}
/*------- /slick -------*/

/*------- buttons -------*/
.btn, input[type=submit], button{
	position: relative; display: inline-flex; align-items: center; justify-content: center; padding: 0 40px; height: 54px; font-size: 16px; border-radius: @borderRadius; color: #fff; background: @blue; text-decoration: none; .gradient-green; position: relative; overflow: hidden;
	
	&:hover{
		color: #fff; text-decoration: none;
		&:after{opacity: 1;}
	}
	span{position: relative; z-index: 1;}
	&:after{.pseudo(100%,100%); .gradient-green-hover; opacity: 0; .transition(opacity); top: 0; left: 0;}
	&.flat{
		background: @green; .transition(all);
		&:after{display: none;}
	}
	&.loading{
		color: transparent!important; pointer-events: none; font-size: 0; line-height: 0;
		&>span{opacity: 0; display: none;}
		&:after{display: none;}
	}

	@media (max-width: @t){height: 47px; font-size: 14px;}
}
.btn-light-green.flat{
	background: @lightGreen;
	&:hover{background: @lightGreen/1.2;}
}
.btn-orange, .btn-yellow{
	.gradient(#E2926E,@orange);
	&:after{.gradient(#E2926E/1.1,@orange/1.1);}
}
.btn-white{
	border: 1px solid @borderColor; background: #fff; color: @darkGreen;
	&:after{display: none;}
	&:hover{color: @lightGreen;}
}
.btn-gray{
	background: #E9ECEB; color: @textColor; padding: 0 30px; font-size: 14px; .transition(all);
	&:hover{background: #E9ECEB/1.1; color: @textColor;}
	&:after{display: none;}
}
/*------- /buttons -------*/

/*------- header -------*/
.header{
	font-size: 14px; padding-bottom: 320px; background-repeat: no-repeat; background-image: url(images/bg-dark.jpg); background-size: 120% auto; position: relative; background-position: center top;
	&>a{color: #fff;}
	@media (max-width: @l){font-size: 13px;}
	@media (max-width: @t){padding-bottom: 0; background-image: url(images/bg-dark-t.jpg); background-size: cover; font-size: 12px;}
}
.header-spacing0 .header{padding-bottom: 0;}
.page-homepage .header{
	background-size: cover; padding-bottom: 1px; background-image: url(images/bg-dark.jpg);
	@media (max-width: @l){background-image: url(images/bg-dark.jpg);}
	@media (max-width: @t){background-image: url(images/bg-dark-t.jpg);}
	@media (max-width: @m){background: none;}
}
.logo{
	display: block; width: 95px; height: 110px; background: url(images/logo.svg?v5) no-repeat center center; background-size: contain; grid-column: ~"1/2"; grid-row: ~"1/span 2"; margin-top: 10px;
	@media (min-width: @m){
		&:lang(sl){background: url(images/logo-sl.svg) no-repeat center center; background-size: auto 94%;}
		&:lang(en){background: url(images/logo-en.svg) no-repeat center center; background-size: auto 94%;}
		&:lang(de){background: url(images/logo-de.svg) no-repeat center center; background-size: contain;}
	}
	&:hover{text-decoration: none;}
	@media (max-width: @t){width: 60px; height: 80px; margin-top: 21px;}
	@media (max-width: @tp){height: 75px; margin-top: 15px;}
	@media (max-width: @m){
		width: 36px; height: 28px; display: flex; align-items: center; justify-content: center; background: none; text-decoration: none; position: absolute; top: 10px; left: 15px; margin: 0;
		&:before{.icon-symbol(); width: 30px; height: 25px; font: 23px/23px @fonti; color: @lightGreen;}
	}
}
.header-contact{
	grid-column: ~"2 / 3";
	p{padding: 0;}
	a[href^=tel]{color: #E4C571; text-decoration: none;}
	@media (max-width: @t){display: none;}
}
.wrapper-header{
	display: grid; column-gap: 30px; grid-row-gap: 10px; grid-template-columns: 85px 235px auto 200px 45px 45px; grid-template-rows: 50px 50px; color: #fff; padding: 10px 10px 30px; text-align: center;
	&>*{align-self: center;}
	@media (max-width: @l){column-gap:15px; grid-template-columns: 85px 235px auto 200px 55px 55px;}
	@media (max-width: @t){padding-bottom: 15px; grid-template-rows: 30px 50px; padding-left: 30px; padding-right: 30px; grid-template-columns: 60px 205px auto 160px 55px 55px;}
	@media (max-width: @tp){grid-template-columns: 55px 200px auto 45px 45px 45px; grid-gap: 10px; padding-left: 20px; padding-right: 20px; grid-template-rows: 25px 50px; grid-row-gap: 7px;}
	@media (max-width: @m){padding-left: 15px; padding-right: 15px; display: block; padding: 0; height: 50px; background: url(images/header-t.jpg); background-size: cover;}
}
.header-categories-main{grid-column: ~"2/3"; grid-row: 2;}
.header-categories{
	display: flex; width: 100%; overflow: visible; border: 1px solid #E2926E; height: 50px; padding: 0; justify-content: flex-start; justify-content: center;
	&>span{display: flex; align-items: center; padding-left: 35px;}
	span{
		span, span:after, span:before { .pseudo(18px,2px); background: #fff; left: 0; top: 0; border-radius: 3px; .transition(all); }
		span {
			top: 12px; left: 0;
			&:before {top: -5px;}
			&:after {top: 5px;}
		}
	}
	&.active {
		span {background: transparent;}
		span:after {.rotate(45deg); top: 0;}
		span:before {.rotate(-45deg); top: 0;}
		&:after{.pseudo(10px,10px); background: @orange; opacity: 1; .rotate(45deg); top: auto; bottom: -6px; left: 30px;}
		@media (max-width: @t){
			&:after{left: 27px;}
		}
		@media (max-width: @tp){
			&:after{left: 24px;}
		}
	}
	@media (max-width: @t){
		font-size: 14px;
		&>span{padding-left: 29px;}
		span span{top: 10px}
	}
	@media (max-width: @m){
		display: none;
	}
}
.quick-order{
	grid-column: ~"4/5"; grid-row: 2; border: 1px solid #ABC075; height: 50px; position: relative; padding: 0; z-index: 10;
	&>span{
		display: flex; align-items: center;
		&:before{.icon-quickorder(); font: 21px/21px @fonti; left: 0; top: 0; margin-right: 15px;}
	}
	@media (max-width: @t){
		font-size: 14px;
		&>span:before{font-size: 19px; line-height: 19px; margin-right: 12px;}
	}
	@media (max-width: @tp){
		font-size: 0; background: none; border: 0; grid-column: 4; margin-right: -6px;
		&>span:before{margin: 0;}
		&:after{display: none;}
	}
	@media (max-width: @m){
		display: none;
		&:hover{color: @textColor;}
	}
}

.fixed-header{
	.header-body{position: fixed; top: -100px; left: 0; right: 0; z-index: 600; background: #3b3d41 url(images/header.jpg); background-size: cover; box-shadow: 0 15px 40px 0 rgba(0,0,0,0.5); .translate3d(0,100px); .transition(transform);}
	.wrapper-header{
		display: flex; padding: 0; align-items: center; height: 70px;
		@media (max-width: @t){padding: 0 30px;}
		@media (max-width: @m){height: 50px; padding: 0;}
	}
	@media (min-width: @tp){
		&.admin_toolbar .header-body{ .translate3d(0,140px);}
	}
	.header-placeholder{
		height: 150px;
		@media (max-width: @t){height: 115px;}
		@media (max-width: @m){height: 50px;}
	}
	.header-contact, .nav, .currency, .w-lang{display: none;}
	.header-categories{
		min-width: 235px; margin-right: 30px;
		@media (max-width: @t){width: 200px; margin-right: 20px;}
	}
	.logo{
		background: none; display: flex; height: 100%; align-items: center; justify-content: center; text-decoration: none; grid-column: 1; margin: 0 38px 0 10px;
		&:before{.icon-symbol(); color: @lightGreen; font: 28px/28px @fonti;}
		@media (max-width: @t){margin-right: 15px; margin-left: 0;}
	}
	.sw{
		flex-grow: 1; max-width: 700px; margin-right: auto;
		@media (max-width: @l){margin-right: 20px;}
		@media (max-width: @t){max-width: none;}
	}
	.wishlist{margin-right: 20px; margin-left: 2px;}
	.wishlist-counter, .ww-counter{top: 15px;}
	.aw{
		height: 100%;
		span{
			&:after{display: none;}
			&:before{margin: 0; color: #fff; .transition(color);}
		}
	}
	.aw-tooltip{
		top: 65px; right: -78px;
		&:before{margin-left: -5px;}
		@media (max-width: @tp){
			right: -65px; top: 70px;
			&:before{color: #fff;}
		}
	}
	.aw-login{
		font-size: 0; width: 55px; height: 100%; display: flex; align-items: center; justify-content: center;
		&:hover{
			span:before{color: @lightGreen;}
		}
	}
	.quick-order{
		font-size: 0; width: 55px; border: none; background: none; overflow: visible;
		&:after{display: none;}
		span{
			&:before{margin: 0; .transition(color);}
		}
		&:hover{
			span:before{color: @lightGreen;}
			.btn-label{display: block;}
		}
	}
	.btn-label{
		font-size: 13px; padding: 6px 0; border-radius: @borderRadius; background: #fff; box-shadow: 0 5px 15px 0 rgba(0,0,0,0.4); position: absolute; color: @textColor; top: 40px; width: 140px; transform: translateX(-45%); display: none;
		&:before{.pseudo(8px,8px); background: #fff; .rotate(45deg); left: 50%; margin-left: -4px; top: -4px;}
		@media (max-width: @tp){display: none!important;}
	}
	.categories-container{
		top: 75px; left: 124px;
		@media (max-width: @t){left: 100px; top: 70px;}
	}

	@media (max-width: @tp){
		.wrapper-header{padding: 0 20px;}
		.logo{
			width: 35px;
			&:before{font-size: 23px; line-height: 23px;}
		}
		.header-categories{margin-right: 10px; min-width: 200px;}
		.ww, .wishlist, .aw{width: 40px; margin: 0; flex-grow: 0; flex-shrink: 0;}
		.aw-login{width: 100%;}
		.sw{margin-right: 17px;}
		.quick-order{margin: 0; width: 42px;}
		.wishlist-counter, .ww-counter{width: 20px; height: 20px; font-size: 11px;}
		.categories-container{left: 70px;}
	}

	@media (max-width: @m){
		.wrapper-header{padding: 0;}
		.logo{
			width: 36px; height: 28px; margin: 0;
			&:before{font-size: 23px; line-height: 23px;}
		}
		.header-body{background: url(images/header-t.jpg); background-size: cover;}
		.sw{margin: 0;}
	}
}
.loyalty-header{
	overflow: hidden; max-height: 0; padding-left: 20px; padding-right: 20px; .transition(all);
	img{display: block; margin: auto; box-shadow: 3px 3px 5px rgba(0,0,0,0.17); border-radius: @borderRadius;}
	&.active{display: block; max-height: 500px; padding-top: 20px; padding-bottom: 20px;}
}
.btn-loyalty-header{
	display: block; padding: 7px 15px 7px 42px; border-bottom: 1px solid rgba(255,255,255,.3); font-size: 13px; background: #000; color: #fff;
	.h{display: none;}
	&:hover{color: #fff;}
	&.active{
		.h{display: inline;}
		.s{display: none;}
		&:after{.icon-cross(); color: #fff; font-size: 12px; line-height: 12px; top: 11px;}
	}
	&:before{.icon-barcode(); font: 18px/18px @fonti; position: absolute; left: 15px; top: 8px; color: #fff;}
	&:after{.icon-arrow-down(); display: block; font: 8px/8px @fonti; color: @green; position: absolute; right: 15px; top: 13px;}
}

//Hellobar
body.hello-bar-active{
	&.page-catalog {
		.header{z-index: unset;}
	}
}
.hello-bar{
	position: relative;display: flex; justify-content: center; align-items: center; width: 100%; height: 96px; background-size: cover; background-position: left center; background-repeat: no-repeat;
	.wrapper{display: flex; justify-content: flex-end; align-items: center; position: relative; margin: 0 auto;}
	&.active{
		.hellobar-coupon-btn{
			padding: 0 62px 0 24px; color: @white;
			&:before{left: auto; right: 4px; color: #2B3F21; font-size: 12px; line-height: 12px; background: @white; .icon-check2(); .rotate(0); z-index: 1;}
			&:after{.pseudo(100%,100%); background: linear-gradient(180deg, #ABC075 0%, #809941 100%); opacity: 1; .transition(opacity); top: 0; left: 0;}
			.i{display: none;}
			.a{display: block;}
		}
		.activated-coupon-note{display: block;}
		.heb-right-timer{display: none!important;}
	}
	@media (max-width: @t){
		.wrapper{padding: 0 30px;}
		&.active{
			.hellobar-coupon-btn{padding: 0 55px 0 20px;}
		}
	}
	@media (max-width: @tp){
		height: auto; padding: 12px 0 16px;
		.wrapper{padding: 0 75px; flex-flow: column; align-items: center; justify-content: center; text-align: center;}
		&.active{
			.hellobar-coupon-btn{
				padding: 0 47px 0 21px; color: @white;
				&:before{font-size: 9px; line-height: 9px;}
			}
		}
	}
	&.hello-bar-none{display: none!important;}
}

.hello-bar-clear{
	justify-content: center; align-items: center; min-height: 85px;
	.wrapper{justify-content: center; align-items: center; text-align: center; position: relative;}
	.heb-content{justify-content: center; align-items: center; text-align: center; padding: 0 279px!important;}
	.heb-image{top: 4px;}
	padding-left: 0;
	@media (max-width: @l){
		.heb-content{padding: 0 200px!important;}
	}
	@media (max-width: @t){
		.heb-content{padding: 0 140px!important;}
	}
	@media (max-width: @tp){
		.heb-image{left: -15px; top: 0;}
		.heb-content{padding: 0!important;}
	}
}
.heb-image{
	position: absolute; display: flex; width: auto; height: 100%; max-height: 100%; max-width: 205px; align-items: center; justify-content: center; margin-right: 65px; top: 4px; left: 0;
	img{display: block; width: auto; height: auto;}
	@media (max-width: @l){max-width: 175px;}
	@media (max-width: @t){max-width: 135px; left: 30px;}
	@media (max-width: @tp){max-width: 80px; margin-right: 0; position: absolute; left: 5px; top: 0; height: initial;}
}

.heb-content{
	display: flex; flex-flow: column; justify-content: center; color: @white; height: 100%; position: relative; left: auto; right: auto; flex-grow: 1; flex-shrink: 1; padding-left: 279px;
	a{color: @white; text-decoration: none;}
	&.heb-content-noimage{padding-left: 0;}
	@media (max-width: @l){padding-left: 205px;}
	@media (max-width: @t){padding-left: 150px;}
	@media (max-width: @tp){height: auto; flex: unset; order: 2; padding-left: 0;}
} 
.heb-title{
	font-size: 24px; line-height: 28px; padding-bottom: 2px;
	a{.transition(opacity);}
	@media (max-width: @l){font-size: 22px;}
	@media (max-width: @t){font-size: 17px; line-height: 20px;}
	@media (max-width: @tp){font-size: 14px; line-height: 19px; padding-bottom: 3px;}
}
.heb-subtitle{
	font-size: 16px; line-height: 28px;
	@media (max-width: @l){font-size: 14px;}
	@media (max-width: @t){font-size: 13px; line-height: 20px;}
	@media (max-width: @tp){font-size: 11px; line-height: 16px;}
}

.heb-right-container{
	display: flex; align-items: center; flex-shrink: 0; position: relative;
	@media (max-width: @tp){order: 3; justify-content: center; margin-top: 12px; flex-flow: column;}
}
.activated-coupon-note{
	display: none; font-size: 14px; line-height: 1.5; color: @white; margin-right: 30px; padding-left: 10px;
	@media (max-width: @t){margin-right: 15px; font-size: 13px;}
	@media (max-width: @tp){margin-right: 0; font-size: 12px; margin-bottom: 5px; padding-left: 0;}
}
.hellobar-coupon-message{
	position: absolute; opacity: 0; visibility: hidden; top: 100%; background: white; z-index: 51; padding: 5px 10px; font-size: 12px; line-height: 1.2; border-radius: 4px; right: 0; white-space: nowrap; margin-top: 10px; transition: opacity .3s, visibility .3s; box-shadow: 0 5px 20px 0 rgba(43, 63, 33, 0.3);
	p{padding-bottom: 0;}
	&:before{.pseudo(8px,8px); background: @white; .rotate(45deg); right: 20px; top: -3px;}
	&.active{opacity: 1; visibility: visible;}
	@media (max-width: @tp){
		right: unset; left: 50%; transform: translateX(-50%);
		&:before{right: unset; left: 50%; margin-left: -3px;}
	}
}
.hellobar-coupon-btn{
	font-weight: bold; background: @white; flex-grow: 0; flex-shrink: 0; text-transform: unset; font-size: 12px; line-height: 16px; color: #244538; text-transform: uppercase; position: relative; top: auto; right: auto; width: auto; height: 48px; padding: 0 32px 0 66px; display: flex; align-items: center; justify-content: center; border-radius: 24px;
	&:before{.pseudo(40px,40px); background: linear-gradient(180deg, #ABC075 0%, #809941 100%); top: 4px; left: 4px; border-radius: 50%; display: flex; align-items: center; justify-content: center; .icon-arrowR(); font: 15px/15px @fonti; text-indent: 1px; color: @white; box-shadow: 0 5px 20px 0 rgba(43, 63, 33, 0.3);}
	.a{display: none;}
	&:hover{
		background: @white; color: #244538;
		&:after{display: none; color: #244538;}
	}
	@media (max-width: @t){padding: 0 20px 0 55px;}
	@media (max-width: @tp){
		padding: 0 28px 0 51px; font-size: 11px; height: 40px;
		&:before{width: 32px; height: 32px; font-size: 12px; line-height: 12px;}
	}
}
.heb-right-timer{
	flex-shrink: 0;  margin-left: 56px;
	@media (max-width: @l){margin-left: 30px;}
	@media (max-width: @t){margin-left: 15px;}
	@media (max-width: @tp){order: 1; margin-left: 0;}
}
.heb-right{
	display: block; position: relative; font-size: 18px; line-height: 28px; color: @white; font-weight: normal; display: flex; align-items: center; justify-content: flex-start; height: 100%; flex-grow: 0; flex-shrink: 0;
	&>div{
		margin-right: 16px;
		&:last-child{margin-right: 0;}
		&.sec{
			span{width: 50px; display: inline-block;}
		}
	}
	span{font-size: 40px; line-height: 40px; font-weight: bold; margin-right: 5px;}
	@media (max-width: @l){
		&>div{
			margin-right: 14px;
			&.sec{
				span{width: 40px;}
			}
		}
		span{font-size: 36px; line-height: 36px;}
	}
	@media (max-width: @t){
		font-size: 14px; 
		&>div{
			margin-right: 10px;
			&.sec{
				span{width: 35px;}
			}
		}
		span{font-size: 32px; line-height: 32px; margin-right: 3px;}
	}
	@media (max-width: @tp){
		font-size: 14px; line-height: 28px;
		&>div{
			margin-right: 8px;
			&.sec{
				span{width: 30px;}
			}
		}
		span{font-size: 24px; line-height: 40px; margin-right: 2px;}
	}
}
/* .heb-close{
	position: absolute; right: 33px; top: 36px; width: 24px; height: 24px; cursor: pointer; flex-grow: 0; flex-shrink: 0;
	&:after{.pseudo(100%,100%); .icon-close2(); font: 24px/24px @fonti; color: @white; top: 0; left: 0; .transition(color);}
	@media (max-width: @l){
		right: 15px; width: 20px; height: 20px;
		&:after{font-size: 20px; line-height: 20px;}
	}
	@media (max-width: @t){
		right: 10px; top: 10px; width: 16px; height: 16px;
		&:after{font-size: 16px; line-height: 16px;}
	}
	@media (max-width: @tp){right: 12px; top: 16px;}
} */
/*------- /header -------*/

/*------- wishlist -------*/
.wishlist{
	grid-row: 2; grid-column: ~"5/6"; width: 55px; height: 100%; margin-left: -6px;
	a{
		height: 100%; width: 100%; display: flex; align-items: center; justify-content: center; text-decoration: none; color: #fff; position: relative;
		&:before{.icon-heart(); font: 22px/22px @fonti; color: #fff; .transition(all);}
		@media (max-width: @t){
			&:before{font-size: 19px; line-height: 19px;}
		}
		&:hover:before{color: @lightGreen;}
	}
	&.active .wishlist-counter{display: flex;}
	@media (max-width: @t){margin-left: -4px;}
	@media (max-width: @tp){width: 100%; margin-left: 0;}
	@media (max-width: @m){width: 40px; right: 100px; position: absolute; top: 0;}
}
.wishlist-counter, .ww-counter{
	background: @green; border-radius: 100px; width: 22px; height: 22px; display: none; align-items: center; justify-content: center; color: #fff; position: absolute; top: 3px; right: 0; font-size: 12px; line-height: 14px;
	@media (max-width: @m){width: 20px; height: 20px; font-size: 11px; top: 6px!important;}
}
/*------- /wishlist -------*/

/*------- currency -------*/
.w-lang{
	grid-column: ~"4/5"; grid-row: 1; position: relative; z-index: 10; font-size: 12px; display: flex; justify-content: center;
	&.active{
		.w-list{display: block;}
	}
	@media (max-width: @l){padding-left: 90px;}
	@media (max-width: @t){padding-left: 0;}
	@media (max-width: @tp){grid-column: ~"4/7";}
	@media (max-width: @m){height: 44px; position: static; background: #F9FAF9; border-bottom: 1px solid @borderColor; padding: 0 19px; display: flex; justify-content: flex-start; display: none;}
}
.btn-w-toggle{
	text-transform: uppercase; font-weight: bold; position: relative; display: flex; align-items: center;
	&:after{.pseudo(1px,auto); right: -8px; top: 4px; bottom: 4px; background: #abc075; opacity: .5;}
	.flag{margin-top: 0;}
	@media (max-width: @m){
		line-height: 15px;
		&:after{right: -6px; top: 4px; bottom: 4px;}
		.flag{margin-top: 0;}
	}
}
.w-lang-mobile-close{
	display: none !important;
	@media (max-width: @m){
		display: flex !important; width: 22px !important; height: 22px !important; right: 6px !important; top: 6px !important;
	}
}
.flag{
	margin-right: 7px; display: block; background: url(images/croatia.svg) top left no-repeat; width: 23px; height: 17px; background-size: contain;
	&.en{background: url(images/eu.svg) top left no-repeat; background-size: contain;}
	&.si{background: url(images/slovenia.svg) top left no-repeat; background-size: contain;}
	&.de{background: url(images/germany.svg) top left no-repeat; background-size: contain;}
	&.at{background: url(images/austria.svg) top left no-repeat; background-size: contain;}
}
.w-lang-span{
	display: flex; column-gap: 15px; cursor: pointer;
	@media (max-width: @m){column-gap: 11px; align-items: center;}
}
.bold2{font-weight: bold; }
.delivery-to{
	display: flex; flex-flow: column; line-height: 18px; text-align: left; align-items: flex-start;
	.bold2{
		font-weight: bold; position: relative; padding-right: 12px;
		&:after{.icon-arrow-down(); font: 6px/6px @fonti; position: absolute; top: 6px; right: 0; color: @lightGreen;}
	}
	@media (max-width: @t){line-height: 15px; white-space: nowrap; font-size: 11px;}
	@media (max-width: @m){
		display: block; white-space: unset; font-size: 12px; line-height: 15px;
		.bold2{
			&:after{color: @green; top: 7px;}
		}
	}
}
.w-list-lang{
	display: flex; align-items: center; column-gap: 18px; padding: 18px 20px 14px; text-transform: uppercase; justify-content: center; text-align: center;
	a{
		width: 23px; display: flex; flex-flow: column; row-gap: 3px;
		&.active{display: none;}
		.flag{margin: 0;}
	}
	@media(max-width: @m){column-gap: 22px;}
}

//modal country select
.body-country-select{
	overflow: hidden; position: relative;
	//&:before{.pseudo(auto,auto); top: 0; left: 0; right: 0; bottom: 0; background: rgba(28,34,25,0.75); z-index: 999999;}
	&.hidden-close-btn{
		.shipping-country-close{display: none;}
	}
	.country-select-modal{opacity: 1; visibility: visible; z-index: 9999999999;}
	@media (max-width: @m){
		&:before{position: fixed; }
		&.hidden-close-btn,&.page-webshop-shipping{
			.country-select-modal{top: 15px; bottom: 15px; transform: unset; padding-bottom: 10px; overflow: auto; border-bottom: 114px solid white; border-radius: 0;}
			.country-cart-cnt{row-gap: 10px; right: 15px; left: 15px; box-shadow: 0px 0px 40px rgba(0, 0, 0, 0.3); position: fixed; bottom: 15px; padding: 18px 20px; background: white; margin-top: 0;}
		}
	}
}
.country-select-modal{
	position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 99999999;  opacity: 0; visibility: hidden; display: flex; align-items: center; justify-content: center; align-self: inherit!important;
	&:before{.pseudo(auto,auto); top: 0; left: 0; bottom: 0; right: 0; background: rgba(28,34,25,0.75);}
	.ww-cart-items-inner{
		max-height: 200px; overflow: auto;
		@media(max-width: @m){max-height: unset; overflow: unset;}
	}
	@media(max-width: @m){
		left: 15px; right: 15px; transform: translate(0,-50%); width: auto; padding: 20px; border-radius: 0;
		.ww-cart-items{
			padding-top: 18px;
			.wp-pickup-products{margin-top: 24px; margin-bottom: 8px; margin-left: 0; margin-right: 0; padding: 12px 15px;}
			.wp{padding-left: 0; padding-right: 0; margin-bottom: 0; padding: 7px 0;}
			.wp-total{position: static;}
			.wp-image{width: 50px;}
			.wp-badge{margin-right: -75px;}
		}
	}
}
.country-select-modal-inner{
	position: relative; border-radius: @borderRadius; padding: 40px 50px; width: 580px; background: #fff;
}
.csm-title{
	font-size: 16px; line-height: 20px; font-weight: bold; padding-bottom: 7px; color: @textColor;
	@media(max-width: @m){padding-bottom: 10px;}
}	
.page-webshop-shipping{
	.country-select-modal{
		.csm-title,.shipping-country-close{display: none;}
		.ww-cart-items{padding: 0; margin: 0; border: unset; max-height: unset;}
	}
}
.shipping-country-close{
	@media(max-width: @m){
		width: 22px !important; height: 22px !important; top: 10px !important; right: 10px !important; border: unset !important;
		&:before{font-size: 9px !important; line-height: 9px !important;}
	}
}
.shipping-country-select-cnt{
	position: relative; 
	&:before{.pseudo(auto,auto); .icon-pickup(); font: 24px/24px @fonti; color: @lightGreen; top: 15px; left: 20px; pointer-events: none; z-index: 10;}
	@media(max-width: @m){
		&:before{font: 21px/21px @fonti; top: 13px; left: 15px;}
	}
}
.shipping-country-select-note{
	font-size: 12px; line-height: 16px; padding-top: 10px; text-align: center; color: @gray2;
	@media(max-width: @m){padding-left: 15px; padding-right: 15px; text-align: left;}
}
.shipping-country-select{
	width: 100%; display: block; padding: 0 30px 0 50px; color: @textColor;
	@media(max-width: @m){padding: 0 30px 0 41px;}
}
.shipping-country-submit{
	width: 100%; margin-top: 25px; font-weight: bold;
	@media(max-width: @m){margin-top: 15px;}
}

.ww-cart-items{
	padding-top: 30px;
	.wp-pickup-products{padding: 15px 22px; margin-top: 30px; font-weight: normal; margin-bottom: 15px;}
	.wp-pickup-label{
		p{padding-bottom: 0;}
	}
	.wp{border: unset; padding-bottom: 15px; margin-bottom: 0;}
	.wp-image{width: 70px;}
	.wp-title{font-size: 12px; padding-bottom: 3px;}
	.wp-total{width: 60px;}
	.wp-cnt{text-align: left;}
}
.country-cart-cnt{
	display: flex; flex-flow: column; row-gap: 20px; align-items: center; margin-top: 15px;
	.btn{width: 100%; font-weight: bold; cursor: pointer;}
	.btn-autochange{
		font-size: 14px; color: @darkGreen; text-underline-offset: 2px; cursor: pointer;
		@media (min-width: @t){
			&:hover{color: @green;}
		}
	}
	@media(max-width: @m){margin-top: 0;}
}

.w-delivery-change-cnt{display: flex; flex-flow: column; justify-content: center; border-top: 1px solid @borderColor; padding: 18px 20px; color: @textColor; text-align: center;}
.w-delivery-change{
	display: block; text-decoration: underline !important; color: @green !important; cursor: pointer;
	@media (min-width: @t){
		&:hover{color: @darkGreen;}
	}
}
.w-toggle{
	width: 60px; position: relative;
	&>a{
		color: #fff; text-decoration: none;
		span{
			position: relative; padding-right: 15px;
			&:after{.icon-arrow-down(); font: 6px/6px @fonti; position: absolute; top: 5px; right: 0; color: @lightGreen;}
		}
		@media (max-width: @m){display: flex; align-items: center; height: 100%; width: 100%;}
	}
	&.active .w-list{display: block;}
	@media (max-width: @m){height: 100%; display: flex; align-items: center;} 
}
.w-list{
	background: #fff; width: 192px; border-radius: @borderRadius; position: absolute; top: 45px; left: 0; box-shadow: 0 15px 50px 0 rgba(0,0,0,0.75); display: none;
	&:before{.pseudo(10px,10px); background: #fff; .rotate(45deg); left: 50%; top: -3px; margin-left: -5px;}
	a{text-decoration: none;}
	@media (max-width: @m){
		box-shadow: 0 5px 30px 0 rgba(0,0,0,0.2); padding: 0; top: 43px; left: 15px; border-radius: unset; width: 274px; z-index: 9999;
		&:before{left: 51px; margin-left: 0;}
	}
}
@media (max-width: @m){
	.lvl2 .w-lang{display: none;}
}
/*------- /currency -------*/
/*------- navigation -------*/
.nav{
	list-style: none; display: flex; grid-column: ~"3/4"; justify-content: space-around; align-items: center; position: relative; z-index: 1000;
	li{
		position: relative;
		&:hover>a{color: @lightGreen;}
	}
	a{text-decoration: none; color: #fff;}
	ul{
		list-style: none; opacity: 0; visibility: hidden; position: absolute; padding: 0; margin: 0; background: #fff; border-radius: @borderRadius; width: 210px; left: 50%; top: 30px; padding: 17px 0; box-shadow: 0 15px 50px 0 rgba(0,0,0,0.3); transform: translateX(-50%); .transition(opacity);
		a{color: @textColor; display: block; padding: 2px 0;}
		&:before{.pseudo(10px,10px); background: #fff; .rotate(45deg); left: 50%; margin-left: -5px; top: -3px;}
		@media (max-width: @tp){top: 28px;}
	}
	li.has-children:hover{
		@media (min-width: @m){
			&>a{padding-bottom: 10px;}
		}
		@media (min-width: @t){
			ul{opacity: 1; visibility: visible;}
		}
	}
	li.has-children.active ul{opacity: 1; visibility: visible;}
	@media (max-width: @l){grid-row: 1; grid-column: ~"3/5"; margin-right: 125px;}
	@media (max-width: @t){grid-column: ~"2/4"; margin-right: 0;}
	@media (max-width: @tp){
		justify-content: flex-start;
		&>li{margin-right: 8px;}
		ul{width: 180px; font-size: 14px;}
	}
	@media (max-width: @m){display: none;}
}
.btn-toggle-nav{
	display: none; position: absolute; justify-content: center; align-items: center; top: 0; right: 0; width: 50px; height: 100%;.gradient-orange; border-left: 1px solid #E2926E;
	span, span:after, span:before { .pseudo(18px,2px); background: #fff; left: 0; top: 0; .transition(all); }
	span {
		position: relative;
		&:before {top: -5px;}
		&:after {top: 5px;}
	}
	&.active {
		span {background: transparent;}
		span:after {.rotate(45deg); top: 0;}
		span:before {.rotate(-45deg); top: 0;}
	}
	&:hover{text-decoration: none; color: @red;}
	@media (max-width: @m){
		display: flex;
	}
}

.nav-sidebar{
	list-style: none; padding: 0; margin: 0 0 40px; font-weight: bold; font-size: 15px;
	li{
		margin-bottom: 10px;
		&.selected a{color: @gray2;}
		@media (max-width: @m){width: 50%; margin-bottom: 6px;}
	}
	a{
		position: relative; text-decoration: none; color: @textColor; padding-left: 20px;
		&:before{.pseudo(6px,6px); background: @green; border-radius: 100px; top: 7px;}
		&:hover{color: @lightGreen; text-decoration: underline;}
		@media (max-width: @m){
			padding-left: 15px;
			&:before{top: 5px;}
		}
	}
	@media (max-width: @t){font-size: 13px;}
	@media (max-width: @tp){font-size: 12px;}
	@media (max-width: @m){margin: 0; display: flex; flex-wrap: wrap;}
}

.categories-container{
	position: absolute; visibility: hidden; opacity: 0; text-align: left; top: 135px; display: flex; z-index: 55; left: 123px; background: #fff; border-radius: @borderRadius; box-shadow: 0 25px 50px 0 rgba(0,0,0,0.2); color: @textColor; .transition(opacity);
	a{color: @textColor;}
	&.active{visibility: visible; opacity: 1;}
	@media (min-width: @t){
		&.has-extra-content{right: 0;}
	}
	@media (max-width: @l){left: 110px;}
	@media (max-width: @t){
		left: 105px; top: 110px; right: auto;
		&:before{top: -2px; left: 28px;}
	}
	@media (max-width: @tp){
		left: 85px; top: 102px;
	}
	@media (max-width: @m){display: none;}
}
.categories-container-body{display: flex; width: 100%;}
.category-container-products-title{
	border-bottom: 1px solid @borderColor; font-size: 24px; font-weight: bold; padding: 15px; flex-grow: 1; display: flex; align-items: center; justify-content: center;
	@media (max-width: @l){font-size: 22px;}	
}
.category-container-product-items{
	display: flex;
	.cp{
		width: calc(~"100% / 3 - -2px"); font-size: 13px; border-bottom: 0;
		&:last-child{border-right: 0;}
	}
	.cp-addtocart{display: none!important;}
	.cp-brand{
		top: 15px;
		img{max-height: 20px;}
	}
	.cp-cnt{padding-left: 20px; padding-right: 20px;}
	.cp-image{height: 229px;}
	.cp-price{margin-left: 20px; margin-right: 20px; font-size: 14px;}
	.cp-attr-container{left: 20px;}
	.cp-wishlist{top: 10px;}
	.cp-attr-container img{width: 32px;}
	.cp-lowest-price{max-width: 130px;}
	.cp-save{
		@media (max-width: @l){display: none;}
	}
}
.nav-categories{
	list-style: none; padding: 0; margin: 0; width: 300px; flex-grow: 0; flex-shrink: 0; border-right: 1px solid @borderColor; font-size: 16px; 
	li{
		@media (min-width: @m){
			&:last-child{
				a{border: 0;}
			}
		}
		&.active{
			a{
				color: #fff;
				&:after{opacity: 1;}
			}
			&.has-children a:before{opacity: 1;}
		}
		&:first-child a:after{border-top-left-radius: @borderRadius; top: 0;}
		&:last-child a:after{border-bottom-left-radius: @borderRadius; bottom: 0;}
	}
	a{
		text-decoration: none; border-bottom: 1px solid @borderColor; display: block; padding: 10px 27px; position: relative;
		&:after{.pseudo(auto,auto); top: -1px; right: 0; bottom: -1px; left: 0; .gradient-green; opacity: 0; .transition(opacity);}
		&:before{.pseudo(8px,8px); background: @green; top: calc(~"50% - 4px"); right: -4px; .rotate(45deg); opacity: 0; .transition(opacity);}
		span{position: relative; z-index: 1;}
	}
	@media (max-width: @l){width: 280px;}
	@media (max-width: @t){font-size: 14px;}
	@media (max-width: @tp){width: 255px;}
	@media (max-width: @m){
		width: 100%; border: 0;
		a{padding: 10px 15px;}
	}
}
.nav-categories-right{
	display: none; flex-grow: 1; padding: 15px 25px; min-width: 280px;
	&.active{display: block;}
	@media (max-width: @tp){min-width: 240px; font-size: 13px;}
	@media (max-width: @m){padding: 0;}
}
.subcategory-list{
	list-style: none; padding: 0; margin: 0;
	li{padding: 0 0 6px;}
	a{
		text-decoration: none;
		&:hover{color: @lightGreen; text-decoration: underline;}
	}
	.subcategory-new a{color: @lightGreen;}
	.subcategory-sale a{color: @red;}
	@media (max-width: @m){
		li{padding: 0; border-bottom: 1px solid @borderColor;}
		a{
			display: block; padding: 10px 15px;
			&:hover{color: @textColor; text-decoration: none;}
		}
	}
}
.category-container-promo{
	padding: 20px 25px 20px 0; align-self: center;
	img{display: block; width: auto; height: auto; border-radius: @borderRadius;}
}
.category-container-products{
	width: 720px; border-left: 1px solid @borderColor; display: flex; flex-direction: column;
	@media (max-width: @l){width: 530px;}
	@media (max-width: @t){display: none;}
}
.m-nav, .loyalty-quick, .m-nav-title{display: none;}
@media (max-width: @m){
	.m-nav{position: fixed; display: none; flex-direction: column; top: 0; margin-top: 50px; left: 0; right: 0; bottom: 0; z-index: 100; background: #fff; overflow: auto; overflow-x: hidden;}
	.m-nav-title{
		position: absolute; font-size: 14px; left: 15px; top: 14px; color: #fff; font-weight: bold;
		&.active{
			padding-left: 37px;
			&:before{.pseudo(26px,26px); display: flex; justify-content: center; align-items: center; .gradient-green; border-radius: @borderRadius; top: -2px; .icon-arrow-right(); font: 10px/10px @fonti; .scaleX(-1); left: 0;}
		}
	}
	.categories-container{
		opacity: 1; visibility: visible; position: relative; top: 0; left: 0; box-shadow: none; flex-grow: 1; width: 100%;
		&:before{display: none;}
		&.lvl2{
			.categories-container-body{.translate3d(-100vw);}
			.nav-categories{overflow: hidden; height: 300px;}
		}
	}
	.nav{
		flex-wrap: wrap; border-bottom: 1px solid @borderColor; font-size: 12px; padding-top: 13px; padding-bottom: 13px;
		li{width: 50%; margin: 0;}
		a{color: @textColor; display: block; padding: 4px 15px;}
	}
	.m-nav-support{
		padding: 15px; display: flex; font-size: 12px; font-weight: bold;
		p{padding: 0;}
		a{color: @green; font-size: 14px;}
	}
	.m-nav-support-col{flex-grow: 1;}
	.loyalty-quick{display: block; position: relative;}
	.active-nav{
		.nav{display: flex;}
		.m-nav{display: block;}
		.w-lang{display: flex;}
		.loyalty-quick, .nav ul{display: none;}
		.zEWidget-launcher{visibility: hidden!important; pointer-events: none;}
		.m-nav-title, .currency, .categories-container{display: block;}
		.logo, .sw, .wishlist, .ww, .aw, .wrapper-promo, .categories, .benefits, .page-wrapper>*:not(.m-nav):not(.header){display: none;}
		.m-cat-item{display: flex; width: 100%; border-bottom: 1px solid @borderColor; align-items: center; padding-left: 15px;}
		.quick-order{
			font-size: 14px; font-weight: bold; border: 0; padding: 0; display: flex;
			&:after{display: none;}
			span:before{color: @green; margin-right: 10px;}
		}
	}
	.nav-categories{
		width: 100vw;
		li.has-children>a:before{
			.icon-arrow-down(); font: 7px/7px @fonti; color: @green; .rotate(-90deg); right: 15px; opacity: 1; background: transparent;
		}
	}
	.categories-container-body{width: 200vw; .transition(all);}
	.nav-categories-right{width: 100vw;}
}
/*------- /navigation -------*/

/*------- auth widget -------*/
.aw{
	grid-column: ~"5/7"; grid-row: 1; text-align: center; position: relative; z-index: 11;
	a{
		text-decoration: none;
		&:hover{color: @lightGreen;}
	}
	&.active{
		.aw-tooltip{visibility: visible; opacity: 1;}
		span:after{.scaleY(-1);}
	}
	@media (max-width: @tp){grid-column: ~"7";}
	@media (max-width: @m){
		position: absolute; top: 0; right: 140px; width: 40px; height: 100%;
	}
}
.aw-login{
	color: #fff;
	&>span{
		position: relative; display: flex; align-items: center; justify-content: center; line-height: 1;
		&:before{.icon-user(); font: 24px/24px @fonti; color: @lightGreen; margin-right: 10px;}
		&:after{.icon-arrow-down(); font: 5px/5px @fonti; color: @lightGreen; margin-left: 8px;}
	}
	@media (max-width: @t){
		&>span:before{font-size: 20px; line-height: 20px; color: #fff;}
		&>span:after{margin-right: 3px;}
	}
	@media (max-width: @tp){
		font-size: 0;
		&>span:before{margin-right: 5px;}
		&>span:after{margin-left: 5px;}
	}
	@media (max-width: @m){
		font-size: 0; display: flex; align-items: center; justify-content: center; height: 100%;
		&>span{
			&:after{display: none;}
			&:before{margin: 0; color: #fff;}
		}
	}
}
.aw-tooltip{
	opacity: 0; visibility: hidden; border-radius: @borderRadius; width: 210px; right: -10px; top: 35px; position: absolute; background: #fff; box-shadow: 0 15px 50px 0 rgba(0,0,0,0.3); padding: 17px 0; .transition(opacity);
	&:before{
		.pseudo(10px,10px); background: #fff; .rotate(45deg); left: 50%; margin-left: -14px; top: -4px;
		@media (max-width: @tp){left: auto; margin-left: 0; right: 35px;}
	}
	a{display: block; color: @textColor; padding: 2px 0;}
	@media (max-width: @tp){
		width: 173px; font-size: 13px;
		a{padding: 3px 0;}
	}
}
/*
.aw-quick-login{
	width: 430px; padding: 0; color: @textColor; text-align: left;
	&:before{left: auto; right: 109px;}
}
.form-label{
	.field-quick{padding-bottom: 10px;}
	.field-quick-remember{padding: 5px 0 12px;}
}
.form-quick-login{padding: 25px 35px;}
.aw-quick-login-title{font-weight: bold; padding: 0 0 12px; font-size: 14px;}
a.aw-quick-login-close{
	position: absolute; top: 15px; right: 15px; font-size: 0; border-radius: 100px; width: 27px; height: 27px; background: #244538; display: flex; align-items: center; justify-content: center; padding: 0; .transition(all);
	&:before{.icon-cross(); font: 11px/11px @fonti; color: #fff;}
	&:hover{background: #244538/1.2;}
}
.field-quick-remember{
	input[type=checkbox]+label{font-size: 12px;}
}
.aw-quick-links{
	text-align: center; font-size: 12px;
	a{padding: 0; text-decoration: underline; color: @green;}
}
.btn-quick-login{width: 180px;}
*/
/*------- /auth widget -------*/

/*------- search widget -------*/
.sw{
	grid-column: ~"3/4"; position: relative; z-index: 100;
	.autocomplete-container{
		position: absolute; top: 48px; left: 0; text-align: left; z-index: 500; width: 930px; background: #fff; border-top: 1px solid @borderColor;
		@media (max-width: @l){width: 700px;}
		@media (max-width: @t){width: 640px;}
		@media (max-width: @tp){left: -210px; border-radius: @borderRadius;}
		@media (max-width: @m){left: 0; width: 100%; overflow: auto; max-height: 250px; box-shadow: 0 7px 5px rgba(0,0,0,.1);}
	}
	@media (max-width: @m){
		width: 40px; position: absolute; top: 0; right: 180px; height: 100%;
		&.active{
			left: 0; right: 0; width: auto; z-index: 200;
			.sw-form{display: block;}
			.sw-toggle{
				position: absolute; left: 0; width: 50px;
				&:before{.icon-cross(); color: @red; font-size: 15px; line-height: 15px;}
			}
			.sw-input{padding-left: 50px;}
		}
	}
}
.sw-input{
	height: 100%; width: 100%; padding-right: 60px; background: #fff; border: 0; color: @textColor; font-size: 16px; font-weight: normal; .placeholder(@textColor, @borderColor/1.1);
	@media (max-width: @t){font-size: 14px;}
	@media (max-width: @m){border-radius: 0; font-size: 13px;}
}
.sw-form{
	height: 50px; position: relative;
	@media (max-width: @m){display: none; border-bottom: 1px solid @borderColor;}
}
@media (max-width: @m){
	.sw-toggle{
		display: flex; align-items: center; justify-content: center; height: 100%; width: 40px; position: relative; text-decoration: none; z-index: 10;
		&:before{.icon-search(); font: 20px/20px @fonti; color: #fff;}
		&:hover{text-decoration: none;}
	}
	.page-homepage{
		.sw{position: relative; width: 100%; right: auto; left: auto; height: 50px;}
		.sw-form{display: block;}
		.sw-toggle{display: none;}
		.sw-input{padding-left: 15px; padding-right: 50px;}
		.sw-placeholder{height: 50px;}
	}
}
.sw-btn{
	position: absolute; top: 0; right: 0; height: 100%; width: 50px; display: flex; align-items: center; justify-content: center; padding: 0; background: none; font-size: 0;
	&:before{.icon-search(); font: 19px/19px @fonti; color: @green; .transition(all);}
	&:hover:before{color: @darkGreen;}
	&:after{display: none;}
}
/*------- /search widget -------*/

/*------- autocomplete -------*/
.ui-autocomplete{
	background: #fff; text-transform: none; list-style: none; width: 100% !important; left: 0 !important; padding: 0; margin: 0; max-height: 490px; overflow: auto; font-size: 13px; line-height: 1.4; z-index: 550 !important; top: 0 !important;
	li{cursor: pointer; color: @textColor; .transition(background-color);}
	a{
		display: block; color: @textColor; text-decoration: none;
		&:hover{text-decoration: underline; color: @lightGreen;}
	}
}

.autocomplete-container{
	&.no_catalogproduct .autocomplete-col1{display: none;}
	&.no_catalogcategory .autocomplete-col2{display: none;}
	&.no_catalogmanufacturer .autocomplete-col3{display: none;}
	&.no_catalogcategory.no_catalogmanufacturer{width: 100%;
		.autocomplete-col{float: none; width: 100%;}
	}
	&.no_catalogproduct{width: 60%;}
	&>.ui-autocomplete{overflow: hidden; height: 0px;}
	.ui-autocomplete{max-height: none;}
}
.autocomplete-wrapper{
	width: 100%; display: flex; box-shadow: 0 20px 30px 0 rgba(0,0,0,0.20);
	@media (max-width: @m){display: block; box-shadow: none;}
}
.autocomplete-title{text-transform: uppercase; font-size: 13px; font-weight: bold; color: @lightGreen;}
.autocomplete-col2{
	flex-grow: 1; padding: 12px 25px; line-height: 1.3;
	.ui-autocomplete{
		padding-bottom: 10px;
		a{padding: 2px 0;}
	}
	.autocomplete-showall{display: none!important;}
	@media (max-width: @m){padding: 10px 15px;}
}
.autocomplete-col1{
	width: 400px; border-right: 1px solid @borderColor;
	.ui-autocomplete{padding-bottom: 0;}
	.autocomplete-showall{
		padding: 10px 25px 20px;
		a, .ac-showall-btn{.btn; height: 44px; font-size: 14px; width: 100%; font-weight: normal;}
	}
	.ui-menu-item:not(.autocomplete-showall) a{
		display: flex; padding: 6px 20px 7px; line-height: 1.3; .transition(all);
		&:hover{text-decoration: none; background: @borderColor*1.04; color: @textColor;}
	}
	li:first-of-type>a{padding-top: 12px;}
	@media (max-width: @l){
		flex-grow: 0; flex-shrink: 0; padding-top: 8px;
		.autocomplete-showall{padding: 10px 15px 15px;}
		.ui-menu-item:not(.autocomplete-showall) a{padding: 2px 15px;}
	}
	@media (max-width: @t){
		width: 300px;
		.ui-menu-item:not(.autocomplete-showall) a{padding: 2px 5px;}
	}
	@media (max-width: @m){
		width: 100%; border: 0;
	}
}

.search-category{
	font-size: 11px; text-transform: uppercase; color: @lightGreen; font-weight: bold;
	@media (max-width: @t){font-size: 10px;}
}
.search-title{
	display: block; font-size: 13px;
	@media (max-width: @t){font-size: 12px;}
}
.search-price{display: block; font-weight: bold;}
.search-price-old{text-decoration: line-through; padding-right: 5px; font-size: 11px;}
.search-price-discount{font-weight: bold; grid-column: 2;}
.search-image{
	width: 60px; flex-shrink: 0; flex-grow: 0; margin-right: 10px;
	img{display: block; margin: 1px auto;}
	@media (max-width: @t){width: 50px;}
}
.search-col{flex-grow: 1;}

.field-autocomplete{
	.autocomplete-container{
		position: absolute; top: 52px; width: 100%;
		.ui-autocomplete{
			height: auto; padding: 0!important; margin: 0!important; max-height: 200px; overflow: auto; box-shadow: 0 10px 15px rgba(0,0,0,0.1); border: 1px solid @borderColor/1.2; border-top: 0; border-radius: 0 0 @borderRadius @borderRadius;
			li{
				border-bottom: 1px solid @borderColor; padding: 0!important;
				&:before{display: none!important;}
			}
			a{display: block; padding: 4px 25px;}
		}
	}
	@media (max-width: @m) {
		.autocomplete-container{
			.ui-autocomplete{
				a{padding: 4px 15px;}
			}
		}
	}
}
.auth-form{
	.ui-autocomplete{padding: 0!important; margin: 0!important;}
}
.text-highlight{font-weight: bold;}
@media screen and (max-width: @l) {
	.autocomplete-title{font-size: 12px;}
	.ui-autocomplete{font-size: 12px;}
	.autocomplete-col2 .ui-autocomplete a{padding: 0 0 1px;}
	.search-category{display: block;}
	.search-title{padding: 0; line-height: 1.4;}
}
/*------- /autocomplete -------*/

/*------- main -------*/
.main{
	background: #fff; width: var(--pageWidth); margin: auto; border-radius: @borderRadius; box-shadow: 0 25px 50px 0 rgba(0,0,0,0.2); margin-top: -320px; position: relative; z-index: 1; max-width: 1480px;
	@media (max-width: @t){border-radius: 0; background: none; margin-top: 0;}
	@media (max-width: @m){box-shadow: none;}
}
@media (max-width: @t){
	.main-wrapper{background: #fff;}
}
.sidebar{
	flex-shrink: 0; flex-grow: 0; width: 400px; border-left: 1px solid @borderColor; padding-left: 60px; padding-right: 60px; margin-bottom: 80px; margin-top: 50px;
	@media (max-width: @l){width: 325px;}
	@media (max-width: @t){width: 270px; padding-left: 30px; padding-right: 50px; margin-top: 25px;}
	@media (max-width: @tp){display: none;}
}
.main-body{flex-grow: 1;}
.main-content{
	padding-left: var(--contentSpacing); padding-right: var(--contentSpacing); margin-bottom: 80px; margin-top: 50px;
	@media (max-width: @t){margin-top: 25px;}
	@media (max-width: @tp){margin-bottom: 40px;}
	@media (max-width: @m){margin-top: 15px;}
}
.lists{
	ul{.list;}
	ol{
		margin: 15px 0 60px 0; counter-reset: counter; list-style: none;
		li{
			counter-increment: counter; position: relative; padding-left: 60px; margin-bottom: 25px;
			&:before{.pseudo(44px,44px); .gradient-green; border: 5px solid @lightGreen; font-weight: bold; border-radius: 100px; display: flex; align-items: center; justify-content: center; color: #fff; content: counter(counter)'.'; position: absolute; left: 0; top: -7px; text-indent: 4px;}
		}
		@media (max-width: @t){
			li{padding-top: 3px;}
		}
		@media (max-width: @tp){
			li{
				padding-left: 50px;
				&:before{width: 35px; height: 35px; font-size: 12px;}
			}
		}
		@media (max-width: @m){
			li:before{text-indent: 0;}
		}
	}
}
.support{
	font-size: 14px; margin-bottom: 15px;
	p{padding-bottom: 10px;}
	a{
		text-decoration: none; color: @textColor;
		&:hover{text-decoration: underline;}
	}
	.support-title, .support-number{
		padding: 0;
		@media (max-width: @m){padding: 0 0 5px;}
	}
}
@media (max-width: @m){
	.sidebar-cnt{
		padding: 20px 15px 25px 60px; background: url(images/bg-green.jpg); color: #fff;
		a{color: #fff;}
	}
	.support-sidebar{margin-bottom: 0;}
}
.support-contact{margin-bottom: 0;}
.support-title{
	font-weight: bold; font-size: 20px; position: relative;
	&:before{.icon-help(); font: 36px/36px @fonti; color: @green; display: block; margin-bottom: 10px;}
	@media (max-width: @t){font-size: 14px;}
	@media (max-width: @m){
		font-size: 18px; color: @lightGreen;
		&:before{position: absolute; top: 0; left: -45px; font-size: 28px; line-height: 28px; color: @lightGreen;}
	}
}
.support-number{
	font-weight: bold; font-size: 26px; line-height: 1.3;
	a:hover{text-decoration: none;}
	@media (max-width: @t){font-size: 20px;}
	@media (max-width: @m){font-size: 18px;}
}
.social{
	display: flex; font-size: 0;
	a{
		width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; border-radius: @borderRadius; margin: 0 10px 0 0; font-size: 0; padding: 0; overflow: hidden;
		&:before{.icon-fb(); font: 15px/15px @fonti; color: #fff; position: relative; z-index: 1;}
		@media (max-width: @tp){width: 40px; height: 40px;}
	}
	.ig:before{.icon-ig(); font-size: 18px;}
}
.social-m-nav{
	a{margin: 0 0 0 5px;}
}
/*------- /main -------*/

/*------- share -------*/
.share{
	padding: 20px 0 0; font-size: 14px;
	@media (max-width: @m){font-size: 12px;}
}
.ss-label{padding: 0 0 10px;}
.share-icons{display: flex;}
.ss-item{
	width: 42px; height: 42px; background: #fff; border: 1px solid @borderColor; border-radius: @borderRadius; font-size: 0; display: flex; justify-content: center; align-items: center; margin: 0 5px 0 0; border-radius: @borderRadius; cursor: pointer; color: @textColor; overflow: hidden; .transition(all); position: relative;
	&:hover{
		border-color: #ABC075/1.1;
		&:after{opacity: 1;}
		&:before{color: #fff;}
	}
	&:before{.icon-fb2(); font: 19px/19px @fonti; color: @textColor; position: absolute; top: 0; right: 0; bottom: 0; left: 0; display: flex; align-items: center; justify-content: center; z-index: 1; .transition(all);}
	&:after{.pseudo(100%,100%); .gradient-green; opacity: 0; top: 0; left: 0; right: 0; bottom: 0; .transition(opacity);}
}
.ss-wp:before, .ss-whatsapp:before{.icon-whatsapp(); font-size: 18px;}
.ss-viber:before{.icon-viber2();}
.ss-link:before{.icon-link(); font-size: 16px;}
.ss-mail:before, .ss-email:before{
	.icon-email(); font-size: 18px;
}
/*------- /share -------*/

/*------- tell friend -------*/
.quick-html body{background: #fff;}
.tellfriend{
	padding: 20px 30px;
	h1{padding: 0 0 20px 0; font-size: 20px; line-height: 28px; color: #000;}
	label, input[type=text]{display: block; width: 100%;}
	label{padding: 0 0 4px 0;}
	.error{top: 0;}
}
/*------- /tell friend -------*/

/*------- breadcrumbs -------*/
.bc { 
	padding: 0 0 15px; font-size: 12px; color: @gray2; position: relative;
	a { 
		text-decoration:none; padding: 0 10px 0 0; margin: 0 6px 0 0; position: relative; color: @lightGreen;
		&:after {content:"/"; color: #959F90; position: absolute; top: 0; right: 0;}
		&:hover{text-decoration: underline; color: @green;}
		&.active{color: @gray2;}
	}
	@media (max-width: @m){padding: 0 0 8px; font-size: 11px;}
}
.bc-short{
	span{display: none;}
	a:last-of-type{
		padding-right: 0; margin-right: 0;
		&:after{display: none;}
	}
}
/*------- /breadcrumbs -------*/

/*------- about -------*/
.page-about{
	.main{
		margin-top: 0;
		@media (max-width: @t){width: auto; margin-left: 30px; margin-right: 30px;}
		@media (max-width: @tp){margin-left: 20px; margin-right: 20px;}
		@media (max-width: @m){margin-left: 0; margin-right: 0;}
	}
	@media (max-width: @t){
		.main-wrapper{border-radius: @borderRadius;}
	}
	@media (max-width: @m){
		.main-wrapper{border-radius: 0; flex-direction: column-reverse; padding: 15px;}
		.wrapper-testimonials{flex-direction: column;}
		.iw-btns .btn{width: 100%;}
	}
	.header{
		padding-bottom: 212px;
		@media (max-width: @l){background-image: url(images/bg-dark-l.jpg); padding-bottom: 175px;}
		@media (max-width: @t){background-image: url(images/bg-dark-t.jpg); padding-bottom: 130px;}
		@media (max-width: @m){background: none;}
	}
	.main-content{
		font-size: 16px; line-height: 1.6;
		@media (max-width: @l){padding-left: 60px; margin-top: 30px; margin-bottom: 25px;}
		@media (max-width: @t){padding-left: 45px; padding-right: 30px; margin-top: 25px; font-size: 14px;}
		@media (max-width: @tp){padding-left: 25px;}
		@media (max-width: @m){padding: 0; margin: 0;}
	}
	.sidebar{
		border: 0; width: 700px; margin: 30px 30px 30px 0; padding: 0;
		@media (max-width: @l){width: 550px; margin-right: 20px; margin-top: 20px;}
		@media (max-width: @t){width: 440px;}
		@media (max-width: @tp){display: block; width: 350px;}
		@media (max-width: @m){width: auto; margin: 0;}
	}
	.testimonials{
		margin-top: -212px;
		@media (max-width: @l){margin-top: -180px;}
		@media (max-width: @t){margin-top: -130px;}
	}
}
.about-content{
	h2{font-size: 26px; padding-bottom: 10px;}
	.btn{margin-top: 10px;}
	@media (max-width: @t){
		h2{font-size: 22px; padding-top: 5px;}
	}
}
.about-title{
	font-size: 36px; padding: 0 0 2px; position: relative;
	&:before{.icon-symbol(); font: 40px/40px @fonti; color: @lightGreen; display: block; margin: 0 0 5px;}
	@media (max-width: @l){font-size: 28px;}
	@media (max-width: @t){
		font-size: 26px;
		&:before{font-size: 30px; line-height: 30px;}
	}
	@media (max-width: @tp){font-size: 24px;}
	@media (max-width: @m){
		&:before{display: none;}
	}
}
.about-slogan{
	color: @green; font-size: 20px; font-weight: bold;
	@media (max-width: @l){font-size: 18px;}
	@media (max-width: @tp){font-size: 16px;}
}
.about-images{
	img{display: block; width: 100%; border-radius: @borderRadius;}
	.slick-prev{left: 30px;}
	.slick-next{right: 30px;}
	@media (max-width: @t){
		.slick-prev{left: 15px;}
		.slick-next{right: 15px;}
	}
	@media (max-width: @m){margin-bottom: 20px; display: none;}
}
@media (max-width: @m){
	.about-image-placeholder{
		min-height: 300px;
		.about-images{display: block;}
	}
}
.about-fact{
	color: #fff; text-align: center; font-size: 42px; font-weight: bold; padding: 85px;
	@media (max-width: @l){font-size: 38px; padding: 60px;}
	@media (max-width: @t){font-size: 32px; padding: 55px;}
	@media (max-width: @tp){font-size: 28px; padding: 40px;}
	@media (max-width: @m){
		padding: 30px 0; font-size: 20px; background: url(images/benefits-m.jpg) no-repeat center top; background-size: cover;
		&>span{display: block; max-width: 250px; margin: auto;}
	}
}
/*------- /about -------*/

/*------- faq -------*/
.f-title{padding-bottom: 30px;}
.faq-cat-title{
	font-size: 26px; padding: 0 0 15px;
	@media (max-width: @tp){font-size: 22px;}
	@media (max-width: @m){font-size: 16px;}
}
.faq-cat{margin-bottom: 42px;}
.fp{
	margin-bottom: 10px;
	&.active{
		.fp-title{
			color: @green; padding-bottom: 8px;
			.toggle-icon:before{display: none;}
		}
		.fp-content{max-height: 1500px;}
	}
}
.fp-content{
	overflow: hidden; max-height: 0; .transition(max-height); padding: 0 0 0 25px;
	@media (max-width: @m){font-size: 12px;}
}
.fp-title{
	display: flex; align-items: flex-start; cursor: pointer; font-size: 17px; .transition(all);
	&:hover{color: @green; text-decoration: underline;}
	.toggle-icon{
		margin: 6px 10px 0 0; position: relative;
		@media (max-width: @t){margin-top: 3px;}
	}
	@media (max-width: @t){font-size: 15px;}
	@media (max-width: @tp){font-size: 14px; line-height: 1.4;}
}
/*------- /faq -------*/

/*------- contact -------*/
.page-contact{
	.main-content{padding-right: 20px;}
	@media (max-width: @t){
		.main-content{padding-right: 30px;}
		.map{margin-top: 30px;}
		.support-title{font-size: 18px;}
	}
	@media (max-width: @m){
		.support-title{
			font-size: 16px;
			&:before{font-size: 22px; line-height: 22px;}
		}
		.support-title, .support-number{padding: 0; color: @textColor;}
		.support-title-contact:before{left: -38px;}
		.main-content{padding-right: 15px; margin-bottom: 10px;}
		.map{margin: 20px 0 0;}
	}
}
@media (max-width: @m){
	.contact-row{flex-direction: column-reverse;}
}
.contact-col1{
	font-size: 14px; width: 530px; flex-grow: 0; flex-shrink: 0; padding-right: 50px;
	h2{font-size: 20px; padding: 0 0 10px;}
	p{padding: 0 0 5px;}
	@media (max-width: @l){width: 410px;}
	@media (max-width: @t){width: 53%;}
	@media (max-width: @m){
		width: 100%; padding-right: 0; font-size: 12px;
		h2{font-size: 16px;}
	}
}
.contact-col2{
	padding-left: 55px; font-size: 14px; flex-grow: 1;
	h2{font-size: 20px; line-height: 1.4; padding: 0;}
	.btn{margin-top: 10px;}
	@media (max-width: @m){
		padding-left: 40px; margin-bottom: 30px; font-size: 12px;
		.btn-locations{margin-left: -40px; margin-top: 0;}
	}
}
.support-title-contact{
	position: relative;
	&:before{position: absolute; left: -52px; top: -3px;}
}
.support-title-job:before{.icon-portfolio(); font-size: 28px;}
.support-title-wholesale:before{.icon-store(); font-size: 28px;}
.social-contact{
	padding-bottom: 50px;
	@media (max-width: @m){padding-bottom: 30px;}
}
.contact-links{
	margin-bottom: 30px;
	.support-title{padding: 0;}
	@media (max-width: @m){margin-bottom: 20px;}
}

.map{
	height: 580px; content-visibility: auto; contain-intrinsic-size: 580px;
	img[src*="googleapis.com"]{filter: grayscale(1);}
	@media (max-width: @l){height: 520px;}
	@media (max-width: @t){height: 400px;}
	@media (max-width: @m){height: 360px;}
}
.infoBox{
	width: 300px; position: relative; font-size: 14px; line-height: 1.3; background: #fff; margin: -30px 25px;box-shadow: 0 0 30px 0 rgba(0,0,0,0.5); border-radius: @borderRadius; overflow: visible!important;
	&>strong{display: none;}
	&>br{display: none;}
	&>img{position: absolute!important; top: -10px; right: -10px; z-index: 1; width: 30px; height: auto;}
	&>span>img{display: block; border-radius: @borderRadius @borderRadius 0 0;}
	&:before{.pseudo(15px, 15px); background: @white; position: absolute; left: -4px; top: 159px; transform: rotate(45deg);}
}
.infoBox-cnt{
	padding: 17px 25px 8px; font-size: 14px; line-height: 1.4; display: block;
	&>span{display: block;}
	.title{font-weight: bold; color: @green; padding-bottom: 8px; display: block;}
	.business-hour{padding-bottom: 10px;}
	p{padding-bottom: 10px;}
	a{color: @textColor;}
}
/*------- /contact -------*/

/*------- locations -------*/
.page-locations{
	.main{margin-top: -675px;}
	.header{padding-bottom: 675px;}
	@media (max-width: @t){
		.header{padding-bottom: 540px; background-size: contain;}
		.main{margin-top: -540px;}
	}
}
.locations-header{
	background: url(images/bg-green.jpg); color: #fff; padding: 50px 0 40px;
	@media (max-width: @m){padding: 20px 15px;}
}
.locations-title{
	padding: 0; font-size: 37px;
	@media (max-width: @m){font-size: 28px;}
}
.bc-locations{
	font-size: 12px; padding: 0 0 10px;
	a{color: #fff;}
	@media (max-width: @m){padding-bottom: 5px;}
}
.l-items{
	padding-bottom: 60px;
	@media (max-width: @m){padding: 15px;}
}
.nav-locations{
	list-style: none; padding: 25px 0 0 0; margin: 0; display: flex;
	li{margin: 0 30px 0 0;}
	a{
		color: #fff; text-decoration: none; position: relative; padding-left: 30px;
		&:before{.pseudo(20px,26px); background: url(images/icons/pin1.svg) no-repeat left top; background-size: contain; left: 0; top: -2px;}
		&:hover{text-decoration: underline; color: @lightGreen;}
	}
	@media (max-width: @t){
		position: relative; justify-content: space-between; bottom: auto; right: auto; flex-wrap: wrap; font-size: 12px;
		li{margin: 0;}
	}
	@media (max-width: @m){
		row-gap: 10px;
		li{width: 33%;}
		a{padding-left: 21px;}
		a:before{width: 15px; height: 20px;}
	}
}
.l-title{
	font-size: 28px; padding: 40px 0 15px;
	@media (max-width: @m){font-size: 20px; padding-top: 10px;}
}
.l-counter{font-size: 16px; color: @green; font-weight: normal;}
.lp{
	display: flex; margin-bottom: 20px;
	.btn{margin-top: 10px;}
	@media (max-width: @m){flex-wrap: wrap;}
}
.lp-cnt p{
	padding-bottom: 10px;
	@media (max-width: @m){padding-bottom: 3px;}
}
.lp-left{
	width: 480px; flex-grow: 0; flex-shrink: 0;
	img{display: block;}
	@media (max-width: @t){width: 405px;}
	@media (max-width: @tp){width: 365px;}
	@media (max-width: @m){width: 100%;}
}
.lp-right{
	flex-grow: 1; padding-left: 75px; display: flex; align-items: center;
	@media (max-width: @t){padding-left: 60px;}
	@media (max-width: @m){
		padding: 15px;
		.btn{margin-top: 6px;}
	}
}
.lp-title{
	padding: 0 0 7px; color: @green; font-size: 16px; font-weight: bold;
	a{color: @green;}
	@media (max-width: @t){font-size: 14px;}
}
.lp-images{
	//overflow: hidden; max-height: 0;
	&.slick-initialized{max-height: none; overflow: visible;}
	.slick-arrow{width: 46px; height: 46px;}
	.slick-prev{left: -23px;}
	.slick-next{right: -23px;}
	@media (max-width: @m){
		.slick-prev{left: 10px;}
		.slick-next{right: 10px;}
	}
}
@media (max-width: @t){
	.l-map{padding: 0 30px 50px; height: 400px;}
}
@media (max-width: @tp){
	.l-map{padding: 0 20px 40px;}
}
@media (max-width: @m){
	.l-map{padding: 0;}
}
/*------- /locations -------*/

/*------- location detail -------*/
@media (max-width: @m){
	.ld-row{flex-direction: column;}
}
.ld-col1{
	flex-grow: 1; padding: 40px 100px;
	@media (max-width: @l){padding-left: 70px; padding-right: 70px;}
	@media (max-width: @t){padding-right: 0; padding-left: 60px; padding-bottom: 20px;}
	@media (max-width: @tp){padding-left: 45px;}
	@media (max-width: @m){padding: 15px;}
}
.ld-col2{
	width: 700px; flex-grow: 0; flex-shrink: 0; margin: 30px;
	@media (max-width: @l){width: 550px;}
	@media (max-width: @t){width: 470px;}
	@media (max-width: @tp){width: 350px;}
	@media (max-width: @m){width: 100%; margin: 0;}
}
.ld-title{
	font-size: 26px; line-height: 1.5; padding: 0 0 15px; color: @green;
	@media (max-width: @m){font-size: 16px;}
}
.ld-icon{
	position: relative; padding-left: 30px; margin-bottom: 12px;
	&:before{.icon-clock2(); font: 20px/20px @fonti; color: @lightGreen; position: absolute; left: 0; top: 2px;}
	p{padding-bottom: 0;}
	@media (max-width: @m){
		padding-left: 25px; margin-bottom: 7px;
		&:before{font-size: 16px; line-height: 16px;}
	}
}
.ld-address:before{
	content:""; background: url(images/icons/pin1.svg) no-repeat left top; background-size: contain; width: 20px; height: 26px; top: 0;
	@media (max-width: @m){width: 16px; height: 20px;}
}
.ld-contact:before{.icon-phone();}
.ld-cnt{padding-top: 15px;}
.ld-images{
	img{display: block; border-radius: @borderRadius; width: auto; margin: auto;}
	.slick-prev{left: 30px;}
	.slick-next{right: 30px;}
	@media (max-width: @m){
		margin-top: 10px;
		.slick-arrow{width: 46px; height: 46px;}
		.slick-prev{left: 10px;}
		.slick-next{right: 10px;}
	}
}
.bc-ld{
	font-size: 12px; padding-bottom: 6px;
	a{color: @green;}
	@media (max-width: @m){display: none;}
}
.btn-all-stores{
	display: inline-flex; margin: 10px 0; position: relative; font-size: 12px;
	&:before{.icon-arrow-left(); font: 15px/7px @fonti; color: @green; position: absolute; left: -25px; top: 6px;}
	@media (max-width: @m){margin-top: 0; margin-left: 25px;}
}
/*------- /location detail -------*/

/*------- comments -------*/
/*
.comments-title{
	font-size: 20px; font-weight: bold; padding: 0 0 16px;
	@media (max-width: @t){font-size: 18px;}
	@media (max-width: @m){font-size: 16px;}
}
.pd-comments{
	.comment-field-rate, .comment-rate{display: none!important;}
	@media (max-width: @m){padding-bottom: 35px;}
}
.comment-form-container{
	margin-bottom: 50px;
	@media (max-width: @m){margin-bottom: 40px;}
}
.comment-form{display: flex; flex-wrap: wrap;}
.comment-field{
	width: calc(~"50% - 10px");
	label{font-size: 14px;}
	@media (max-width: @tp){width: calc(~"50% - 5px");}
	@media (max-width: @m){width: 100%;}
}
.comment-field-rate{
	width: 100%; display: flex; align-items: center; padding-left: 25px; margin: 5px 0;
	@media (max-width: @m){padding-left: 15px;}
}
.comment-field-email{margin-left: auto;}
.comment-field-message{
	width: 100%;
	textarea{width: 100%; display: block;}
}
.form-rate-item{
	display: inline-flex; margin-right: 5px; width: 20px; height: 21px;
	input[type=radio]+label{
		padding: 0; font-size: 0;
		&:before{.icon-star(); color: #D5D9D3; font: 20px/20px @fonti; border: 0; box-shadow: none!important; background: none!important;}
		&:after{display: none;}
	}
	&.active, &.active-hover{
		input[type=radio]+label:before{color: @yellow;}
	}
}
.form-field-rate-items{
	display: flex;
	@media (max-width: @m){.scale(0.8); transform-origin: left center;}
}
.btn-send-comment{
	min-width: 200px;
	@media (max-width: @t){min-width: 150px; font-size: 16px;}
	@media (max-width: @tp){min-width: 120px; font-size: 14px;}
	@media (max-width: @m){width: 100%;}
}
.cd-comments .comment-note{
	text-align: left; padding-left: 20px; max-width: 310px; margin-right: 0;
	@media (max-width: @m){padding: 15px 15px 0; max-width: 300px; margin: auto; text-align: center;}
}
.comment-buttons{
	display: flex; width: 100%; align-items: center;
	@media (max-width: @m){flex-wrap: wrap;}
}
.comment-note{
	font-size: 12px; font-style: italic; line-height: 1.5; color: @gray2; max-width: 270px; margin-right: 25px; margin-left: auto; text-align: center;
	@media (max-width: @t){margin-right: 0;}
	@media (max-width: @tp){font-size: 11px;}
	@media (max-width: @m){margin: 15px auto 0;}
}
.comment-success .btn{margin-top: 15px;}
.comments-list{
	margin-bottom: 60px;
	@media (max-width: @tp){margin-bottom: 30px;}
	@media (max-width: @m){margin-bottom: 0;}
	&.active{
		.comments-load-more{display: none;}
		.comment-items .comment:nth-of-type(1n + 11){display: block;}
	}
}
.comments-list-title{padding-bottom: 0;}
.comment-items{
	.comment:nth-of-type(1n + 11){display: none;}
}
.comments-load-more{margin-top: 8px;}

.comment{
	font-size: 14px; border-top: 1px solid @borderColor; padding: 15px 0 19px;
	@media (max-width: @m){font-size: 12px;}
}
.comment-header{padding-bottom: 5px; display: flex; align-items: center;}
.comment-date{color: @gray2; width: 100%; flex-grow: 1; flex-shrink: 1; text-align: right;}
.comment-rate, .add_rate{
	display: inline-flex;
	.icon-star, .icon-star-empty{
		position: relative; margin-right: 2px;
		&:before{.icon-star(); font: 12px/12px @fonti; color: #d5d9d3;}
	}
	.icon-star:before{color: @yellow;}
}
.comment-rate{margin-right: 12px; flex-grow: 0; flex-shrink: 0;}
.comment-rate .icon-star-active:before, .add_rate .icon-star:before{color: @yellow;}
.comments-counter{color: @green; font-weight: normal; font-size: 16px;}
.comment-username{font-weight: 700; margin-right: 10px; flex-grow: 0; flex-shrink: 0;}
.comment-verified-buyer{
	position: relative; display: flex; align-items: center; font-size: 12px; line-height: 16px; text-transform: uppercase; font-weight: 700; color: @green; padding-top: 2px; flex-grow: 0; flex-shrink: 0;
	&:before{.icon-available(); font: 20px/1 @fonti; color: @green; margin-right: 6px; margin-top: -2px;}	
}
.comment-footer{position: relative; display: flex; align-items: center; justify-content: space-between; margin-top: 12px;}
.comment-footer-top{position: relative; display: flex; align-items: center; gap: 10px; width: 100%;}
.comment-review-useful{font-size: 12px;}
.review-rates{
	position: relative; display: flex; align-items: flex-end; font-size: 14px; line-height: 1; padding-left: 22px;
	&.empty{color: @gray2;}
	&:before{.pseudo(16px,16px); .icon-thumbsup(); font: 16px/1 @fonti; color: @green; bottom: 0; left: 0;}
	&.down{
		&:before{color: @red; .rotate(180deg); bottom: auto; top: 0;}
	}
}
.comment-reply{
	flex-grow: 1; flex-shrink: 1; display: flex; justify-content: flex-end; text-align: right; text-decoration: underline; font-size: 12px; line-height: 16px; color: @darkGreen; cursor: pointer; transition: text-decoration-color 0.3s; padding-left: 22px;
	span{
		position: relative; display: flex; align-items: center; padding-left: 28px; text-align: right; flex-grow: 0; flex-shrink: 0;
		&:before{.pseudo(20px,17px); .icon-reply(); font: 17px/1 @fonti; color: @green; left: 0;}
	}
	@media (min-width: @h){
		&:hover{text-decoration-color: transparent;}
	}
}
*/
/*------- /comments -------*/

/*------- tabs -------*/
.m-tabs{position: relative; z-index: 1; display: block;}
.tabs{
	display: flex; justify-content: center; list-style: none; padding: 0; margin: -54px 0 0; font-weight: bold;
	li{
		.gradient-green; position: relative; margin: 0 1px 0 0; border-top-right-radius: @borderRadius; border-top-left-radius: @borderRadius; overflow: hidden;
		&.active{
			background: #fff;
			a, span{color: @textColor;}
		}
		&:after{.pseudo(auto,auto); top: 0; right: 0; bottom: 0; left: 0; .gradient-green-hover; opacity: 0; .transition(opacity);}
		&:hover:not(.active){
			&:after{opacity: 1;}
		}
	}
	a, span{
		height: 54px; min-width: 220px; padding: 0 20px; display: flex; align-items: center; justify-content: center; color: #fff; text-decoration: none; position: relative; z-index: 1; .transition(color);
		@media (max-width: @t){min-width: 160px;}
		@media (max-width: @tp){height: 47px;}
	}
	span{cursor: pointer}
	.tab-best-buy{
		.gradient-red;
		&:after{.gradient-red-hover;}
	}
	@media (max-width: @tp){margin: -47px 0 0;}
	@media (max-width: @m){margin: 0; display: none;}
}
.tab-content{
	padding: 35px 0 0;
	@media (max-width: @t){padding-left: 30px; padding-right: 30px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px; padding-top: 25px;}
}
@media (max-width: @m){
	.cw-tabs{
		.tab-content{max-height: none!important; padding: 0 0 10px; position: relative; overflow: visible!important; visibility: visible!important;}
		.tabs-content{padding-top: 20px;}
		.tab-toggle{font-weight: bold; padding: 15px 0 10px 15px; font-size: 16px;}		
	}
}
.tab{
	overflow: hidden; height: 0;
	&.active{height: auto; overflow: visible;}
	@media (max-width: @m){
		overflow: visible; height: auto;
	}
}
.tab-toggle{
	display: none;
	@media (max-width: @m){display: block;}
}
.tab-btns{
	text-align: center;	padding-top: 35px; padding-bottom: 85px;
	.btn{min-width: 180px;}
	@media (max-width: @tp){padding-top: 25px; padding-bottom: 50px;}
	@media (max-width: @m){
		padding-bottom: 0; position: absolute; top: -33px; right: 15px; padding: 0;
		.btn{
			padding: 0; background: none; min-width: 0; height: auto; padding-right: 18px; color: @green; font-size: 12px; position: relative;
			&:after{display: none;}
			&:before{.icon-arrow-left(); position: absolute; font: 10px/10px @fonti; top: 5px; right: 0; display: block; .scaleX(-1);}
		}
	}
}
/*------- /tabs -------*/

/*------- benefits -------*/
.benefits{
	color: #fff; justify-content: space-between; font-size: 16px; padding: 45px 70px 45px;
	@media (max-width: @l){font-size: 15px;}
	@media (max-width: @t){padding-left: 105px; padding-right: 105px; font-size: 14px;}
	@media (max-width: @tp){font-size: 13px; padding: 35px 60px 20px;}
	@media (max-width: @m){background: url(images/benefits-m.jpg) no-repeat center top; background-size: cover; display: block; text-align: center; font-size: 12px; padding: 20px 0;}
}
.benefit{
	padding-left: 50px; position: relative; min-height: 30px;
	strong{color: @lightOrange;}
	a{
		text-decoration: none; color: #fff; display: flex; align-items: center;
		&:before{.icon-happiness(); font: 35px/35px @fonti; color: @lightOrange; position: absolute; left: 0; top: -6px; .transition(all);}
		&:hover{
			color: @lightGreen; text-decoration: underline;
			strong, &:before{color: @lightGreen;}
		}
	}
	@media (max-width: @t){
		width: 28%;
		&:before{top: 3px;}
	}
	@media (max-width: @tp){width: 33%;}
	@media (max-width: @tp){
		padding-left: 45px;
		&:before{font-size: 31px; top: 1px;}
	}
	@media (max-width: @m){
		padding: 7px 15px; width: 100%;
		a{display: block;}
		a:before{display: none;}
	}
}
.benefit-shipping{
	padding-left: 65px;
	a:before{.icon-shipping(); font-size: 30px;}
	@media (max-width: @tp){
		&:before{font-size: 26px;}
	}
	@media (max-width: @tp){padding-left: 57px;}
	@media (max-width: @m){width: 100%; padding: 0;}
}
.benefit-nutrigold{
	padding-left: 0;
	span{display: flex; align-items: center; flex-wrap: wrap; justify-content: center;}
	a:before{display: none;}
	img{margin-right: 10px;}
	@media (max-width: @t){
		display: block; text-align: center; width: 20%;
		img{display: block; margin: auto;}
	}
	@media (max-width: @tp){
		img{height: 20px; width: auto;}
	}
	@media (max-width: @m){
		width: 100%;
		img{display: inline-block; vertical-align: bottom; margin: 0 10px 0 0; height: 18px;}
	}
}
.benefit-faq{
	a:before{.icon-faq(); font-size: 30px;}
}
.benefit-personal-pickup{
	a:before{.icon-location(); font-size: 30px;}
}
.benefit-payment-safe {
	a:before{.icon-payment-protection(); font-size: 30px;}
}
/*------- /benefits -------*/

/*------- categories -------*/
@media (max-width: @m){
	.categories{background: #fff;}
}
.categories-widget{
	display: flex; list-style: none; padding: 50px 70px 0; margin: 0 0 160px; flex-wrap: wrap; justify-content: center; position: relative; z-index: 50;
	&>li{
		width: 268px; text-align: center; margin-bottom: 65px; position: relative;
		&:nth-child(n+6){margin-bottom: 0;}
		@media (max-width: @l){width: 230px;}
		@media (max-width: @t){margin-bottom: 45px; width: 172px;}
		@media (max-width: @tp){width: 190px;}
		@media (max-width: @m){width: 100%; margin: 0; text-align: left; border-bottom: 1px solid @borderColor;}
	}
	@media (min-width: @m){
		.active{
			z-index: 5;
			.category-subnav{visibility: visible; opacity: 1;}
			.category-title{color: @textColor; text-decoration: none;}
			.category-image{background: url(assets/images/bg-green.jpg); box-shadow: 0px 0px 0 7px #fff; .transition(box-shadow);}
		}
	}
	@media (max-width: @l){padding: 20px 0 0; margin-bottom: 110px;}
	@media (max-width: @t){padding-left: 70px; padding-right: 70px; margin-bottom: 120px;}
	@media (max-width: @tp){padding-left: 30px; padding-right: 30px; margin-bottom: 90px;}
	@media (max-width: @m){
		padding: 0; margin: 0; display: block; font-size: 1px;
		a{color: @textColor; display: flex;}
		.active{
			.category-subnav{max-height: 1000px; padding-bottom: 10px;}
			.toggle-icon:before{display: none;}
		}
	}
}
.category-subnav{
	list-style: none; width: 100%; visibility: hidden; opacity: 0; position: absolute; padding: 5px 20px 25px; margin: 0; font-size: 16px; line-height: 1.4; font-weight: normal; .transition(opacity);
	li{padding: 0 0 5px;}
	a{
		color: @textColor; text-decoration: none;
		&:hover{color: @lightGreen; text-decoration: underline;}
		@media (max-width: @m){display: block; padding: 5px 10px 5px 50px;}
	}
	li{
		position: relative; z-index: 1;
		@media (max-width: @m){padding: 0;}
	}
	&:after{.pseudo(auto,auto); left: -10px; right: -10px; top: -110px; bottom: 0; background: url(images/bg.jpg); border-radius: @borderRadius; box-shadow: 0px 0px 20px rgba(0,0,0,.2);}
	.subcategory-new{font-weight: bold;}
	.subcategory-sale{
		font-weight: bold;
		a{color: @red;}
	}
	@media (max-width: @l){font-size: 14px; padding-bottom: 15px;}
	@media (max-width: @t){
		font-size: 13px;
		&:after{top: -75px; left: 0; right: 0;}
	}
	@media (max-width: @m){
		position: relative; padding: 0; opacity: 1; visibility: visible; overflow: hidden; max-height: 0; .transition(all);
		&:after{display: none;}
	}
}
.category-title{
	text-align: center; text-decoration: none; font-size: 16px; display: block; position: relative; z-index: 1; color: #fff; .transition(none);
	&:hover{text-decoration: underline; color: @lightGreen;}
	@media (max-width: @l){font-size: 15px;}
	@media (
		max-width: @t){font-size: 13px;
		&:hover{text-decoration: none; color: #fff;}
	}
	@media (max-width: @tp){padding: 0 15px;}
	@media (max-width: @m){
		text-align: left; padding: 0; display: flex; align-items: center;
		&:hover{text-decoration: none; color: @textColor;}
	}
}
.category-title-link{
	.transition(all);
	&:hover{text-decoration: underline; color: @lightGreen;}
}
.category-toggle-icon{
	display: none;
	@media (max-width: @m){display: block; position: absolute; right: 10px; top: 19px; .scale(0.8);}
}
.category-image{
	width: 125px; height: 125px; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; background: rgba(0,0,0,.4); box-shadow: 0px 0px 0px 7px rgba(255,255,255,.08); border-radius: 100px;
	img{max-width: 50px; max-height: 55px; width: auto; height: auto;}
	@media (max-width: @t){
		width: 85px; height: 85px; box-shadow: 0px 0px 0px 4px rgba(255,255,255,.08);
		img{max-width: 30px; max-height: 30px;}
	}
	@media (max-width: @tp){
		width: 67px; height: 67px;
		img{max-height: 24px;}
	}
	@media (max-width: @m){
		width: 50px; height: 45px; box-shadow: none; background: none; flex-grow: 0; flex-shrink: 0; margin: 0;
	}
} 
.c-categories .categories-widget{
	margin-bottom: 0; padding-bottom: 80px; z-index: 2;
	@media (max-width: @t){padding-bottom: 45px;}
	@media (max-width: @m){padding-bottom: 0;}
}
/*------- /categories -------*/

/*------- special products -------*/
.cw{background: #fff;}
/*------- /special products -------*/

/*------- catalog -------*/
.page-catalog{
	.header{
		padding: 0; z-index: 50;
		@media (max-width: @m){background: #fff;}
	}
	&.toolbar-stuck .header-body{box-shadow: none;}
	.page-wrapper{overflow: initial;}
	@media (max-width: @t){
		.cp-btn-addtocart span:before{display: block;}
	}
}
.c-level0-header{
	text-align: center; color: #fff; padding-top: 15px;
	@media (max-width: @m){text-align: left; padding-left: 15px; padding-right: 15px; border-bottom: 1px solid @borderColor; color: @textColor; padding-bottom: 5px;}
}
.c-header{
	display: flex; align-items: flex-start;
	//img{width: auto; height: 34px; margin-right: 10px; margin-top: 4px;}
	@media (max-width: @t){
		font-size: 30px;
	}
	@media (max-width: @tp){
		//img{height: 25px;}
	}
	@media (max-width: @m){
		display: block; position: relative; padding-bottom: 0;
		//img{height: 22px; margin: 0; position: absolute; left: 0; top: 0;}
	}
}
.c-header-col{flex-grow: 1;}
.c-title{
	font-size: 36px; padding-bottom: 10px;
	@media (max-width: @t){font-size: 30px;}
	@media (max-width: @tp){font-size: 28px;}
	@media (max-width: @m){font-size: 18px;}
}
@media (max-width: @tp){
	.c-title-level1{padding-bottom: 7px;}
	.c-title-brand{padding-left: 0; padding-bottom: 15px;}
}
.c-title-level0{
	font-size: 42px;
	@media (max-width: @t){font-size: 34px;}
	@media (max-width: @m){color: @textColor;}
	@media (max-width: @m){font-size: 24px;}
}
.wrapper-catalog{
	margin-top: 40px; margin-bottom: 120px;
	@media (max-width: @t){padding-left: 30px; padding-right: 30px; margin-bottom: 80px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px; margin-top: 20px;}
	@media (max-width: @m){padding-left: 15px; padding-right: 15px; margin-bottom: 0; margin-top: 15px;}
}
.c-col1{
	width: 250px; flex-grow: 0; flex-shrink: 0; margin-right: 50px;
	@media (max-width: @l){width: 235px; margin-right: 30px;}
	@media (max-width: @t){width: 215px;}
	@media (max-width: @tp){display: none;}
}
.c-col2{flex-grow: 1;}
.bc-catalog{padding: 0 0 7px;}
.c-desc{
	font-size: 14px; line-height: 1.6; padding-top: 5px;
	&.has-border{
		border-bottom: 1px solid @borderColor; margin-bottom: 15px;
		@media (max-width: @m){
			border: 0;
			&:after{.pseudo(auto,1px); background: @borderColor; left: -15px; right: -15px; bottom: 0;}
		}
	}
	@media (max-width: @m){padding-top: 0; line-height: 1.5; font-size: 12px;}
}
.c-desc-cnt{
	max-width: 100%; 
	p{padding-bottom: 8px;}
	@media (max-width: @tp){
		.btn-toggle-content{
			position: relative; display: block; width: 100%; background: #fff; text-decoration: underline;
			&:before{.pseudo(100%,18px); background: linear-gradient(0deg,rgba(255, 255, 255, .9) 10%, rgba(255, 255, 255, .4) 100%); top: -18px; left: 0;}	
		}
		p:last-child{padding-bottom: 0;}
	}
}
.c-filter{
	font-size: 15px; font-weight: bold;
	a{
		display: block; text-decoration: none; color: @red; position: relative; padding-left: 32px;
		&:before{.pseudo(20px,20px); display: flex; align-items: center; justify-content: center; top: 3px; left: 0; border: 1px solid @borderColor; border-radius: @borderRadius;}
		&.active:before{.icon-check(); .gradient-green; border-color: @lightGreen; font: 10px/10px @fonti; color: #fff;}
	}
	@media (max-width: @m){
		order: 3;
		a:before{top: -2px;}
	}
}
@media (max-width: @m){
	.c-discount{margin-top: 13px; width: 100%; font-size: 12px;}
	.c-empty{margin-bottom: 20px;}
}
.c-counter{
	margin-right: auto;
	@media (max-width: @m){margin: 20px 0 10px; width: 100%;}
}
.c-toolbar{
	padding: 0 0 24px 0; font-size: 12px; display: flex; align-items: center; justify-content: flex-end;
	select{height: 50px; width: 220px;}
	&.no-border{border: 0;}
	@media (max-width: @tp){
		select{width: 180px;}
	}
	@media (max-width: @m){
		flex-wrap: wrap; justify-content: space-between;
		select{width: 100%; height: 40px; font-size: 12px;}
	}
}
.c-toolbar-level0{
	border:0; padding-top: 0;
	@media (max-width: @m){justify-content: space-between;}
}
.c-sort{
	margin-left: 50px;
	label{color: @gray2; padding: 0 12px 0 0;}
	select{padding-left: 22px;}
	@media (max-width: @l){margin-left: 12px;}
	@media (max-width: @tp){
		margin-left: 15px;
		label{display: none;}
	}
	@media (max-width: @m){order: 2; margin: 0; width: calc(~"50% - 5px");}
}
/*------- /catalog -------*/

/*------- catalog post -------*/
.c-items{display: flex; flex-wrap: wrap; padding-top: 1px;}
.c-items4 .cp{width: calc(~"100% / 4");}
.c-items5 .cp{width: calc(~"100% / 5");}
.c-items3 .cp{width: calc(~"100% / 3");}

@media (max-width: @m){
	.c-items-main{
		margin-left: -15px; margin-right: -15px;
		.cp{width: 50%;}
	}
}

.cp{
	display: flex; background: #fff; width: 20%; font-size: 14px; line-height: 1.5; border: 1px solid @borderColor; margin: -1px 0 0 -1px; flex-direction: column; position: relative; .transition(all);
	&.hover:not(.no-shadow){border-color: @lightGreen; z-index: 30;}
	@media (min-width: @t){
		&:hover, &.hover{
			z-index: 30;
			.cp-addtocart{opacity: 1;}
		}
		&:not(.no-shadow):hover, &.hover{box-shadow: 0 0 30px 0 rgba(0,0,0,0.25);}
	}
	@media (max-width: @t){font-size: 13px; line-height: 1.4;}
	@media (max-width: @m){font-size: 12px;}
}
.cp-col2{flex-grow: 1; display: flex; flex-direction: column;}
.cp-image{
	height: 300px; display: flex; position: relative; margin-bottom: 12px; overflow: hidden;
	a{display: flex; width: 100%; height: 100%; align-items: center; justify-content: center;}
	img{display: block; width: auto; height: auto; max-height: 100%; margin-bottom: -40px;}
	@media (max-width: @t){height: 185px;}
	@media (max-width: @tp){height: 250px; margin-bottom: 8px;}
	@media (max-width: @m){height: 160px;}
}
.cp-unavailable{
	.cp-main-image img{opacity: .35;}
}
.cp-brand{
	position: absolute; top: 20px; left: 0; right: 0; height: 30px; display: flex; align-items: center; justify-content: center;z-index: 2;
	img{max-height: 30px; max-width: 90px; width: auto; margin: auto; align-self: flex-start;}
	@media (max-width: @l){
		top: 13px;
		img{max-height: 25px;}		
	}
	@media (max-width: @t){
		img{max-height: 20px;}
	}
	@media (max-width: @tp){
		top: 13px;
		img{max-height: 20px;}
	}
	@media (max-width: @m){
		top: 9px;
		img{max-height: 16px;}
	}
}
.cp-cnt{
	padding: 0 var(--ppHorizontalSpacing) var(--ppHorizontalSpacing); flex-grow: 1;
	@media (max-width: @t){padding-bottom: 20px;}
}
.cp-category{
	text-decoration: none; text-transform: uppercase; color: @green; font-size: 12px; font-weight: bold;
	@media (max-width: @t){font-size: 11px; display: block; padding: 4px 0 5px;}
}
a.cp-category:hover{color: @green/1.2; text-decoration: none;}
.cp-title{
	margin-top: 1px; flex-grow: 1;
	a{
		text-decoration: none;
		&:hover{text-decoration: none;}
	}
}
.cp-code{
	color: @gray2; padding-top: 5px; font-size: 12px;
	@media (max-width: @m){font-size: 11px;}
}
.cp-price{
	margin: 0 var(--ppHorizontalSpacing) 16px; font-size: 16px; font-weight: bold; line-height: 1.3; position: relative; z-index: 1;
	@media (max-width: @t){font-size: 14px; margin-bottom: 10px;}
	@media (max-width: @m){font-size: 12px;}
	ins{padding: 0 2px;}
}
.cp-old-price{
	font-weight: normal; font-size: 12px; display: block;
	span{text-decoration: line-through;}
	@media (max-width: @t){font-size: 11px;}
	@media (max-width: @tp){font-size: 10px;}
}
.cp-save{
	position: absolute; bottom: 0; right: 0; font-size: 13px; font-weight: normal;
	@media (max-width: @t){font-size: 11px;}
	@media (max-width: @tp){width: 50px; text-align: center; margin-right: -6px;}
}
.cp-lowest-price{
	font-size: 9px; font-weight: normal; padding-top: 2px;
	@media (max-width: @l){max-width: 130px;}
}

.cp-addtocart{
	position: absolute; height: 64px; display: flex; background: #fff; top: 100%; left: -1px; right: -1px; box-shadow: 0 15px 30px 0 rgba(0,0,0,0.25); padding: 0 var(--ppHorizontalSpacing) 20px; opacity: 0; align-items: center; .transition(opacity);
	&:before{.pseudo(auto,35px); background: #fff; left: 0; top: -25px; right: 0;}
	@media (max-width: @l){padding: 0 var(--ppHorizontalSpacing) 15px; height: 54px;}
	@media (max-width: @t){
		opacity: 1; visibility: visible; top: auto; position: relative; height: 50px; left: auto; right: auto; box-shadow: none; padding-bottom: 10px;
		&:before{display: none;}
		&.pickup-not-visible{visibility: hidden !important; display: flex !important;}
	}
}
.cp-btn-addtocart{
	padding: 0; flex-grow: 1; height: 100%; font-size: 14px;
	span{
		display: flex; position: relative; align-items: center;
		&:before{.icon-cart(); color: #fff; font: 23px/23px @fonti; margin: -2px 10px 0 0;}
		@media (max-width: @l){
			&:before{font-size: 19px; line-height: 19px; margin-right: 8px;}
		}
		@media (max-width: @t){
			&:before{display: none;}
		}
		@media (max-width: @m){
			&:before{margin: 0;}
		}
	}
	@media (max-width: @t){font-size: 13px;}
	@media (max-width: @m){font-size: 0; width: 40px; flex-grow: 0; flex-shrink: 0;}

	&:lang(de){
		font-size: 12px;
		span:before{
			font: 18px/18px @fonti; margin-right: 6px;
			@media (max-width: @m){margin-right: 0;}
		}
		@media (max-width: @m){font-size: 0;}
	}
}
@media (max-width: @m){
	.cp-btn-addtocart-single{
		flex-grow: 1;
		span{
			font-size: 14px;
			&:before{margin-right: 6px;}
		}
	}
}
.cp-btn-detail{
	width: 44px; height: 44px; flex-grow: 0; flex-shrink: 0; padding: 0;
	span:before{.icon-info(); color: #fff; font: 15px/15px @fonti; display: flex; position: absolute; top: 0; left: 0; width: 100%; height: 100%; justify-content: center; align-items: center;}
	@media (max-width: @l){width: 39px; height: 39px;}
}
.cp-unavailable-label{
	flex-grow: 1; font-size: 13px;
	@media (max-width: @t){font-size: 12px;}
	@media (max-width: @m){font-size: 12px; max-width: 80px; margin-right: auto;}
}
.cp-add-success{display: block; background: #fff; padding: 2px 5px; position: absolute; bottom: -23px; left: 80px; right: 0; font-weight: bold; font-size: 11px; z-index: 10; text-align: center;}
@media (max-width: @tp){
	.tab-content{
		.cp-btn-addtocart{
			font-size: 0; width: 40px; height: 40px; flex-shrink: 0; flex-grow: 0;
			span:before{display: block; margin: 0; font-size: 17px;}
		}
		.cp-addtocart-single .cp-btn-addtocart{
			width: 100%!important; font-size: 12px;
			span:before{margin-right: 8px;}
		}
		.cp-image{height: 140px;}
		.cp-attr-container img{width: 25px;}
		.cp-brand{
			top: 10px;
			img{max-height: 15px;}
		}
		.cp-title, .cp-price{font-size: 12px;}
		.cp-wishlist{right: 0; top: 3px;}
		.cp-wishlist-btn:after{font-size: 17px;}
		.cp-save{font-size: 10px; line-height: 1.3; width: 50px; bottom: 1px;}
	}

	.cp-rp{
		font-size: 12px;
		.cp-image{height: 160px;}
		.cp-btn-addtocart{
			font-size: 0; width: 40px; flex-shrink: 0; flex-grow: 0;
			span:before{display: block; margin: 0; font-size: 17px;}
		}
		.cp-btn-addtocart-single{
			width: 100%;
			span:before{margin-right: 6px;}	
		}
		.cp-price{font-size: 13px;}
		.cp-save{width: 50px; text-align: center;}
		.cp-attr-container{display: none;}
		.cp-brand img{max-height: 15px;}
		.cp-category{font-size: 10px; line-height: 1.1;}
		.cp-wishlist{top: 0; right: 0;}
		.cp-wishlist-btn:after{font-size: 15px;}
		.cp-brand{top: 5px;}
	}
}

@media (max-width: @m){
	.cw-items{
		flex-wrap: nowrap; overflow: hidden; overflow-x: auto; padding-left: 1px; padding-bottom: 10px; -webkit-overflow-scrolling: touch;
		.cp{
			width: 140px; flex-grow: 0; flex-shrink: 0;
			&:first-child{margin-left: 15px;}
		}
	}
}

.cp-qty{
	width: 100px; height: 100%; position: relative; display: flex; flex-grow: 0; flex-shrink: 0; margin-right: 5px;
	input{width: 100%; height: 100%; padding: 0 35px; font-size: 16px; text-align: center;}
	&.cp-qty-single{
		display: none;
		&:before{.pseudo(auto,auto); left: 0; top: 0; right: 0; bottom: 0; background: rgba(255,255,255,.8); z-index: 35;}
	}
	@media (max-width: @l){
		width: 75px;
		input{padding: 0 25px;}
	}
	@media (max-width: @t){
		input{font-size: 14px;}
	}
	@media (max-width: @tp){
		flex-grow: 1;
		.qty-input-container{width: 100%; height: 100%;}
		input{font-size: 12px;}
	}
}
.wp-btn-qty, .qty-btn{
	width: 30px; display: flex; align-items: center; justify-content: center; flex-grow: 0; flex-shrink: 0; position: absolute; left: 0; top: 0; bottom: 0; z-index: 20; font-size: 0; cursor: pointer;
	&:before{.pseudo(14px,2px); background: @green;}
	&:after{.pseudo(2px,14px); background: @green; left: 6px;}
	@media (max-width: @l){width: 22px;}
	@media (max-width: @m){width: 28px;}
}
.wp-btn-inc, .qty-btn-inc{left: auto; right: 0; justify-content: flex-start;}
.wp-btn-dec, .qty-btn-dec{
	justify-content: flex-end;
	&:after{display: none;}
	.toggle-icon:before{display: none;}
}
.cp-attr-container{
	position: absolute; top: 15px; left: var(--ppHorizontalSpacing); z-index: 10; font-size: 0;
	img{
		width: 42px; position: relative; z-index: 1;
		@media (max-width: @l){width: 38px;}
	}
	@media (max-width: @l){top: 10px;}
	@media (max-width: @t){
		top: 12px;
		img{width: 30px;}
	}
	@media (max-width: @tp){
		img{width: 38px;}
	}
	@media (max-width: @m){
		img{width: 24px;}
	}
}
.cp-attr{
	margin-bottom: 8px; position: relative;
	&:hover .cp-attr-title{display: block;}
	@media (max-width: @t){margin-bottom: 5px;}
}
.cp-attr-title{
	position: absolute; top: 0; bottom: 0; font-size: 13px; border-radius: 100px; background: @green; color: #fff; padding: 11px 18px 0 52px; white-space: nowrap; cursor: default; display: none;
	@media (max-width: @l){padding: 9px 15px 0 48px;}
	@media (max-width: @t){display: none!important;}
}
.cp-attr-image{background: #fff; display: block; position: relative; z-index: 1; border-radius: 100px;}
.cp-badges{
	position: absolute; bottom: 0; left: var(--ppHorizontalSpacing); display: flex; flex-flow: column; row-gap: 8px; align-items: baseline; z-index: 1;
	@media (max-width: @t){row-gap: 3px;}
}
.cp-badge{
	display: flex; height: 30px; padding: 0 12px; font-weight: bold; background: @green; border-radius: @borderRadius; align-items: center; justify-content: center; color: #fff; min-width: 60px; margin-right: 1px; line-height: 1;
	@media (max-width: @t){height: 24px; min-width: 50px; font-size: 12px;}
	@media (max-width: @tp){height: 22px; min-width: 0; font-size: 11px; line-height: 14px; letter-spacing: -0.24px; padding: 0 7px;}
}
.cp-badge-discount, .cp-badge-action{background: @red;}
.cp-badge-special{background: @yellow;}
.cp-badge-pickup{
	background: @red;
	@media (max-width: @m){font-size: 10.3px; line-height: 14px; letter-spacing: -0.3px; min-height: 20px; height: 22px; padding: 0 3px;}
}
.cp-wishlist{
	position: absolute; top: 13px; right: 10px; z-index: 20;
	&.active{
		.cp-wishlist-add{display: none;}
		.cp-wishlist-remove{display: flex;}
	}
	@media (max-width: @l){top: 7px;}
	@media (max-width: @t){right: 5px;}
	@media (max-width: @tp){top: 7px;}
	@media (max-width: @m){right: 0; top: 4px;}
}
.cp-wishlist-btn{
	height: 42px; overflow: hidden; position: relative; text-decoration: none; display: flex; justify-content: center; align-items: center; font-size: 13px; font-weight: bold; border: 1px solid transparent; border-radius: @borderRadius; cursor: pointer;
	&:after{.icon-heart(); display: flex; align-items: center; justify-content: center; width: 42px; height: 100%; font: 23px/23px @fonti; color: @green; .transition(color); text-decoration: none;}
	@media (min-width: @t){
		&:hover{
			border-color: @borderColor; background: #fff;
			span{display: block;}
		}
	}
	&>span:not(.cp-wishlist-message){white-space: nowrap; padding: 0 5px 0 15px; display: none;}
	@media (min-width: @h){
		&:hover{
			text-decoration: none;
			&>span:not(.cp-wishlist-message){display: block;}
		}
	}
	@media (max-width: @t){
		&:after{font-size: 18px; line-height: 18;}
	}
	@media (max-width: @tp){
		&:after{font-size: 20px;}
	}
	@media (max-width: @m){
		&:after{font-size: 17px;}
	}
}
.cp-wishlist-remove{
	display: none;
	&:after{.icon-heart-full();}
	@media (min-width: @t){
		&:hover:after{.icon-trash();}
	}
	&:hover{color: @textColor;}
}
.product-in-wishlist{
	position: absolute; display: block; background: #fff; color: @textColor; font-size: 14px; white-space: nowrap; top: 100%; text-align: center; padding: 10px 20px; border-radius: @borderRadius; width: 100%; box-shadow: 0 0 10px 0 rgba(0,0,0,0.2); z-index: 10;
	&:after{.pseudo(10px,10px); background: #fff; left: calc(~"50% - 5px"); top: -3px; .rotate(45deg);}
	//&.wishlist_message_response_ok{display: block!important;}
	a{color: @lightGreen;}
	@media (max-width: @t){font-size: 12px; padding: 8px 16px;}
	@media (max-width: @m){width: 140px; max-width: none; white-space: normal;}
}
.cp-rate{
	height: 19px; display: flex; font-size: 12px; color: @gray2;
	@media (max-width: @tp){.scale(.8); transform-origin: left top;}
}
.cp-rate-counter{margin-left: 5px; margin-top: -2px;}
.cp-list{
	width: 100%; flex-direction: row; margin-left: 0;
	.cp-col1{width: 210px; flex-grow: 0; flex-shrink: 0; position: relative;}
	.cp-col2{flex-grow: 1; display: flex; flex-direction: column; flex-wrap: wrap; margin: 20px 25px 20px 30px; position: relative;}
	.cp-cnt{width: 100%; padding: 0;}
	.cp-addtocart{position: relative; align-items: initial; opacity: 1; top: auto; bottom: auto; left: auto; right: auto; background: none; box-shadow: none; padding: 0; height: 45px;}
	.cp-price{margin: 0; flex-grow: 1; display: flex; flex-wrap: wrap;}
	.cp-old-price{width: 100%;}
	.cp-save{position: relative; right: auto; bottom: -3px; padding-left: 12px; font-weight: bold;}
	.cp-btn-addtocart{width: 140px; flex-grow: 0;}
	img{max-height: 200px; width: auto;}
	.cp-brand{
		img{max-height: 20px;}
	}
	.cp-lowest-price{width: 100%;}
	.cp-unavailable-label{display: none;}
	.cp-footer{display: flex; align-items: center;}
	.cp-image{margin: 0; height: 250px;}
	.cp-attr-container{left: 15px; width: 36px;}
	.cp-badges{left: 17px; bottom: 21px;}
	.cp-wishlist{right: 0; top: -10px;}
	//&:hover{box-shadow: 0 0 30px 0 rgba(0,0,0,0.25); border-color: transparent !important;}
	@media (max-width: @t){
		.cp-btn-addtocart span:before{display: block;}
		.cp-price{font-size: 16px;}
		.cp-save{font-size: 12px; bottom: -5px;}
	}
	@media (max-width: @tp){
		font-size: 14px;
		.cp-col1{width: 170px;}
		.cp-col2{margin: 18px 15px 15px 0;}
		.cp-attr-container{width: 28px;}
		.cp-category{font-size: 12px;}
		.cp-btn-addtocart{
			font-size: 14px; height: 45px;
			span:before{margin-right: 6px; font-size: 20px;}
		}
		.cp-btn-detail{width: 45px; height: 45px;}
		.cp-save{white-space: nowrap;}
		.cp-rate{.scale(1);}
		.cp-wishlist{top: -14px;}
		.cp-wishlist-remove:hover{font-size: 13px;}
		.cp-wishlist-btn{
			span{display: block;}
			&:after{font-size: 20px;}
		}
		.cp-image{height: auto;}
		.cp-price{font-size: 14px;}
	}
	@media (max-width: @m){
		border-left: 0; border-right: 0; font-size: 12px;
		.cp-col1{width: 120px;}
		.cp-col2{margin-top: 15px;}
		.cp-attr-container{display: none;}
		.cp-footer{flex-wrap: wrap;}
		.cp-price{width: 100%; margin-top: 10px;}
		.cp-addtocart{height: 36px; margin-top: 10px; width: 100%;}
		.cp-btn-addtocart{
			width: auto; flex-grow: 1; height: 100%;
			span:before{display: none;}
		}
		.cp-rate{.scale(.9);}
		.cp-brand{
			height: 20px;
			img{max-height: 17px;}
		}
		.cp-wishlist{right: -10px;}
		.cp-wishlist-btn{
			&:after{font-size: 16px;}
			span{display: none;}
		}
		.cp-qty{flex-grow: 0;}
		.cp-category{font-size: 11px;}
		.cp-btn-detail{height: 36px; width: 36px;}
		.cp-save{bottom: -3px;}
		img{margin-bottom: 0;}
	}
}

.cp-related{
	border: 0; border-top: 1px solid @borderColor;
	.cp-col1{
		width: 170px;
		@media (max-width: @l){width: 125px;}
	}
	.cp-col2{
		margin: 15px 20px 20px 20px;
		@media (max-width: @l){margin-left: 0;}
		@media (max-width: @tp){margin-right: 0;}
		@media (max-width: @m){margin-right: 15px; margin-top: 11px;}
	}
	.cp-image{
		height: auto; min-height: 220px;
		@media (max-width: @t){min-height: 150px;}
	}
	.cp-attr-container, .cp-rate, .cp-save{display: none;}
	.cp-btn-addtocart{
		font-size: 0; width: 45px;
		span:before{margin: 0; font-size: 20px;}
	}
	.cp-attr-container{width: 25px;}
	.cp-brand{top: 10px;}
	.cp-wishlist{top: -10px; right: -10px;}
	.cp-wishlist-btn:after{font-size: 20px; line-height: 20px;}
	@media (min-width: @t){
		&:hover{box-shadow: 0 0 30px 0 rgba(0,0,0,0.15);}
	}
	@media (max-width: @t){
		.cp-title{font-size: 12px;}
		.cp-price{font-size: 14px;}
		.cp-wishlist-btn:after{font-size: 16px; line-height: 16px;}
		.cp-wishlist{top: -12px;}
	}
	@media (max-width: @tp){
		.cp-wishlist-btn>span{display: none;}
		.cp-title{font-size: 12px; padding-top: 6px; padding-bottom: 15px;}
		.cp-price{font-size: 16px;}
	}
	@media (max-width: @m){
		.cp-btn-addtocart{font-size: 12px;}
		.cp-price{font-size: 12px;}
		.cp-rate{display: flex; margin: 3px 0 0;}
		.cp-save{font-size: 11px; bottom: 0; display: block; font-weight: normal;}
	}
}

.cp-bought-together{
	width: 100%; border: 0; opacity: 1!important; margin-bottom: 35px;
	.cp-col1{
		width: 130px; margin-right: 20px;
		@media (max-width: @t){width: 100px;}
		@media (max-width: @tp){width: 70px; margin-right: 10px;}
		@media (max-width: @m){width: 100px;}
	}
	.cp-badge-pickup{display: none;}
	.cp-col2{margin: 0;}
	.cp-image{height: 100%;}
	.cp-save, .cp-rate-counter, .cp-addtocart:before{display: none;}
	.cp-rate{position: absolute; top: 4px; right: 0;}
	img{max-height: 150px; margin-bottom: 0;}
	.cp-image img{margin-top: 20px;}
	.cp-footer{margin-top: 35px;}
	.cp-badges{bottom: 5px; left: 0;}
	.cp-brand{height: auto; top: 3px;}
	&:first-child{
		a{pointer-events: none;}
		.cp-qty a{pointer-events: auto;}
	}
	&:last-child{margin-bottom: 0;}
	@media (max-width: @t){
		.cp-title{margin-top: 5px;}
	}
	@media (max-width: @m){align-items: flex-start;}
}
.cp-checkbox{
	margin-left: 5px; font-weight: bold;
	p{padding: 0;}
	input[type=checkbox]+label{
		display: flex; align-items: center; justify-content: center; height: 100%; width: 100%; border: 1px solid @borderColor; padding: 0 15px 0 45px; border-radius: @borderRadius; white-space: nowrap;
		&:before{left: 15px; top: 12px;}
		@media (max-width: @t){font-size: 13px;}
		@media (max-width: @tp){
			padding: 0 10px 0 40px; font-weight: normal;
			span{display: none;}
			&:before{left: 10px;}
		}
		@media (max-width: @m){
			&:before{top: 8px;}
			justify-content: flex-start;
		}
	}
	input[type=checkbox]:checked+label{
		background: #f6f8f1; border-color: @green;
	}
	input[type=checkbox]:disabled:checked+label{
		background: #F9FAF9; border-color: @borderColor; color: #7d8a77;
		&:before{background: #bac1b7; border-color: #bac1b7;}
	}
	@media (max-width: @tp){margin-left: 0;}
	@media (max-width: @m){
		flex-grow: 1;

	}
}

.cp-bought-together-message{font-weight: bold; padding-top: 10px; color: @green;}

.cp-instashop{
	font-size: 12px;
	.cp-image{height: 180px;}
	.cp-save, .cp-attr-container, .cp-feedback{display: none;}
	.cp-cnt{padding: 0 20px 20px;}
	.cp-price{margin: 0 20px 10px; font-size: 14px;}
	.cp-old-price{padding-right: 10px;}
	.cp-brand{
		top: 6px;
		img{max-height: 20px;}
	}
	.cp-addtocart{
		position: relative; height: 51px; box-shadow: none; padding: 0 20px 15px; left: auto; right: auto; top: auto; background: none; opacity: 1;
		&:before{display: none;}
	}
	.cp-btn-addtocart span:before{display: none;}
	.cp-cnt{padding-bottom: 10px; min-height: 76px;}
	.cp-category{font-size: 10px;}
	.cp-badges{left: 20px;}
	.cp-qty{
		width: 75px;
		input{padding: 0 22px; font-size: 12px;}
	}
	.toggle-icon{
		width: 10px; height: 10px;
		&:before{left: 4px;}
		&:after{top: 4px;}
	}
	.cp-rate{display: none;}
	.cp-wishlist{right: 5px; top: 0;}
	.cp-wishlist-btn:before{font-size: 18px; line-height: 18px;}
	.wp-btn-qty{width: 20px;}
	.cp-unavailable-label{font-size: 12px;}

	@media (max-width: @instashopL){
		.cp-image{height: 135px;}
		.cp-cnt{padding: 0 10px 10px;}
		.cp-price{margin: 0 10px 10px;}
		.cp-addtocart{padding: 0 10px 10px; height: 46px;}
		.cp-badges{left: 10px;}
		.cp-qty{width: 45px;}
		.wp-bty-qty{width: 15px;}
		.cp-btn-addtocart{font-size: 12px;}
		.cp-rate{display: none;}
		.cp-brand{
			top: 9px;
			img{max-height: 20px;}
		}
		.cp-wishlist{top: 2px; right: 2px;}
	}
	@media (max-width: @instashopTp){
		.cp-btn-addtocart{
			width: auto; padding: 0 10px; height: 35px;
			span:before{display: block; margin-right: 5px; font-size: 14px;}
		}
	}
	@media (max-width: @m){width: 100%;}
}

/*
.c-special{
	background: #F4F5F3; padding: 20px 40px 40px; border-radius: @borderRadius; margin-top: 15px; margin-bottom: 25px;
	@media (max-width: @l){padding: 20px 30px 30px;}
	@media (max-width: @t){padding: 20px;}
	@media (max-width: @m){margin-left: -15px; margin-right: -15px; padding: 15px 0 15px 15px;}
}

.c-special-title{
	font-size: 28px; color: #274E40; line-height: 1.5; font-weight: bold; padding: 0 0 20px 20px;
	@media (max-width: @t){font-size: 22px; padding-bottom: 10px; padding-left: 30px;}
	@media (max-width: @tp){font-size: 20px;}
	@media (max-width: @m){font-size: 16px; padding-left: 0;}
}
*/
@media (max-width: @t){
	.c-special-items{
		.cp-btn-addtocart span:before{display: none;}
		.cp-qty{
			width: 65px;
			input{padding: 0 20px;}
		}
		.wp-btn-qty{width: 19px;}
		.cp-qty input{font-size: 13px;}
	}
}
@media (max-width: @tp){
	.c-special-items{
		.cp-attr-container img{width: 30px;}
		.cp-image{height: 180px;}
		.cp-save{font-size: 10px;}
	}
	.c-special-items-slider .c-items{
		width: calc(~"100% - -32px"); margin-left: -16px; padding: 1px 16px; overflow-x: auto;
		&::-webkit-scrollbar {-webkit-appearance: none; height: 0;}
		&::-webkit-scrollbar-thumb {background-color: transparent;}
	}
}
@media (max-width: @m){
	.c-special-items{
		flex-wrap: nowrap;
		.cp{width: 140px; flex-shrink: 0; flex-grow: 0;}
		.cp-btn-addtocart>span:before{display: block;}
		.cp-attr-container{display: none;}
		.cp-image{height: 150px;}
	}
	//.c-special-items-slider{overflow: auto; width: calc(~"100vw - 15px"); padding-left: 1px;}
}
/*------- /catalog post -------*/

/*------- catalog detail -------*/
.cd-mini-image{display: none;}
.no-related-products{
	background: #fff;
	.cd-row{
		margin-bottom: 50px;
		@media (max-width: @m){margin-bottom: 0;}
	}
}
.cd-row{background: #fff;}
@media (max-width: @m){
	.cd-wrapper{display: block;}
	.cd-comments{padding: 25px 15px;}
}
.cd-container{
	width: 740px; position: relative;
	@media (max-width: @l){width: 100%;}
}
.cd-col{
	width: 50%;
	@media (max-width: @m){width: 100%;}
}
.cd-col1{
	@media (max-width: @m){order: 2;}
}
.cd-col1 .cd-container{
	margin-left: auto;
	@media (max-width: @l){margin-left: 12%; width: 88%;}
	@media (max-width: @t){
		margin-left: 30px; width: calc(~"100% - 30px");
		&.cd-container-images{margin-left: 0;}
	}
	@media (max-width: @tp){
		margin-left: 20px; width: calc(~"100% - 20px");
	}
	@media (max-width: @m){margin: 0; width: 100%;}
}
.cd-col2 .cd-container{
	margin-right: auto; padding-left: 80px;
	@media (max-width: @l){padding-right: 12%; padding-left: 50px;}
	@media (max-width: @t){padding-right: 30px; padding-left: 30px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px;}
	@media (max-width: @m){padding: 0;}
}
.cd-col2{
	border-left: 1px solid @borderColor; 
	@media (max-width: @m){margin: 0; order: 1; border: none;}
}
.cd-title{
	font-size: 22px; line-height: 1.5; padding: 0 0 3px;
	@media (max-width: @t){font-size: 18px;}
	@media (max-width: @tp){font-size: 16px;}
	@media (max-width: @m){font-size: 14px;}
}
.cd-category{
	font-size: 12px; padding-bottom: 10px;
	a{
		color: @green;
		&:hover{text-decoration: none;}
	}
	@media (max-width: @m){padding-bottom: 5px;}
}
.cd-info{
	display: flex; align-items: center; font-size: 12px; line-height: 1.4; color: @gray2; margin-bottom: 5px;
	@media (max-width: @m){font-size: 11px; margin-bottom: 10px;}
}
.cd-info-item{
	margin-right: 10px;
	&:last-of-type{margin-right: 0;}
}
.cd-code{
	position: relative; padding-right: 13px; margin-right: 10px;
	&:after{.pseudo(1px,12px); background: @gray; top: 2px; right: 0;}
	&.no-feedback:after{display: none;}
	@media (max-width: @m){margin-right: 7px; padding-right: 10px;}
}

.cd-loyalty-price{
	position: relative; padding-left: 140px; margin: 15px 0 20px;
	&:before{.pseudo(125px,80px); background: url(images/card-small-hr.png) no-repeat left top; top: 0; left: 0; background-size: contain;}
	&:lang(de):before{background: url(images/card-small-de.png) no-repeat left top;}
	&:lang(en):before{background: url(images/card-small-en.png) no-repeat left top;}
	&:lang(sl):before{background: url(images/card-small-si.png) no-repeat left top;}
	@media (max-width: @t){
		padding-left: 115px;
		&:before{width: 100px;}
	}
	@media (max-width: @tp){
		padding-left: 0;
		&:before{display: none;}
	}
}
.cd-loyalty-club{font-size: 12px; line-height: 1.2;}
.cd-price-container{
	font-size: 28px; line-height: 1.5;
	@media (max-width: @t){font-size: 22px;}
	@media (max-width: @m){font-size: 18px;}
}
.cd-tax{
	font-size: 12px; line-height: 1.5;
	@media (max-width: @m){font-size: 11px;}	
}
.cd-price{display: flex; align-items: center; flex-wrap: wrap; padding-bottom: 5px;}
.cd-old-price{
	font-weight: normal; font-size: 12px; line-height: 1.3; position: relative; top: 2px; width: 100%;
	span{text-decoration: line-through;}
}
.cd-current-price{
	font-weight: bold;
	ins{
		font-weight: normal; color: transparent; padding: 0; border-right: 1px solid @textColor; display: inline-block; width: 1px; height: 22px; margin: 0 5px; position: relative; top: 10px;
		@media (max-width: @t){height: 17px; top: 8px;}
		@media (max-width: @m){height: 14px;}
	}
}
.cd-loyalty{
	background: #F9FAF9; margin-right: 50px; margin-bottom: 32px; padding: 12px 0 12px 24px; font-size: 14px; line-height: 1.5; border: 1px solid @borderColor; border-radius: @borderRadius; margin-top: 19px; position: relative; min-height: 68px; display: flex; align-items: center;
	//&:before{.pseudo(176px,108px); border: 1px solid red; right: -35px; top: -23px;} 
	&:after{.pseudo(198px,148px); background: url(images/card-hr.png) no-repeat left top; background-size: contain; top: -40px; right: -51px;}
	&:lang(de):after{background: url(images/card-de.png) no-repeat left top;}
	&:lang(en):after{background: url(images/card-en.png) no-repeat left top;}
	&:lang(sl):after{background: url(images/card-si.png) no-repeat left top;}
	a{
		color: @green;
		&:hover{color: @green/1.2;}
	}
	@media (max-width: @l){
		padding-left: 25px;
		&:after{right: -100px; top: -35px;}
	}
	@media (max-width: @t){
		padding: 14px 0 12px 17px; //margin-bottom: 0;
		&:after{width: 155px; height: 115px; top: -25px; right: -55px;}
	}
	@media (max-width: @tp){
		margin: 20px -20px 0; border: 0; padding-left: 100px;
		&:after{width: 65px; height: 50px; right: auto; left: 20px; top: 10px;}
	}
	@media (max-width: @m){
		border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; font-size: 12px; padding: 12px 90px 12px 16px; margin-left: -15px; margin-right: -15px; border-radius: 0;
		&:after{left: auto; right: 16px;}
	}
}
.loyalty-cnt{
	padding-bottom: 15px; max-width: 280px; position: relative;
	p{padding-bottom: 0;}
	span{color: @red; font-weight: bold;}
	span.green{color: @green;}
	@media (max-width: @t){max-width: 200px;}
	@media (max-width: @tp){max-width: none; padding-right: 20px;}
	@media (max-width: @m){padding-bottom: 5px;}
}
.loyalty-checkbox{
	font-weight: bold; max-width: 380px;
	input[type=checkbox]+label{
		padding-top: 0; padding-left: 68px; display: flex; align-items: center; font-weight: normal;
		span{font-weight: bold; padding-left: 5px;}
		&:before{.pseudo(52px,30px); background: #E0E3DF; border-radius: 16px; top: auto; left: 0; border: none;}
		&:after{.pseudo(24px,24px); background: #fff; border-radius: 50%; left: 3px; box-shadow: 0 4px 2px rgba(0,0,0,0.2); .transition(left);}
		p{padding-bottom: 0;}	
	}
	input[type=checkbox]:checked+label{
		&:before{background: @lightGreen;}
		&:after{left: 25px;}
	}
	@media (max-width: @tp){
		padding-right: 20px;
		input[type=checkbox]+label{font-size: 13px;}
	}
	@media (max-width: @m){		
		input[type=checkbox]+label{
			font-size: 12px; align-items: flex-start;
			&:before{width: 45px; height: 26px;}
			&:after{width: 20px; height: 20px; top: 3px; left: 3px;}
		}
		input[type=checkbox]:checked+label:after{left: 22px;}
	}
	input[type=checkbox]+label:hover:before{border-color: @green;}
}

.cd-brand{
	position: absolute; left: 0; right: 0; top: 31px; z-index: 10; text-align: center;
	img{width: auto; height: auto;}
	@media (max-width: @t){top: 20px;}
	@media (max-width: @tp){
		img{max-height: 22px;}
	}
	@media (max-width: @m){right: auto; left: 15px; top: 0; margin: 0!important;}
}
.multiple-images{
	.cd-brand, .cd-nav, .cd-badges{
		margin-left: 120px;
		@media (max-width: @l){margin-left: 100px;}
		@media (max-width: @t){margin-left: 10px;}
		@media (max-width: @tp){margin-left: 60px;}
	}
}
.cd-rates{
	a{text-decoration: none;}
	@media (max-width: @m){position: relative; top: 1px;}
}
.cd-images{
	display: flex; width: 100%;
	@media (max-width: @m){flex-direction: column-reverse;}
}
.cd-thumbs{
	width: 120px; flex-grow: 0; flex-shrink: 0; display: flex; flex-wrap: wrap; align-self: flex-start;
	@media (max-width: @l){width: 100px;}
	@media (max-width: @t){width: 70px; margin-left: 10px;}
	@media (max-width: @tp){width: 60px;}
	@media (max-width: @m){
		width: 100%; display: flex; flex-wrap: wrap; justify-content: flex-start; margin-top: 8px; margin-left: 0; padding: 0 16px;
		.swiper-navigation{display: none;}	
	}
}
.cd-thumb{
	height: 120px; width: 100%; display: flex; align-items: center; justify-content: center; position: relative; z-index: 10;
	img{max-height: 100%; width: auto;}
	&:before{.pseudo(2px,auto); top: 30px; bottom: 30px; left: 0; opacity: 0; .transition(opacity);}
	&:hover:before{opacity: 1;}
	&.active{
		&:before{background: @green; opacity: 1;}
	}
	@media (max-width: @l){
		height: 100px;
		&:before{top: 20px; bottom: 20px;}
	}
	@media (max-width: @t){
		height: 80px;
		&:before{top: 15px; bottom: 15px;}
	}
	@media (max-width: @tp){
		height: 60px;
	}
	@media (max-width: @m){
		width: 20%; max-width: 60px;
		&:before{width: auto; height: 2px; top: 0; bottom: auto; left: 5px; right: 5px;}
	}
}
.cd-hero-image{
	display: flex; align-items: center; justify-content: center; flex-grow: 1; width: calc(~"100% - 125px");
	img{width: auto; height: auto; display: block; margin: auto;}
	/*
	@media (max-width: @m){
		height: 350px;
		img{max-height: 350px; width: auto;}
	}
	*/
	@media (max-width: @m){
		width: 100%;
	} 
}
.cd-hero-slider{
	width: 100%;
	/*
	@media (max-width: @l){width: 430px;}
	@media (max-width: @t){width: 400px;}
	@media (max-width: @tp){width: 290px;}
	@media (max-width: @m){width: 100%;}
	*/
	@media (max-width: @m){
		.swiper-wrapper{height: 360px;}
	}
}


.cd-cnt{
	background: #f9faf9; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; padding-top: 45px; padding-bottom: 45px;
	table{
		background: #fff; border: 1px solid @borderColor; width: 100%!important; font-size: 14px;
		td, th{padding: 10px 25px;}
	}
	@media (max-width: @t){padding-top: 25px;}
	@media (max-width: @tp){
		table{
			font-size: 12px;
			td, th{padding: 8px 15px;}
		}
	}
	@media (max-width: @m){padding: 0; background: none; border: none;}
}
@media (max-width: @m){	
	.cd-tab-cnt{display: none; padding: 0 16px 22px;}
}
.cd-tab-nutrition{
	td:first-child{width: 240px;}
	@media (max-width: @tp){
		td:first-child{width: 50%;}
	}
	@media (max-width: @m){
		table, tr, td, tbody{display: block; width: 100%!important;}
		table{border-bottom: 0;}
		tr{border-bottom: 1px solid @borderColor; padding: 6px 10px;}
		td{
			border: 0; padding: 0!important;
			&:first-child{font-weight: bold;}
		}
	}
}
.cd-tab-title{
	font-size: 22px; padding: 0 0 15px;
	@media (max-width: @m){
		position: relative; font-size: 14px; line-height: 20px; padding: 14px 35px 14px 16px;
		&:before{.pseudo(12px,2px); background: @lightGreen; top: 24px; right: 16px;}
		&:after{.pseudo(2px,12px); background: @lightGreen; top: 19px; right: 21px; display: block;}
		&.active{
			color: @lightGreen;
			&:after{display: none;}
		}
	}
}
.cd-tab{
	padding-bottom: 40px;
	@media (max-width: @m){
		font-size: 12px; padding-bottom: 0; border-top: 1px solid #E9ECEB; border-bottom: 1px solid #E9ECEB; margin-top: -1px;
		ul{margin-left: 0;}
		.lists ul li:before{top: 8px;}
		&.active{
			.cd-tab-cnt{display: block;}
		}
	}
}
.cd-share{
	padding-top: 0;
	@media (max-width: @m){padding: 22px 16px 0; margin-bottom: 25px;}	
}
.cd-comments-title{
	font-size: 22px; padding-top: 35px;
	@media (max-width: @m){font-size: 16px; padding-top: 0;}
}
.cd-attr-container{
	position: absolute; top: 25px; right: 30px; z-index: 10;
	@media (max-width: @t){right: 0; top: 20px;}
	@media (max-width: @tp){top: 15px; right: -5px;}
	@media (max-width: @m){position: relative; top: auto; right: auto; display: flex; flex-wrap: wrap; margin-bottom: 10px;}
}
.cd-attr{
	position: relative; display: flex; align-items: center; text-align: right; font-size: 12px; font-weight: bold; margin-bottom: 10px;
	@media (max-width: @m){
		flex-direction: row-reverse; margin: 0 16px 16px 0;
	}
}
.cd-attr-img{
	width: 45px; flex-grow: 0; flex-shrink: 0; margin-left: 12px;
	img{display: block;}
	@media (max-width: @t){
		width: 35px; margin-left: 8px;
	}
	@media (max-width: @m){
		width: 30px; margin-left: 0; margin-right: 8px;
	}
}
.cd-attr-title{flex-grow: 1;}
.cd-related{
	/*border-top: 1px solid @borderColor; padding-bottom: 20px;*/
	.cd-container{
		padding-right: 45px; padding-top: 40px;
		@media (max-width: @t){padding-right: 30px; padding-top: 24px;}
		@media (max-width: @tp){padding-right: 20px;}
		@media (max-width: @tp){padding-top: 30px;}
		@media (max-width: @m){padding: 15px;}
	}
	.subtitle{
		font-size: 22px;
		@media (max-width: @t){font-size: 18px;}
		@media (max-width: @tp){font-size: 16px;}
		@media (max-width: @m){padding-left: 0;}
	}
	@media (max-width: @m){border: 0;}
}
.cd-related-posts{
	padding-bottom: 30px;
	@media (max-width: @tp){padding-bottom: 24px;}	 
}

.cd-related-package{
	padding: 50px 0 0;
	@media (max-width: @m){padding: 16px 0 0;}
}
.cd-related-package-title{
	font-weight: bold; font-size: 17px; line-height: 1.4; padding: 0 0 20px;
	@media (max-width: @tp){font-size: 16px; padding: 0 0 14px;}
	@media (max-width: @m){font-size: 14px; padding: 0 0 8px;}
}
.cd-related-package-items{
	display: flex; flex-wrap: wrap;
	@media (max-width: @m){gap: 8px;}
}
.cd-related-package-item{
	display: block; text-align: center; text-decoration: none; width: 140px; max-width: calc(~"33% - 10px"); padding: 20px 10px; border: 1px solid @borderColor; margin: 0 10px 10px 0; border-radius: 2px; font-size: 16px; line-height: 1.3; position: relative; color: @textColor; .transition(all);
	@media (max-width: @tp){max-width: calc(~"50% - 10px"); width: 100%;}
	/*
	&:before{
		.pseudo(auto,auto); top: -1px; left: -1px; right: -1px; bottom: -1px; .gradient-green; border-radius: 2px; opacity: 0; .transition(opacity);
		@media (max-width: @t){display: none;}
	}
	*/
	&:hover{
		text-decoration: none; color: @textColor; border-color: @green;
		.cd-related-package-unit{color: @green;}
		/*
		&:before{opacity: 1;}
		.cd-related-package-discount-price{
			color: #fff;}
		@media (max-width: @t){color: @textColor;
			.cd-related-package-discount-price{color: @red;}
		}
		*/
	}
	@media (max-width: @tp){padding: 13px 0 6px; font-size: 14px;}
	@media (max-width: @m){margin: 0 0 10px; width: 100%; max-width: 100%; padding: 10px 15px;}
}
.cd-related-package-item-inner{
	@media (max-width: @m){display: flex; align-items: center;}
}
.cd-related-package-unit{
	padding: 0 0 5px; font-weight: bold; position: relative; z-index: 1; .transition(all);
	@media (max-width: @tp){padding: 0 0 2px;}
	@media (max-width: @m){padding: 0; margin-right: 20px; min-width: 50px; text-align: left;}
}
.cd-related-package-price{
	font-size: 14px; position: relative; z-index: 1;
	@media (max-width: @tp){font-size: 12px;}
	@media (max-width: @m){font-size: 14px; margin-left: auto; text-align: right;}
}
.cd-related-package-old-price{
	opacity: .6; font-size: 12px; padding-bottom: 1px; text-decoration: line-through; padding-right: 3px;
	@media (max-width: @tp){font-size: 11px;}
	@media (max-width: @m){font-size: 12px; margin-right: 2px;}
}
.cd-related-package-price-label{text-decoration: none;}
.cd-related-package-discount-price{font-weight: bold; color: @red; .transition(all);}
.cd-related-package-attr{
	position: absolute; color: #fff; border-radius: 2px; background: #75B900; top: -8px; left: 5px; right: 5px; font-size: 10px; line-height: 1.3; padding: 5px; text-align: center; display: flex; align-items: center; justify-content: center;
	&:before{
		.icon-organic(); font: 13px/13px @fonti; color: #fff; margin-right: 5px;
		@media (max-width: @tp){font-size: 9px; line-height: 9px; margin-right: 3px;}
		@media (max-width: @m){font-size: 12px; line-height: 12px; margin-right: 6px;}
	}
	@media (max-width: @tp){font-size: 9px; padding: 2px; top: -5px;}
	@media (max-width: @m){position: relative; top: auto; right: auto; bottom: auto; left: auto; font-size: 10px; padding: 5px 8px;}
}
.cd-related-flavor{
	.cd-related-package-item{
		width: auto; display: flex; align-items: center; max-width: none; padding: 7px 13px 7px 85px; text-align: left; min-height: 77px;
		@media (max-width: @tp){width: 100%; margin: 0 0 10px; padding: 7px 10px; min-height: 0;}
		@media (max-width: @m){width: auto; flex-grow: 0; flex-shrink: 1; padding: 8px 16px 8px 4px; min-height: 56px; margin: 0;}  
	}
	.cd-related-package-price{
		@media (max-width: @tp){font-size: 14px;}
		@media (max-width: @m){font-size: 12px; line-height: 16px; text-align: left; margin-left: 0;}
	}
	.cd-related-package-unit{
		font-size: 14px;
		@media (max-width: @tp){flex-grow: 1; padding: 0;}
		@media (max-width: @m){font-size: 12px; line-height: 16px;}
	}
	.cd-related-package-old-price{
		padding-right: 6px;
		@media (max-width: @tp){font-size: 12px;}
	}
	.cd-related-package-item-inner{
		@media (max-width: @tp){display: flex; width: 100%; align-items: center;}
		@media (max-width: @m){display: flex; flex-flow: column; align-items: flex-start; justify-content: flex-start; padding-left: 46px;}
	}
}
.cd-related-package-image{
	position: relative; z-index: 1; position: absolute; left: 10px; top: 7px; width: 60px; justify-content: center; flex-grow: 0; flex-shrink: 0;
	img{
		display: block; margin: auto; max-height: 60px; width: auto;
		@media (max-width: @tp){max-height: 40px;}
	}
	@media (max-width: @tp){position: relative; top: auto; left: auto; width: 40px; margin-right: 15px;}
	@media (max-width: @m){position: absolute; top: auto; left: 4px;}
}

.cd-nav-m{
	display: none;
	@media (max-width: @m){display: inline;}
}
.cd-nav{
	display: flex; justify-content: center; list-style: none; padding: 25px 0 0; margin: 0; font-size: 14px; line-height: 1.5;
	li{
		position: relative; padding-right: 10px; margin-right: 10px;
		&:after{.pseudo(1px,12px); right: 0; top: 6px; background: @borderColor;}
		&:last-child{
			margin-right: 0; padding-right: 0;
			&:after{display: none;}
		}
		&>span{
			cursor: pointer; .transition(color);
			&>p{padding-bottom: 0;}
		}
	}
	a:hover, li>span:hover{color: @green;}
	@media (max-width: 1370px){font-size: 12px;}
	@media (max-width: @tp){
		font-size: 12px;
		li{
			margin-right: 8px; padding-right: 8px;
			&:after{top: 4px;}
		}
	}
	@media (max-width: @m){
		margin: 5px 0 0!important; padding: 10px 15px; display: flex; flex-wrap: wrap; justify-content: space-between; border-top: 1px solid @borderColor;
		li{
			margin-right: 0; padding: 3px 0; width: 50%;
			&:after{display: none;}
		}
		.d{display: none;}
		.m{text-transform: capitalize;}
	}
}

.cd-bought-together{
	border-bottom: 1px solid @borderColor; padding-bottom: 24px;
	.cd-container{padding-right: 80px;}
	@media (max-width: @l){
		.cd-container{padding-right: 45px;}	
	}
	@media (max-width: @t){
		margin-bottom: 20px;
	}
	@media (max-width: @tp){
		.items-list{margin-left: -10px;}
		margin-bottom: 10px;
	}
	@media (max-width: @m){
		margin: -1px 0 0; padding: 0;
		.cd-container{padding: 0 16px 22px;}
	}
}
.bought-together-title{
	border-bottom: 1px solid @borderColor; margin-bottom: 25px;
	p{padding-bottom: 0;}
}
.cd-bought-together-footer{
	border-top: 1px solid @borderColor; display: flex; align-items: flex-end; padding-top: 20px; padding-left: 150px; margin-top: 20px; font-size: 16px; line-height: 1.4;
	@media (max-width: @l){font-size: 15px;}
	@media (max-width: @t){padding-left: 0; font-size: 14px;}
	@media (max-width: @tp){font-size: 13px;}
	@media (max-width: @m){flex-direction: column; text-align: left; align-items: flex-start; padding-left: 100px; position: relative;}
}
.choosen-info{flex-grow: 1;}
.choosen-saving{color: @red; font-size: 12px; font-weight: bold; padding-top: 3px;}
.btn-bought-together-add{
	padding: 0; width: 258px;
	span{
		display: flex; align-items: center;
		&:before{.icon-cart(); font: 22px/22px @fonti; margin-right: 15px; position: relative; top: -1px;}
	}
	@media (max-width: @l){width: 235px;}
	@media (max-width: @t){width: 230px;}
	@media (max-width: @tp){
		width: 180px;
		span:before{display: none;}
	}
	@media (max-width: @m){width: 100%; margin-top: 10px;}
}
.choosen-info-total{
	font-size: 12px;
	.counter{color: @green; font-weight: bold; margin-left: 3px;}
	@media (max-width: @m){position: absolute; left: 0; top: 22px;}
}
.cd-bought-together-list-message{
	background: @green; color: #fff; border-radius: @borderRadius; font-size: 14px; padding: 6px 15px; margin-top: 20px; margin-left: 150px; display: none;
	&.active{display: block;}
	@media (max-width: @t){margin-left: 0;}
}
.chosen-total-saving{display: block; font-size: 12px; color: @red; font-weight: 700; margin-top: 2px;}

.cd-rp{
	border-top: 1px solid @borderColor; padding-top: 80px; padding-bottom: 80px; content-visibility: auto; contain-intrinsic-size: 750px;
	@media (max-width: @l){padding-top: 60px;}
	@media (max-width: @t){background: url(images/bg.jpg); padding-left: 30px; padding-right: 30px;}
	@media (max-width: @tp){padding-top: 45px; padding-left: 20px; padding-right: 20px;}
	@media (max-width: @m){padding: 32px 0 25px; max-width: 100%; overflow: hidden;}
}
.cd-rp-title{
	font-size: 32px; font-weight: bold; text-align: center; padding: 0 0 30px; display: block;
	@media (max-width: @t){font-size: 26px; padding-bottom: 20px;}
	@media (max-width: @tp){font-size: 22px; padding-bottom: 15px;}
	@media (max-width: @m){font-size: 20px; text-align: left; padding-left: 16px; padding-bottom: 12px;}
}
.cd-rp-slider{
	//height: 0; overflow: hidden;
	//&.slick-initialized{overflow: visible; height: auto;}
	.slick-arrow{top: 112px;}
	.slick-next{right: -90px;}
	.slick-prev{left: -90px;}
	@media (max-width: @l){
		.slick-next{right: -65px;}
		.slick-prev{left: -65px;}
	}
	@media (max-width: @t){
		width: calc(~"100% - 5px");
		.slick-arrow{
			width: 40px; height: 40px; background: #fff; box-shadow: 0 0 30px 0 rgba(0,0,0,0.25);
			&:before{font-size: 16px; line-height: 1;}
		}
		.slick-next{right: -20px;}
		.slick-prev{left: -20px;}
	}
	@media (max-width: @tp){
		.slick-next{right: -15px;}
		.slick-prev{left: -15px;}
	}
	@media (max-width: @m){
		/*
		height: auto; overflow: visible; display: flex; overflow-x: auto;
		.cp{
			width: 140px; flex-shrink: 0; flex-grow: 0;
			&:first-child{margin-left: 15px;}
		}
		*/
	}
}
.cd-header{
	padding: 20px 0 60px;
	@media (max-width: @t){padding-bottom: 40px;}
	@media (max-width: @tp){padding-bottom: 25px;}
	@media (max-width: @m){padding: 15px;}
}
@media (max-width: @m){
	.cd-m-header{padding: 15px;}
	.cd-container-header{display: flex; flex-direction: column;}
	.cd-m-header-top{display: none;}
	.cd-m-header-placeholder{
		min-height: 100px;
		.cd-m-header{display: block;}
	}
}
.cd-header-placeholder{display: none;}

.fixed-footer{
	overflow: hidden; position: fixed; left: 0; right: 0; bottom: 0; background: #fff; z-index: 48; border-top: 1px solid @borderColor; padding: 0;
	.cd-header{
		padding: 0;
		.cd-container{width: auto; padding: 10px 0; width: var(--pageWidth); margin: auto; display: flex; align-items: center;}
	}
	.cd-item-status-stores,.cd-stores-availability,.cd-webshop-only{display: none;}
	.cd-badge-pickup{display: none !important;}
	.cd-title{
		flex-grow: 1; padding: 0 200px 0 0; font-size: 14px;
		@media (max-width: @l){padding-right: 20px; font-size: 12px;}
	}
	.cd-price{
		font-size: 20px; flex-wrap: wrap; justify-content: flex-end;
		@media (max-width: @t){font-size: 16px;}
	}
	.cd-lowest-price{font-size: 9px;}
	.cd-loyalty-price{
		padding: 0;
		&:before{display: none;}
	}
	.cd-old-price{order: 1; padding: 0; display: block; width: 100%;}
	.cd-add-container{padding: 0;}
	.cd-current-price{
		order: 2;
		ins{height: 17px; top: 6px;}
	}
	.cd-btn-add{width: 230px;}
	.cd-price-container{
		flex-shrink: 0; flex-grow: 0; margin-right: 60px; text-align: right;
		@media (max-width: @l){margin-right: 10px;}	
	}
	.cd-qty{
		width: 110px;
		.wp-btn-qty{width: 30px;}
		.wp-input-qty, .qty-input{padding: 0 30px;}
	}
	.cd-header-placeholder{display: block;}
	.cd-wishlist{
		margin-right: 0; margin-left: 8px; min-width: 200px;
		@media (max-width: @l){margin-left: 10px;}
	}
	.cd-wishlist-btn{
		padding: 0 20px;
		@media (max-width: @t){
			&>span{display: none;}
		}	
	}
	.product-in-wishlist{
		top: 4px; right: 100%; width: auto;
		&:after{top: calc(~"50% - 5px"); left: auto; right: -3px;}	
		@media (max-width: @t){top: 7px;}
	}
	.cd-m-header{display: flex; align-items: center; flex-grow: 1;}
	.cd-mini-image{
		display: block; margin-right: 10px; width: 70px; text-align: center; flex-shrink: 0; flex-grow: 0;
		img{display: block; margin: auto;}
	}
	.cd-change-delivery{margin: 0;}
	.cd-loyalty, .cd-order-info, .cd-info, .bc, .cd-category, .cd-discount-badge, .cd-save, .cd-unit, .cd-tax:not(.cd-lowest-price), .cd-unavailable, .cd-related-package{display: none;}
	.cd-loyalty-price{margin: 0;}
	.cd-loyalty-club{display: none;}

	@media (max-width: @t){
		.cd-header .cd-container{padding-left: 30px; padding-right: 30px;}
		.cd-wishlist{
			min-width: 0; width: 50px; height: 50px; margin-left: 10px; flex-grow: 0; flex-grow: 0; padding: 0;
			&>span{display: block;}
			span span{visibility: hidden;}
		}
		.cd-wishlist-btn{
			padding: 0; height: 50px;
			&:before{left: 13px; top: 15px; margin: 0;}
		}
		.cd-mini-image{
			width: 45px;
			img{max-height: 50px; width: auto;}
		}
		.cd-qty, .cd-btn-add{height: 50px;}
		.cd-btn-add{
			//width: 150px;
			span:before{display: none;}
		}
		.cd-header.not-available{
			.cd-price-container{font-size: 16px; width: auto; padding-right: 20px;}
		}
		.cd-add-container.not-available .cd-wishlist{width: 50px;}
	}
	@media (max-width: 1080px){
		.cd-add-container{flex-wrap: inherit;}
	}
	@media (max-width: @tp){
		.cd-btn-add{
			width: 110px;
			.cd-btn-add-l1{display: none;}
			.cd-btn-add-l2{display: block;}
		}
		.cd-header .cd-container{padding-left: 20px; padding-right: 20px;}
		.cd-qty{width: 90px;}
		.cd-add-container{flex-wrap: nowrap;}
		.cd-price-container{width: 130px;}
		.cd-title{padding-right: 30px;}
	}
	@media (max-width: @m){
		.cd-m-header{display: none;}
		.cd-container-header{flex-direction: row;}
		.cd-header .cd-container{align-items: center; padding-left: 15px; padding-right: 15px;}
		.cd-header.not-available .cd-price-container{flex-grow: 1;}
		.cd-add-container.not-available .cd-wishlist{margin: 0;}
		.cd-btn-add{
			width: 50px; flex-grow: 0; flex-shrink: 0; font-size: 0; border-top-left-radius: 0; border-bottom-left-radius: 0;
			span:before{display: block; margin: 0;}
		}
		.add-to-cart-container{width: auto;}
		.cd-add-container{flex-direction: row-reverse;}
		.cd-qty-limit .cd-btn-add{border-radius: @borderRadius;}
		.cd-qty, .cd-btn-add{height: 42px;}
		.cd-btn-add{width: 42px;}
		.cd-qty{margin-right: 0; border-right: 0;}
		.cd-wishlist{margin: 0; height: 42px;}
		.cd-price-container{width: 120px; flex-grow: 1; margin-right: 10px;}
		.cd-wishlist-btn:before{top: 12px;}
		.cd-current-price{
			font-size: 13px;
			ins{height: 10px; top: 5px;}
		}
		.cd-change-delivery{
			margin: 0; margin-left: 15px; width: 125px; text-align: right; font-size: 11px; line-height: 15px; padding: 0;
			&:before{display: none;}
		}

		.cd-mini-image, .cd-title{display: none;}

		.cd-wishlist-btn{width: 42px; height: 42px}
	}
}
.page-catalog-detail.fixed-cd-header{
	.fixed-footer{.translate(0,-130px);  .transition(transform);}
	@media (max-width: @m){
		.ontop{bottom: 90px;}
		.zEWidget-launcher{bottom: 135px!important;}
	}
}
@media (max-width: @m){
	.page-catalog-detail .ontop{bottom: 90px;}
}

@media (min-width: @m){
	.ftr, .page-catalog-detail.fixed-cd-header{
		.zEWidget-launcher{
			bottom: 100px!important; .transition(all);
			@media (max-width: @t){left: 40px!important; bottom: 80px!important;}
			@media (max-width: @tp){left: inherit!important;}
		}
	}
}

.cd-bc{
	padding: 0 0 10px;
	@media (max-width: @m){padding: 0 0 10px;}
}
.cd-wishlist{
	margin-left: 8px; margin-right: 0; font-size: 14px; display: flex; flex-grow: 1; flex-shrink: 1; position: relative;
	a, .cd-wishlist-btn{text-decoration: none;} 
	&.active{
		.cd-wishlist-add{display: none;}
		.cd-wishlist-remove{display: flex;}
	}
	@media (max-width: @t){font-size: 12px;}
	@media (max-width: 1080px){
		width: 100%; margin: 0; padding-left: 110px; padding-top: 15px; text-align: center; display: flex; align-items: center; justify-content: center;
		&>span{display: inline-block; margin: auto;}
	}
}
.cd-wishlist-btn{
	position: relative; align-items: center; border: 1px solid #DEDEDE; border-radius: 2px; height: 54px; display: flex; align-items: center; justify-content: center; width: 100%; cursor: pointer; transition: border-color 0.3s, color 0.3s;
	&:before{.icon-heart(); font: 20px/1 @fonti; color: @lightGreen; margin-right: 11px;}
	@media (min-width: @h){
		&:hover{border-color: @lightGreen; color: @lightGreen;}
	}
	@media (max-width: 1080px){border: none; height: auto;}
	@media (max-width: @t){
		//padding-left: 28px;
		&:before{font-size: 19px; line-height: 19px;}
	}
}
.cd-wishlist-remove{
	display: none;
	&:before{.icon-heart-full(); top: 0;}
	@media (max-width: @t){
		&:before{top: -2px;}
	}
}
.cd-order-info{
	display: flex; justify-content: space-between; font-size: 12px; line-height: 1.5; padding-top: 40px;
	&.not-available{display: none;}
	@media (max-width: @tp){flex-wrap: wrap; padding-top: 30px;}
	@media (max-width: @m){font-size: 11px; padding-top: 25px; padding-bottom: 8px;}
}
.cd-item-status-stores{
	font-size: 14px; line-height: 20px; font-weight: bold; padding: 16px 25px; border: 1px solid @red; width: 100%; color: #375126; position: relative; margin-top: 60px; border-radius: 2px; margin-bottom: 20px;
	span{color: @red;}
	@media(max-width: @tp){padding: 12px; font-size: 12px; line-height: 18px;}
	@media(max-width: @m){order: 2; margin-top: 40px;}
}

.status-info-label{
	position: absolute; top: -30px; background: @red; min-height: 30px; display: flex; align-items: center; justify-content: center; padding: 0 16px 0 38px; font-size: 14px; line-height: 16px; color: white; font-weight: bold; border-radius: 2px 2px 0 0; left: -1px;
	&:before{.pseudo(auto,auto); .icon-danger-white2(); font: 16px/16px @fonti; color: white; left: 12px; top: 6px;}
	@media(max-width: @tp){
		top: -24px; min-height: 24px; font-size: 12px; line-height: 14px; padding: 0 12px 0 30px;
		&:before{font: 14px/14px @fonti; left: 8px; top: 5px;}
	}
}

.cd-change-delivery{
	position: relative; padding-left: 26px; font-size: 14px; line-height: 20px; display: flex; flex-flow: column; margin-top: 22px;
	.cd-change-delivery-info{padding-bottom: 1px;}
	&:before{.pseudo(auto,auto); .icon-pickup(); font: 22px/22px @fonti; color: @red; top: 0; left: 0;}
	a, .btn-autochange{
		color: @green; text-decoration: underline; text-underline-offset: 2px;
		@media (min-width: @t){
			&:hover{color: @darkGreen;}
		}
	}
	@media(max-width: @m){
		order: 3; font-size: 12px; line-height: 16px; margin-top: 18px;
		.cd-change-delivery-info{padding-bottom: 0;}
	}
}

.cd-webshop-only{
	background: #F9FAF9; padding: 16px 25px 16px 64px; border: 1px solid #E0E3DF; border-radius: 2px; position: relative; font-size: 12px; line-height: 16px; color: #3E5C28; margin-top: 40px;
	&:before{.pseudo(auto,auto); .icon-webshop-only(); font: 32px/32px @fonti; color: @orange; left: 20px; top: 50%; transform: translateY(-50%);}
	a{text-decoration: underline; text-underline-offset: 2px; text-decoration-color: #3E5C28;}
	h4{color: @orange; font-size: 14px; line-height: 20px; font-weight: bold; padding-top: 0; padding-bottom: 2px;}
	@media (max-width: @m){
		order: 3; margin-top: 20px; padding: 14px 25px 14px 59px;
		&:before{left: 16px; top: 16px; transform: unset;}
		h4{font-size: 12px; line-height: 16px;}
	}
}
.cd-stores-availability{
	display: inline-flex; background: #F9FAF9; border: 1px solid #e0e3df; border-radius: 2px; min-height: 54px; align-items: center; justify-content: center; padding: 0 57px; margin-top: 40px; text-decoration: underline; text-underline-offset: 2px; font-size: 14px; line-height: 20px; font-weight: bold; cursor: pointer; .transition(color);
	@media (min-width: @h){
		&:hover{color: @lightGreen;}
	}
	span{
		position: relative; padding-left: 34px; padding-right: 14px;
		&:before{.pseudo(auto,auto); .icon-store(); font: 20px/20px @fonti; top: 1px; color: @green; left: 0;}
		&:after{.pseudo(auto,auto); .icon-arrow-down(); font: 7px/7px @fonti; transform: rotate(-90deg); color: @green; right: 0; top: 7px;}
	}
	@media(max-width: @t){width: 100%; padding: 0 30px;}
	@media(max-width: @tp){min-height: 45px; font-size: 12px; line-height: 15px; padding: 0 16px;
		span{
			&:before{top: -2px;}
			&:after{top: 5px;}
		}
	}
	@media(max-width: @m){margin-top: 20px;}
}
.cd-delivery{
	position: relative; padding: 0 0 0 58px;
	&:before{.icon-shipping(); color: @green; font: 27px/27px @fonti; position: absolute; top: 5px; left: 0;}
	p{padding: 0;}
	strong{color: @green;}
	@media (max-width: @t){
		padding-left: 0;
		&:before{display: none;}
	}
	@media (max-width: @tp){
		width: 100%; margin-bottom: 15px; padding-left: 55px;
		&:before{display: block; font-size: 24px; line-height: 24px; top: 7px;}
	}
	@media (max-width: @m){
		font-size: 11px; padding-left: 45px; margin-bottom: 10px; max-width: 260px;
		&:before{font-size: 20px; line-height: 20px; top: 5px;}
	}
}
.cd-discount-badge{
	background: @red; font-size: 14px; font-weight: bold; padding: 0 15px; height: 30px; display: flex; align-items: center; color: #fff; border-radius: @borderRadius; position: relative; margin: 0 13px 0 18px;
	&:after{.pseudo(8px,8px); background: @red; .rotate(45deg); right: -3px; top: calc(~"50% - 4px");}
	@media (max-width: @t){height: 25px; padding: 0 10px; margin-left: 15px;}
	@media (max-width: @m){font-size: 12px; margin-left: 10px;}
}
.cd-save{
	font-size: 14px; font-weight: bold; line-height: 1.3;
	@media (max-width: @t){font-size: 12px; font-weight: normal; padding-top: 3px;}
	@media (max-width: @m){
		font-weight: bold;
		.label{display: block;}
	}
}

.cd-tooltip{
	background: #fff; display: none; box-shadow: 0 0 10px 0 rgba(0,0,0,0.2); position: absolute; padding: 10px 20px; width: 200px; z-index: 50; top: calc(~"100% - -10px"); left: -8px;
	&:before{.pseudo(10px,10px); background: #fff; .rotate(45deg); left: 20px; top: -4px;}
}
.cd-loyalty-tooltip{
	top: calc(~"100% - 5px"); width: 325px; padding: 15px 25px; left: 0;
	p{padding-bottom: 7px;}
	ul{list-style: none; padding: 0; margin: 0;}
	li{margin-bottom: 3px;}
	a{
		color: @textColor; text-decoration: none;
		&:hover{color: @green; text-decoration: underline;}
	}
	&:before{left: calc(~"50% - 20px");}
	@media (max-width: @t){display: none!important;}
}
.tooltip-active .cd-tooltip{display: block;}
.cd-phone-order{
	 position: relative; padding: 0 0 0 40px; min-width: 175px;
	&:before{.icon-phone2(); font: 30px/30px @fonti; color: @green; left: 0; top: 2px; position: absolute;}
	strong{color: @green;}
	p{padding: 0;}
	@media (max-width: @t){
		padding-left: 0; min-width: 0;
		&:before{display: none;}
	}
	@media (max-width: @tp){
		padding-left: 55px;
		&:before{display: block; font-size: 25px; line-height: 25px; left: 9px; top: 5px;}
	}
	@media (max-width: @m){
		padding-left: 45px;
		&:before{font-size: 22px; line-height: 22px;}
	}
}
.cd-phone-order-tooltip{
	strong{display: block; color: @textColor;}
	@media (max-width: @t){display: none!important;}
}

.add-to-cart-container{
	display: flex;
	@media (max-width: 1080px){
		width: 100%;
		&.cd-qty-limit+.cd-wishlist{padding-left: 0;}
	}
}

.cd-btn-add{
	width: 296px; padding: 0; flex-grow: 1; font-size: 14px; cursor: pointer;
	&>span{
		position: relative; display: flex; align-items: center;
		&:before{.icon-cart(); font: 23px/23px @fonti; margin-right: 12px; position: relative; top: -2px;}
	}
	@media (max-width: @l){width: 200px;}
	@media (max-width: @t){
		height: 54px;
		span:before{font-size: 20px; line-height: 20px; top: 0; margin-right: 8px;}
	}
	@media (max-width: @m){
		height: 45px;
	}
}
.cd-btn-add-l2{display: none;}
.cd-add-container{
	display: flex; align-items: center; padding-top: 0; 
	&.not-available{
		flex-wrap: wrap; flex-direction: column-reverse;
		.cd-wishlist{
			margin: 0; width: 100%;
			@media (max-width: @tp){padding-left: 0; text-align: left; margin: 0;}
		}
		.add-to-cart-container{width: 100%;}
		@media (max-width: @m){flex-direction: column;}
	}
	@media (max-width: 1080px){padding-top: 20px; flex-wrap: wrap;}
}
.cd-qty-limit{
	.cd-qty{display: none;}
}
.cd-unavailable{
	width: 100%; margin-top: 30px; font-size: 14px;
	h2{padding: 0 0 5px; font-size: 20px; color: @red;}
	@media (max-width: @t){font-size: 12px;}
	@media (max-width: @tp){
		h2{font-size: 16px;}
	}
	@media (max-width: @m){margin-top: 0;}
}
@media (max-width: @t){
	.cdu-subtitle{max-width: 290px;}
}
.cd-notify-form-container{margin-top: 10px;}
.cd-notify-form-fields{
	display: flex; position: relative;
	input{flex-grow: 1; border-right: 0; border-radius: @borderRadius 0 0 @borderRadius;}
	.error{position: absolute; top: 100%;}
}
.btn-notify{
	border-top-left-radius: 0; border-bottom-left-radius: 0; height: 54px;
	@media (max-width: @t){padding: 0; width: 125px;}
	@media (max-width: @m){height: 47px;}
}
.notifyme_success{
	color: @green; font-size: 16px;
	@media (max-width: @tp){font-size: 14px;}
}

.wp-qty{
	position: relative; width: 85px; height: 30px;
	.wp-input-qty, .qty-input{width: 100%; height: 100%; text-align: center; font-size: 12px; padding: 0 26px;}
	.wp-btn-qty{
		width: 23px; z-index: 10;
		.toggle-icon{
			width: 12px; height: 12px;
			&:before{left: 5px;}
			&:after{top: 5px;}
		}
	}
	@media (max-width: @m){width: 100%;}
}
.wp-unit{display: block; text-align: center; font-size: 11px; color: @gray2; padding-top: 3px;}

.cd-qty{
	width: 120px; height: 54px; margin-right: 10px; position: relative;
	.wp-input-qty, .qty-input{
		padding: 0 20px; font-size: 16px;
		@media (max-width: @m){font-size: 14px;}
	}
	.wp-btn-qty{width: 35px; transform: none;}
	@media (max-width: @l){
		width: 110px;
		.wp-btn-qty{width: 27px;}
		.wp-input-qty, .qty-input{padding: 0 30px;}
	}
	@media (max-width: @t){width: 100px;}
	@media (max-width: @m){height: 45px;}
}
.cd-badges{
	position: absolute; top: 32px; left: 0; z-index: 50; display: flex;
	@media (max-width: @t){top: 25px;}
	@media (max-width: @tp){display: none;}
	@media (max-width: @m){display: block; margin: 0!important; top: -3px; right: 15px; left: auto;}
}
.cd-badge{
	margin-bottom: 5px; font-size: 14px; display: inline-flex;
	@media (max-width: @m){font-size: 12px;}
}
.cd-badge-pickup{
	margin-bottom: 20px;
	@media (max-width: @tp){font-size: 11px; line-height: 14px; padding: 0 8px; align-self: baseline; width: auto; margin-bottom: 10px; letter-spacing: -0.24px;}
}
/*------- /catalog detail -------*/

/*------- flyout availability -------*/
.flyout-active{
	overflow: hidden; position: relative;
	&:before{.pseudo(auto,auto); left: 0; right: 0; top: 0; bottom: 0; opacity: 1; z-index: 9999999999; background-color: rgba(35,35,35,0.3);}
}
.flyout{
	width: 400px; top: 0; bottom: 0; right: -750px; .transition(transform); display: block; border-radius: unset; background: white; position: absolute; bottom: 0; box-shadow: 0 0 40px 0 rgba(0,0,0,0.3); z-index: 9999999999; bottom: 0; visibility: hidden; opacity: 0;
	&.active{transform: translateX(-750px); top: 0!important; visibility: visible; opacity: 1; position: fixed;}
	&::-webkit-scrollbar{-webkit-appearance: none; width: 4px; background: white; border-radius: 4px;}
	&::-webkit-scrollbar-thumb {background-color: @green; border-radius: 4px;}
	@media(max-width: @m){
		width: 100%; right: unset; transition: unset; left: 0; top: 0; bottom: 0; right: 0; display: none; transform: unset;
		&.active{display: block; transform: unset}
	}
}
.flyout-close{
	display: flex; align-items: center; justify-content: center; font-size: 13px; line-height: 17px; box-shadow: 0 0 3px 0 rgba(0,0,0,0.5); padding: 0 13px; border: 1px solid var(--black); border-radius: 17px; min-height: 34px; background: var(--white); color: var(--textColor); font-family: var(--fontMedium);
	span{
		position: relative; padding-left: 14px;
		&:before{.pseudo(auto,auto); .icon-arrow-down(); left: 0; top: 4px; font: 10px/10px var(--fonti); color: var(--textColor); .transition(color);}
	}
	/*@media(min-width: @l){
		cursor: pointer;
		span:before{.transition(left);}
		&:hover{
			span:before{left: -2px;}
		}
	}
	@media(max-width: @l){
		transition: background .3s;
		&:active{background: var(--activeGray);}
	}*/
}
.cd-flyout-title{
	min-height: 68px; display: flex; align-items: center; font-size: 16px; line-height: 20px; font-weight: bold; color: #244538; padding-left: 61px; padding-right: 55px; position: relative; border-bottom: 1px solid #E0E3DF; 
	&:before{.pseudo(auto,auto); top: 23px; left: 25px; font: 22px/22px @fonti; color: @green; .icon-store();}
	@media(max-width: @m){
		min-height: 50px; font-size: 14px; line-height: 20px; padding-left: 51px; padding-right: 42px;
		&:before{top: 14px; left: 15px;}
	}
}
.cd-flyout-close{
	background: #244538; width: 34px; height: 34px; position: absolute; top: 17px; right: 15px; border-radius: 50%; display: flex; align-items: center; justify-content: center; z-index: 999; cursor: pointer;
	&:after{.pseudo(auto,auto); .icon-cross(); font: 14px/14px @fonti; color: white;}
	@media(max-width: @m){
		width: 24px; height: 24px; top: 13px; right: 15px;
		&:after{font: 9px/9px @fonti;}
	}
}
.cd-flyout-content{
	padding: 25px; max-height: calc(~"100% - 68px"); overflow: auto;
	&::-webkit-scrollbar{-webkit-appearance: none; width: 4px; background: white; border-radius: 4px;}
	&::-webkit-scrollbar-thumb {background-color: @green; border-radius: 4px;}
	@media(max-width: @m){padding: 16px 15px 20px; max-height: calc(~"100% - 50px");}
}
.cd-flyout-content-intro{
	font-size: 12px; line-height: 18px; padding-left: 32px; position: relative; color: #274E40; padding-bottom: 25px;
	&:before{.pseudo(22px,22px); left: 0; .icon-info(); font: 12px/12px @fonti; border-radius: 50%; border: 1px solid #274E40; display: flex; align-items: center; justify-content: center;}
	@media(max-width: @m){padding-bottom: 20px;}
}
.cd-store{
	margin-top: -1px; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; position: relative;
	&.active{
		.cd-store-content{display: block; padding-bottom: 10px;}
		.cd-store-title{
			color: @green;
			&:after{transform: rotate(-180deg);}
		}
	}
	&:before{.pseudo(auto,auto); left: 0; font: 21px/21px @fonti; color: @green; top: 9px; .icon-available();}
	&.cd-store-notavailable{
		&:before{color: @red; .icon-not-available();}
		.cd-store-title span{opacity: .6;}
	}
}
.cd-store-title{
	font-size: 14px; line-height: 20px; padding: 10px 30px 10px 32px; position: relative; color: #274E40; .transition(color); cursor: pointer;
	&:after{.pseudo(auto,auto); .icon-arrow-down(); color: @green; font: 7px/7px @fonti; right: 8px; top: 15px; .transition(transform);}
	&:hover{color: @green;}
	@media(max-width: @m){
		&:after{right: 0;}
	}
}
.cd-store-content{display: none; padding-left: 32px;}
.cd-store-i{
	font-size: 14px; line-height: 20px; padding-bottom: 10px;
	p{padding-bottom: 0;}
}
.cd-store-address{text-decoration: underline; text-underline-offset: 2px;}
.cd-store-business-hour{
	br{display: none;}
}
/*------- /flyout availability -------*/

/*------- brands -------*/
.page-brands .header{background-position: center top; background-repeat: no-repeat; background-size: cover;}
.bc-brands{
	padding-bottom: 10px;
	//a:last-of-type{display: none;}
}
.m-header{
	text-align: center; padding: 30px 0;
	@media (max-width: @tp){padding: 20px 0;}
	@media (max-width: @m){text-align: left; padding: 15px 15px 20px;}
}
.m-title{padding: 0;}
.ma{
	background: url(images/bg.jpg); padding: 65px 0;
	@media (max-width: @l){padding: 40px 0;}
	@media (max-width: @t){padding: 40px var(--wrapperOffset);}
	@media (max-width: @tp){padding-top: 30px; padding-bottom: 30px;}
	@media (max-width: @m){padding-top: 15px; padding-bottom: 15px;}
}
.ma-title{
	font-size: 32px; line-height: 1.3;
	strong{color: @green;}
	@media (max-width: @t){font-size: 26px;}
	@media (max-width: @tp){font-size: 20px;}
}
.ma-col1{
	width: 370px; flex-grow: 0; flex-shrink: 0;
	@media (max-width: @t){width: 280px;}
	@media (max-width: @tp){width: 190px;}
	@media (max-width: @m){display: none;}
}
.ma-col2{flex-grow: 1; display: flex;}
.ma-items{
	display: grid; grid-gap: 12px; grid-template-columns: repeat(13, 60px); grid-template-rows: 1fr 1fr; margin-left: auto;
	@media (max-width: @l){grid-template-columns: repeat(10, 60px);}
	@media (max-width: @t){grid-template-columns: repeat(10, 50px); grid-gap: 10px;}
	@media (max-width: @tp){grid-template-columns: repeat(9, 50px); grid-gap: 8px;}
	@media (max-width: @m){grid-template-columns: repeat(5, auto); grid-gap: 5px; margin: 0; width: 100%;}
}
.ma-item{
	width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; border: 1px solid @borderColor; font-weight: bold; font-size: 20px; border-radius: @borderRadius; background: #fff; text-decoration: none; position: relative; overflow: hidden; cursor: pointer;
	span{position: relative; z-index: 1;}
	&:after{.pseudo(auto,auto); top: -1px; right: -1px; bottom: -1px; left: -1px; .gradient-green; opacity: 0; .transition(opacity);}
	&:hover{
		color: #fff; text-decoration: none;
		&:after{opacity: 1;}
	}
	@media (max-width: @t){width: 50px; height: 50px;}
	@media (max-width: @m){
		width: 100%; height: 100%; font-size: 16px;
		&:before{content:""; padding-bottom: 100%;}
	}
}
.m-logo{
	margin-bottom: 35px;
	img{max-width: 180px;}
	@media (max-width: @tp){
		margin: 10px 0;
		img{max-width: none; width: auto; max-height: 45px;}
	}
	@media (max-width: @m){
		margin: 0;
		img{position: relative; top: auto; left: auto; max-height: 40px;}
	}
}
/*------- /brands -------*/

/*------- wishlist -------*/
.page-wishlist .header{padding-bottom: 0;}
.wrapper-wishlist{
	padding: 30px 0 100px;
	@media (max-width: @t){padding-left: var(--wrapperOffset); padding-right: var(--wrapperOffset);}
	@media (max-width: @m){padding-top: 0; padding-bottom: 0;}
}
.wishlist-header{
	color: #fff; display: flex; padding: 25px 0 25px; align-items: center;
	@media (max-width: @t){padding-left: var(--wrapperOffset); padding-right: var(--wrapperOffset);}
	@media (max-width: @m){
		color: @textColor; padding-top: 20px; padding-bottom: 20px; background: #fff;
		.btn-wishlist-delete{display: none;}
	}
}
.wishlist-title{
	padding: 0; font-size: 42px; position: relative; padding-left: 65px;
	@media (max-width: @t){font-size: 34px;}
	@media (max-width: @tp){
		font-size: 32px; padding-left: 55px;
		.wishlist-title-counter:before{color: #fff;}
	}
	@media (max-width: @m){
		font-size: 20px; padding-left: 45px;
		.wishlist-title-counter:before{color: @textColor; font-size: 23px; line-height: 23px;}
	}
}
.wishlist-title-counter{
	position: absolute; top: 0; left: 0;
	&:before{.icon-heart(); font: 34px/34px @fonti;}
	span{
		position: absolute; top: -5px; right: -19px; font-size: 14px; background: @green; border-radius: 100px; width: 30px; height: 30px; display: none; align-items: center; justify-content: center;
		&.active{display: flex;}
	}
	@media (max-width: @t){
		&:before{font-size: 30px; line-height: 30px;}
	}
	@media (max-width: @tp){
		span{width: 24px; height: 24px;}
		&:before{color: @green;}
	}
	@media (max-width: @m){
		span{color: #fff; font-size: 12px; font-weight: normal; width: 22px; height: 22px; top: -9px; right: -10px;}

	}
}
.btn-wishlist-delete{
	margin-left: auto; min-width: 20%; background: #E9ECEB; border: 0; height: 54px; color: @textColor!important; font-weight: bold; .transition(all); cursor: pointer;
	span{
		display: flex; align-items: center;
		&:before{.icon-trash(); font: 21px/21px @fonti; margin-right: 10px; position: relative; top: -1px;}
	}
	&:hover{background: #E9ECEB/1.1;}
	@media (max-width: @m){
		width: 100%; height: 40px; font-size: 12px;
		span:before{font-size: 17px; line-height: 17px;}
	}
}
.btn-auth-wishlist-delete{
	height: 44px; font-size: 14px; padding: 0 25px;
	span:before{font-size: 17px; line-height: 17px; top: 0;}
	@media (max-width: @m){
		height: 37px; font-size: 12px;
		span:before{margin-right: 7px;}
	}
}
.wishlist-btns{
	text-align: right; padding-top: 30px;
	@media (max-width: @m){padding: 20px 0 30px;}
}
.wishlist-auth-btns{text-align: right;}
.wishlist-auth-btns-top{
	position: absolute; top: 55px; right: 100px;
	@media (max-width: @l){right: 60px;}
	@media (max-width: @t){right: 30px; top: 45px;}
	@media (max-width: @tp){top: -58px; right: 0;}
	@media (max-width: @m){position: relative; top: auto; right: auto; margin-bottom: 15px;}
}
.wishlist-auth-btns-bottom{
	padding-top: 10px;
	@media (max-width: @m){padding-top: 0;}
}
.auth-wishlist-items{
	padding-top: 20px;
	@media (max-width: @tp){position: relative;}
	@media (max-width: @m){padding-top: 0; padding-bottom: 15px;}
}
#view_wishlist{padding-left: 1px;}
@media (max-width: @m){
	.wishlist-items .cp{
		width: 50%;
		.cp-btn-addtocart span:before{display: block;}
	}
}
/*------- /wishlist -------*/

/*------- slider -------*/
.slider{
	position: relative;
	img{display: block; width: auto; height: auto;}
	.slick-arrow{top: calc(~"50% - 40px");}
	@media (min-width: @m){
		.slick-dots{display: none!important;}
		min-height: 420px;
	}
	@media (max-width: @m){min-height: 400px;}
	@media (max-height: 400px){min-height: 300px;}
	.slick-dots{justify-content: center; position: absolute; bottom: 20px; left: 0; right: 0;}
}
.slider-items{
	overflow: hidden; max-height: 0; min-height: 400px;
	@media (max-width: @t){min-height: 300px;}
	@media (max-height: 400px){min-height: 200px;}
	&.slick-initialized{max-height: none; overflow: visible;}
	img{width: 100%;}
}
.slider.glide--carousel{
	.slider-items, .slider-item{max-height: none;}
}
@media (max-width: @t){
	.wrapper.slider{padding-left: 30px; padding-right: 30px;}
}
@media (max-width: @tp){
	.wrapper.slider{padding-left: 20px; padding-right: 20px;}
}
@media (max-width: @m){
	.wrapper.slider{padding: 0;}
}
.slider-item{
	overflow: hidden; display: block; max-height: 0; overflow: hidden; border-radius: @borderRadius;
	img{border-radius: @borderRadius; display: block; margin: auto;}
	&.slick-current{max-height: none;}
	&.loaded{background-color: transparent;}
	@media (max-width: @m){
		border-radius: 0;
		img{border-radius: 0;}
	}
}
.slider-nav{
	display: flex; min-height: 120px; margin: -20px 60px auto; background: linear-gradient(180deg, #FFFFFF 0%, #E8E8E8 100%); border-radius: 3px; overflow: hidden; position: relative; z-index: 1; box-shadow: 0 15px 50px 0 rgba(0,0,0,0.75); font-size: 16px;
	@media (max-width: @l){font-size: 15px; margin: -20px 40px auto; min-height: 100px;}
	@media (max-width: @t){font-size: 12px; min-height: 75px;}
	@media (max-width: @tp){margin-left: 20px; margin-right: 20px; font-size: 11px; min-width: 65px;}
	@media (max-width: @m){display: none;}
}
:deep(.slider-nav-item){
	width: 25%; display: flex; align-items: center; padding: 10px; cursor: pointer; position: relative;
	&.active{
		color: #fff; .gradient-green;
		.slider-nav-progress{display: block;}
	}
	img{.transition(opacity);}
	&:hover{
		img{opacity: .7;}
	}
	@media (max-width: @t){padding: 7px;}
}
.slider-nav-item-title{
	padding-right: 20px; line-height: 1.4;
	@media (max-width: @tp){padding-right: 0;}
}
.slider-nav-progress{position: absolute; display: none; bottom: 0; left: 0; right: 0; height: 5px; background: rgba(0,0,0,.2);}
.inProgress{height: 100%; width: 0; background: @lightGreen;}
.slider-nav-item-image{
	width: 100px; flex-grow: 0; flex-shrink: 0; margin-right: 20px;
	img{display: block; border-radius: @borderRadius; max-height: 100%; width: auto;}
	@media (max-width: @l){width: 80px;}
	@media (max-width: @t){width: 60px;}
	@media (max-width: @tp){width: 50px; margin-right: 10px;}
}
/*------- /slider -------*/

/*------- promo -------*/
.hp-promo{
	background: #000; border-radius: @borderRadius;
	a{display: block; height: 100%; border-radius: @borderRadius;}
	img{display: block; border-radius: @borderRadius; box-shadow: 0 15px 40px 0 rgba(0,0,0,0.5); .transition(opacity);}
	&:hover{
		img{opacity: .8;}
	}
	@media (max-width: @m){
		width: 50%; border-radius: 0;
		a{border-radius: 0;}
		img{box-shadow: none; border-radius: 0;}
	}
}
.wrapper-promo{
	display: grid; grid-template-columns: 360px auto 360px; column-gap: 30px;
	@media (max-width: @l){column-gap: 20px; grid-template-columns: 24.55% auto 24.55%;}
	@media (max-width: @t){column-gap: 10px; grid-template-columns: 24.8% auto 24.8%; width: auto; margin-left: 30px; margin-right: 30px;}
	@media (max-width: @tp){margin-left: 20px; margin-right: 20px; grid-template-columns: 24.7% auto 24.7%;}
	@media (max-width: @m){display: flex; flex-wrap: wrap; margin: 0;}
}
@media (max-width: @m){
	.hp-promo2{order: 1; width: 100%; border-bottom: 1px solid rgba(255,255,255,.7);}
	.hp-promo1{order: 2; border-right: 1px solid rgba(255,255,255,.7);}
	.hp-promo3{order: 3; width: calc(~"50% - 1px");}
}
/*------- /promo -------*/

/*------- newsletter -------*/
// newsletter main
.nw{
	border-top: 1px solid @borderColor; background-color: #fff; background-position: right top; background-repeat: no-repeat; background-size: contain; padding: 65px 0 70px var(--contentSpacing); font-size: 16px; position: relative; border-bottom-right-radius: @borderRadius; border-bottom-left-radius: @borderRadius;
	@media (max-width: @l){font-size: 15px; padding-top: 50px; padding-bottom: 50px;}
	@media (max-width: @t){margin-left: 30px; margin-right: 30px; border: 0; border-radius: @borderRadius; font-size: 14px; padding-top: 25px; padding-bottom: 25px; box-shadow: 0 25px 40px 0 rgba(0,0,0,0.3);}
	@media (max-width: @tp){margin-left: 20px; margin-right: 20px; font-size: 13px; padding-top: 15px; padding-bottom: 15px;}
	@media (max-width: @m){background: transparent!important; box-shadow: none; margin: 0; padding: 15px; font-size: 12px; content-visibility: auto; contain-intrinsic-size: 500px;}
}
.nw-title{
	font-size: 39px; font-weight: bold; position: relative; .circle-icon;
	p{padding: 0;}
	&:before{left: -80px;}
	strong{color: @red;}
	@media (max-width: @l){
		font-size: 38px; line-height: 1.5; margin-bottom: 10px;
		&:lang(de){font-size: 34px;}
		&:before{width: 54px; height: 54px; font-size: 20px;}
	}
	@media (max-width: @t){
		font-size: 28px; margin-bottom: 0;
		&:lang(de){font-size: 28px;}
		&:before{display: none;}
	}
	@media (max-width: @tp){
		font-size: 26px;
		&:lang(de){font-size: 26px;}
	}
	@media (max-width: @m){
		font-size: 18px; padding-left: 40px; margin-bottom: 10px; padding-top: 2px;
		&:lang(de){font-size: 18px;}
		&:before{display: flex; left: 0; width: 28px; height: 28px; font-size: 11px;}
	}
}
.nw-fields{
	max-width: 500px; position: relative;
	input{width: 100%;}
	@media (max-width: @t){
		max-width: none;
		input{height: 45px;}
	}
}
.nw-input{
	width: 100%; padding-right: 130px; font-size: 16px;
	@media (max-width: @t){font-size: 14px; padding-left: 20px;}
}
.nw-button{
	position: absolute; top: 0; right: 0; border-radius: 0; padding: 0; width: 110px; background: @darkGreen; .transition(all); border-top-right-radius: @borderRadius; border-bottom-right-radius: @borderRadius;
	&:hover{background: @lightGreen;}
	&:after{display: none;}
	@media (max-width: @t){height: 45px; width: 90px; font-size: 14px;}
}
.nw-cnt{
	padding-bottom: 20px;
	@media (max-width: @tp){padding-bottom: 10px;}
}
.nw-body{
	width: 560px; padding-left: 80px;
	@media (max-width: @t){padding-left: 0; width: 420px;}
	@media (max-width: @tp){width: 310px;}
	@media (max-width: @m){width: auto;}
}
.nw-error{font-size: 14px; padding: 5px 0 0 25px; text-align: left; color: @red;
	@media (max-width: @t){font-size: 12px; padding-left: 20px;}
}
.nw-checkbox{
	margin-top: 17px;
	label span{
		display: block; max-height: 80px; font-size: 12px; line-height: 1.4; overflow: auto; padding-right: 10px;
		&::-webkit-scrollbar { -webkit-appearance: none; width: 4px; background: @borderColor; }
		&::-webkit-scrollbar-thumb {
			background-color: @green; border-radius: 50px;
			box-shadow: 0 0 1px rgba(255,255,255,.5);
		}
		@media (max-width: @t){font-size: 11px; max-height: 48px;}
		@media (max-width: @m){font-size: 10px;}
	}
	.error{
		padding-left: 30px; padding-top: 2px; text-align: left;
		@media (max-width: @t){padding-bottom: 15px; font-size: 12px;}
	}
}


// newsletter - footer
.nw-bottom{
	width: var(--pageWidth); max-width: @pageWidth; display: flex; box-shadow: 0 25px 40px 0 rgba(0,0,0,0.3); margin: auto; padding: 0; border: 0; border-radius: @borderRadius; overflow: hidden; z-index: 10;
	.nw-body{
		margin: 75px 0 50px 80px; padding-right: 0;
		@media (max-width: @l){margin: 38px 0 35px 40px; width: 520px;}
		@media (max-width: @t){width: 415px; margin-top: 30px; margin-left: 30px;}
		@media (max-width: @tp){width: 300px; margin-left: 25px; margin-top: 20px;}
		@media (max-width: @m){width: 100%; margin: 0; padding: 20px 15px;}
	}
	.support{
		font-size: 16px;
		@media (max-width: @tp){font-size: 12px;}
	}
	.support-title, .support-number{
		font-size: 32px; color: @lightGreen; line-height: 1.3;
		@media (max-width: @l){font-size: 28px;}
		@media (max-width: @t){font-size: 20px;}
		@media (max-width: @tp){font-size: 18px;}
	}
	.support-title:before{
		color: @lightGreen; font-size: 42px;
		@media (max-width: @t){font-size: 30px; margin-bottom: 2px;}
	}
	.support-number{padding-top: 5px;}
	@media (max-width: @t){width: auto; margin: 0 30px;}
	@media (max-width: @tp){margin: 0 20px; line-height: 1.2;}
	@media (max-width: @m){margin: 0; display: block; border-radius: 0;}
}
.nw-col1{
	width: 460px; min-height: 440px; flex-grow: 0; flex-shrink: 0; background: url(/images/bg-green.jpg) center no-repeat; color: #fff; padding: 75px 80px;
	a, a[href^=tel]{color: #fff;}
	@media (max-width: @l){width: 360px; padding: 45px 50px; min-height: 360px;}
	@media (max-width: @t){width: 290px; padding: 30px 35px; min-height: 270px;}
	@media (max-width: @tp){width: 230px; padding: 25px 30px; min-height: 250px;}
	@media (max-width: @m){width: 100%; min-height: 0; padding-left: 62px;}
}
.nw-col2{
	flex-grow: 1; background-position: top right -100px; background-size: contain; background-repeat: no-repeat;
	&.custom-image{background-size: cover; background-position: top right 0;}
	@media (max-width: @m){background-image: none!important;}
}
.nw-main{
	.wrapper-bottom{padding-top: 165px;}
	.bottom{margin-top: -85px;}
	@media (max-width: @l){
		.wrapper-bottom{padding-top: 150px;}
	}
	@media (max-width: @t){
		.bottom{margin-top: -220px;}
		.wrapper-bottom{padding-top: 275px;}
	}
	@media (max-width: @tp){
		.bottom{margin-top: -205px;}
		.wrapper-bottom{padding-top: 240px;}
	}
	@media (max-width: @m){
		.bottom{margin-top: 0;}
		.wrapper-bottom{padding: 0;}
	}
}
.nw-bottom-main{
	box-shadow: none; border-top-left-radius: 0; border-top-right-radius: 0; border-top: 1px solid @borderColor;
	@media (max-width:  @l){width: auto;}
	@media (max-width: @t){border: 0; box-shadow: 0 25px 40px 0 rgba(0,0,0,0.3); border-radius: @borderRadius;}
}

.nw-bottom-custom{
	border-radius: 0 0 3px 3px;
}


// newsletter leaving
.quick-fancybox-leave{
	.fancybox-inner{
		overflow: visible!important;
		@media (max-width: @m){background-image: none!important;}
	}
	.fancybox-skin{padding: 0!important;}
}
.nw-leaving-logo{
	width: 85px; height: 85px; display: flex; align-items: center; justify-content: center; .gradient-green; border: 6px solid #fff; border-radius: 200px; position: absolute; top: -40px; left: 50%; margin-left: -40px;
	&:before{.icon-organic-food(); font: 26px/26px @fonti; color: #fff;}
	@media (max-width: @tp){
		width: 70px; height: 70px; margin-left: -35px; top: -35px;
		&:before{font-size: 23px; line-height: 23px;}
	}
}
.nw-leaving{
	text-align: center; max-width: 520px; margin: auto; font-size: 16px; padding-top: 70px;
	&:lang(de){max-width: 650px;}
	@media (max-width: @tp){
		font-size: 14px; max-width: 400px; padding-top: 55px;
		&:lang(de){max-width: 500px;}
	}
	@media (max-width: @tp){
		padding: 15px; text-align: left; font-size: 12px;
		&:before{font-size: 10px;}	
	}
}
.nw-leaving-title{
	font-size: 42px; line-height: 1.2; font-weight: bold; padding: 0 0 15px;
	&:lang(de){font-size: 33px;}
	p{padding: 0;}
	strong{color: @red;}
	@media (max-width: @tp){
		font-size: 34px;
		&:lang(de){font-size: 22px;}
	}
	@media (max-width: @m){padding: 0 0 8px;}
}
.nw-leaving-fields{max-width: none;}
.nw-leaving-subtitle{
	line-height: 1.5; padding-bottom: 5px;
	@media (max-width: @m){
		padding-bottom: 10px;
		p{padding: 0;}
	}
}

@media (max-width: @t){
	.popup-nl-active{overflow: hidden;}
}

@media (max-width: @m){
	.popup-nl-active{
		.zEWidget-launcher{visibility: hidden;}
		.fancybox-close{top: 20px; right: 20px;}
		.fancybox-wrap{align-items: flex-end;}
		.fancybox-skin{width: 100%!important;}
	}
	.nw-leaving-logo{display: none;}
	.nw-leaving-title{font-size: 24px;}
	.quick-fancybox-leave{
		left: 0!important; right: 0!important; bottom: 0!important; top: auto!important; width: auto!important;
		.fancybox-skin{box-shadow: none!important; border-radius: 0!important;}
		.fancybox-overlay{background: none!important;}
		.fancybox-inner{width: auto!important; height: auto!important;}	
		.fancybox-close{
			width: 25px; height: 25px; right: 15px; top: 13px;
			&:before{font-size: 10px;}
		}
	}
}
/*------- /newsletter -------*/

/*------- brands -------*/
.brands{
	border-top: 1px solid @borderColor; font-size: 15px; border-bottom: 1px solid @borderColor; background: #fff; content-visibility: auto; contain-intrinsic-size: 100px;
	@media (max-width: @t){font-size: 13px;}
	@media (max-width: @tp){font-size: 12px;}
	@media (max-width: @m){
  		margin: 30px 15px; border: 0;
  		&.homepage{
  			.brands-items{padding: 1px 0 0 1px;}
  			.all-brands{border: 1px solid @borderColor;}
  		}	
  	}
}
@media (max-width: @m){
	.m-brands{margin: 0;}
}
.brands-items{
	display: flex; border-left: 1px solid @borderColor;
	@media (max-width: @m){flex-wrap: wrap; border: 0; padding-top: 1px;}
}
.brand-item{
	border-right: 1px solid @borderColor; flex-grow: 1; width: calc(~"100% / 7"); height: var(--brandItemHeight); display: flex; align-items: center; justify-content: center;
	img{max-height: var(--brandItemImgHeight); width: auto; .transition(opacity); display: block;}
	&:hover{
		img{opacity: .6;}
	}
	&.loaded{background: none;}
	@media (max-width: @m){
		width: 33.3333%; border: 1px solid @borderColor; margin: -1px 0 0 -1px;
	}
}
.all-brands{
	font-weight: bold; text-decoration: none;
	&:hover{text-decoration: none; color: @green;}
	@media (max-width: @m){
		border: 0; text-decoration: underline;
		&:hover{text-decoration: underline; color: @textColor;}
	}
}
.wrapper-m-items{
	width: 900px;
	@media (max-width: @tp){width: auto;}
}
.m-items{
	padding: 45px 0; display: grid; grid-template-columns: repeat(4, 1fr);
	@media (max-width: @m){padding: 20px 0; grid-template-columns: repeat(1, 1fr);}
}
.m-row{display: flex; flex-wrap: wrap;}
.m-column{
	width: 100%; padding-bottom: 60px; text-align: center; position: relative;
	&:nth-child(4n), &:last-child{
		.m-letter:after{right: 100px;}
	}	
	&:nth-child(4n - 3){
		.m-letter:after{left: 100px;}
	}
	@media (max-width: @m){
		width: 100%; text-align: left; padding: 0 15px; margin-bottom: 25px;
	}
}
.m-list{
	list-style: none; padding: 0; margin: 0; font-size: 14px;
	li{padding-bottom: 3px;}
	a{
		text-decoration: none;
		&:hover{text-decoration: underline; color: @green;}
	}
	@media (max-width: @m){
		font-size: 12px; column-count: 3;
		li{display: block; vertical-align: top;}
	}
}
.m-letter{
	font-weight: bold; display: flex; align-items: center; justify-content: center; padding: 0; font-size: 32px; margin: auto; margin-bottom: 5px; background: #fff; z-index: 1;
	span{position: relative; z-index: 1; background: #fff; display: block; width: 60px; height: 60px; margin: auto;}
	&:after{.pseudo(auto,1px); background: @borderColor; top: 30px; left: 0; right: 0;}
	@media (max-width: @m){
		justify-content: flex-start;
		span{width: auto; height: auto; font-size: 18px; margin: 0;}
		&:after{left: 40px!important; right: 15px!important; top: 15px;}
	}
}
/*------- /brands -------*/

/*------- publish widget -------*/
.pw{
 	content-visibility: auto; contain-intrinsic-size: 900px;
 	&.homepage{
  		@media (max-width: @m){margin-top: -1px;}
  	}	
 }
@media (max-width: @t){
	.pw{background: url(images/bg.jpg);}
}
.wrapper-pw{
	padding: 125px 0 130px; display: grid; grid-template-columns: var(--pwColWidth) auto; column-gap: 30px;
	@media (max-width: @l) {padding: 115px 0 100px;}
	@media (max-width: @t){padding: 60px 30px; column-gap: 25px; position: relative;}
	@media (max-width: @tp){padding: 45px 20px 35px; column-gap: 18px;}
	@media (max-width: @m){padding: 25px 15px 0; display: flex; flex-direction: column;}
}
.pw-title{
	font-size: var(--pwTitle); line-height: 1.3; position: relative; padding-left: 80px;
	.circle-icon;
	p{padding: 0;}
	&:before{
		top: -4px; .icon-eco(); font-size: 55px;
		@media (max-width: @l){top: -10px;}
	}
	@media (max-width: @t){
		padding-left: 65px;
		&:before{font-size: 44px; top: -6px}
	}
	@media (max-width: @tp){
		padding-left: 60px; width: 260px; line-height: 1.25; margin-bottom: 20px;
		&:before{top: 5px;}
	}
	@media (max-width: @m){
		order: 1; width: auto; padding-left: 40px; padding-top: 8px;
		&:before{font-size: 27px;}
	}
}
@media (max-width: @m){
	.pw-col1{order: 2; margin-bottom: 10px;}
	.pw-col2{order: 3;}
}
.pw-nav-container{
	grid-column: ~"1/-1"; padding: 33px 0 30px;
	@media (max-width: @m){order: 4; padding: 20px 0 25px;}
}
.pw-nav{
	list-style: none; padding: 0; border-radius: @borderRadius; font-size: 16px; font-weight: bold; display: inline-flex; flex-wrap: wrap;
	li{
		background: #fff; border: 1px solid @borderColor; height: 50px; margin: -1px 0 0 -1px;
		&:first-child{border-radius: @borderRadius 0 0 @borderRadius;}
		&:last-child{border-radius: 0 @borderRadius @borderRadius 0;}
		@media (max-width: @m){height: auto; margin: 5px 16px 5px 0; border: none; background: none;}
	}
	a{
		display: flex; align-items: center; justify-content: center; height: 100%; text-decoration: none; padding: 0 30px;
		&:hover{color: @green;}
	}
	@media (max-width: @m){
		border: 0; background: none; font-size: 12px;
		a{text-decoration: underline; padding: 0;}
	}
}
/*------- /publish widget -------*/

/*------- publish -------*/
.page-publish{
	.header{
		padding-bottom: 573px;
		@media (max-width: @l){padding-bottom: 460px;}
		@media (max-width: @t){background-image: url(images/bg-dark-t.jpg); padding-bottom: 400px;}
		@media (max-width: @tp){padding-bottom: 350px;}
		@media (max-width: @m){padding-bottom: 0;}
	}
	.bc a{color: @lightGreen;}
	.pw-title{
		color: #fff; font-size: 42px;
		@media (max-width: @l){font-size: 38px;}
		@media (max-width: @t){
			font-size: 32px;
			&:before{top: -3px;}
		}
		@media (max-width: @tp){
			font-size: 28px; width: 340px; line-height: 1.2; padding-left: 70px; margin-bottom: 20px; display: flex; align-items: center; min-height: 70px;
			&:before{width: 50px; height: 50px; top: 10px;}
		}
		@media (max-width: @m){
			padding: 0 0 0 38px; min-height: 0; color: @textColor; font-size: 17px; width: auto;
			&:before{width: 28px; height: 28px; top: -4px;}
		}
	}
	@media (max-width: @t){background-image: url(images/bg.jpg);}
	@media (max-width: @m){background: #fff;}
}
@media (max-width: @m){
	.p-index-items{background: url(images/bg.jpg); margin: 0 -15px -15px; padding: 15px;}
}
.page-publish-level2{
	.header{padding-bottom: 370px;}
	.wrapper-publish{margin-top: -345px;}
	@media (max-width: @tp){
		.wrapper-publish{margin-top: -230px;}
		.header{padding-bottom: 250px;}
	}
	@media (max-width: @m){
		.wrapper-publish{margin-top: 0;}
		.header{padding-bottom: 0;}
	}
}
.wrapper-publish{
	margin-top: -548px; padding-bottom: 120px;
	@media (max-width: @l){margin-top: -448px; padding-bottom: 90px;}
	@media (max-width: @t){padding-left: 30px; padding-right: 30px; margin-top: -375px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px; margin-top: -330px; padding-bottom: 65px;}
	@media (max-width: @m){margin-top: 0; padding: 15px;}
}
.p-title{font-weight: bold;}
.p-intro{
	margin-bottom: 60px; margin-top: 5px;
	@media (max-width: @t){margin-bottom: 45px;}
	@media (max-width: @tp){margin-bottom: 35px;}
	@media (max-width: @m){margin-bottom: 25px;}
}
.p-intro-items{
	background: #fff; border-radius: @borderRadius; .box-shadow-yellow; padding: 20px; display: flex; margin-top: 40px;
	@media (max-width: @l){margin-top: 30px;}
	@media (max-width: @t){padding: 15px;}
	@media (max-width: @tp){padding: 10px;}
	@media (max-width: @m){display: block; border-radius: 0; box-shadow: none; padding: 0; background: none; margin-top: 10px;}
}
.p-intro-col1{
	width: 780px; flex-grow: 0; flex-shrink: 0; margin-right: 40px;
	@media (max-width: @l){width: 600px; margin-right: 30px;}
	@media (max-width: @t){width: 490px;}
	@media (max-width: @tp){width: 380px; margin-right: 20px;}
	@media (max-width: @m){width: auto; margin: 0; min-height: 280px;}
}
.p-intro-col2{
	flex-grow: 1;
	@media (max-width: @m){margin-top: 10px;}
}
.p-items{
	display: grid; grid-gap: 35px; grid-template-columns: repeat(3, 1fr);
	@media (max-width: @t){grid-gap: 15px;}
	@media (max-width: @tp){grid-gap: 10px;}
	@media (max-width: @m){grid-template-columns: repeat(2, 1fr);}
}
.p-intro-title{
	font-size: 26px; font-weight: bold; padding: 0 0 15px;
	@media (max-width: @l){font-size: 22px; padding-bottom: 10px; line-height: 1.4;}
	@media (max-width: @tp){font-size: 18px;}
}
.p-nav{
	list-style: none; padding: 0; display: inline-flex; flex-wrap: wrap; font-weight: bold; margin-top: 35px; font-size: 16px;
	li{
		position: relative; .gradient-green; border: 1px solid @lightGreen; margin: -1px 0 0 -1px;
		&:before{.pseudo(auto,auto); top: 0; right: 0; bottom: 0; left: 0; .gradient-green-hover; opacity: 0; .transition(opacity);}
		&:hover:before{opacity: 1;}
		&:first-child{border-radius: @borderRadius 0 0 @borderRadius;}
		&:last-child{border-radius: 0 @borderRadius @borderRadius 0;}
	}
	a{
		height: 50px; padding: 0 30px; display: block; display: flex; align-items: center; justify-content: center; text-decoration: none; color: #fff; position: relative; z-index: 1;
		@media (max-width: @t){height: 47px;}
		@media (max-width: @tp){padding: 0 20px;}
	}
	@media (max-width: @tp){margin-top: 0; font-size: 13px;}
	@media (max-width: @m){
		font-size: 12px;
		li{
			background: none; border: 1px solid @borderColor; margin: 0 10px 10px 0; border-radius: @borderRadius!important;
			&:before{display: none;}
		}
		a{color: @textColor; height: 37px; min-width: 0; padding: 0 16px;}
	}
}

.load-more-container{text-align: center;}
.btn-load-more{
	margin-top: 80px; height: 50px;
	@media (max-width: @l){margin-top: 45px;}
	@media (max-width: @tp){margin-top: 25px;}
	@media (max-width: @m){margin-top: 20px; margin-bottom: 30px;}
}
.btn-load-more-catalog{
	margin-top: 30px;
	@media (max-width: @tp){margin-bottom: 0;}
}
.load-more-loader{
	display: block; grid-column: ~"1 / -1"; text-align: center;
	span{
		position: relative;
		&:before{.pseudo(40px,40px); background: url(images/loader.svg) no-repeat left top; background-size: contain; position: relative; margin: auto auto 5px;}
	}
}
.page-publish-tags{
	.no-publish{color: #fff;}
	.p-nav{display: none!important;}
}
.no-publish{position: relative;}
/*------- /publish -------*/

/*------- publish author -------*/
.page-author{
	.header{padding-bottom: 20px;}
}
.wrapper-publish-author{
	padding-top: 30px; padding-bottom: 50px;
	@media (max-width: @t){padding-left: 30px; padding-right: 30px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px;}
}
.wrapper-author-intro{
	color: #fff;
	@media (max-width: @t){padding-left: 30px; padding-right: 30px; padding-top: 25px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px;}
}
.p-author-desc{
	font-size: 16px; max-width: 70%;
	@media (max-width: @tp){max-width: 100%; font-size: 14px;}
}
/*------- /publish author -------*/

/*------- publish post -------*/
.pp{
	text-decoration: none; font-size: var(--ppFontSize); line-height: 1.4; position: relative; display: block; margin-bottom: 30px; .transition(all);
	&:hover{text-decoration: none; z-index: 5;}
	&.gray-shadow:after{.box-shadow-gray;}
	@media (min-width: @t){
		margin-bottom: 20px;
		&:after{.pseudo(auto,auto); left: -12px; top: -12px; right: -12px; bottom: -12px; background: #fff; .box-shadow-yellow; border-radius: @borderRadius; opacity: 0; .transition(opacity);}
	}
	@media (max-width: @tp){margin-bottom: 15px;}
}

.pp:not(.pp-hover2){
	&:hover:after{opacity: 1;}
}
.pp-hover2:hover{color: @green;}

.pp-image{
	display: flex; align-items: center; justify-content: center; border-radius: @borderRadius; position: relative; z-index: 1; flex-shrink: 0; flex-grow: 0;
	img{display: block; border-radius: @borderRadius; width: auto; height: auto;}
	/*&:before{content:""; display: block; padding-top: calc(~"100% / 2.1");}*/
}
.pp-cnt{
	display: block; padding: 20px 45px 10px; position: relative; z-index: 1;
	@media (max-width: @l){padding-left: 30px; padding-right: 30px;}
	@media (max-width: @t){padding-left: 20px; padding-right: 20px; padding-top: 15px;}
	@media (max-width: @tp){padding-left: 15px; padding-right: 15px; padding-top: 10px;}
	@media (max-width: @m){padding: 5px 0 0;}
}
.pp-category{
	color: @green; text-transform: uppercase; font-weight: bold; font-size: 12px;
	@media (max-width: @l) {font-size: 11px;}
	@media (max-width: @m){font-size: 10px;}
}
.pp-title{
	font-size: 18px; line-height: 1.4; padding: 3px 0 6px; margin: 0; font-weight: bold; display: block;
	@media (max-width: @l) {padding: 0 0 1px;}
	@media (max-width: @t){font-size: 15px;}
	@media (max-width: @tp){font-size: 14px;}
	@media (max-width: @m){font-size: 12px;}
}
.pp-short-desc{
	color: @textColor; display: -webkit-box;
	@media (max-width: @l) {-webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;}
	@media (max-width: @t){display: none;}
	@media (max-width: @m){overflow: visible; -webkit-line-clamp: inherit;}
}
.pp-big{
	font-size: 16px; margin-bottom: 0;
	.pp-title{
		font-size: 28px;
		@media (max-width: @l) {padding-bottom: 5px;}
		@media (max-width: @t){font-size: 22px;}
		@media (max-width: @tp){font-size: 18px;}
	}
	.pp-cnt{padding-bottom: 17px;}
	@media (max-width: @t){
		font-size: 14px;
		.pp-short-desc{display: block;}
	}
	@media (max-width: @tp){font-size: 12px;}
	@media (max-width: @m){
		.pp-title{font-size: 14px;}
	}
	@media (max-width: @m){
		.pp-cnt{padding-left: 10px; padding-right: 10px;}
	}
}
.pp-big-horizontal{
	display: flex; align-items: center;
	.pp-image{
		width: 715px;
		@media (max-width: @l){width: 570px;}
		@media (max-width: @t){width: 440px;}
		@media (max-width: @tp){width: 350px;}
		@media (max-width: @m){width: 100%;}
	}
	.pp-cnt{
		padding: 20px 45px 20px 80px; width: 100%;
		@media (max-width: @l){padding-left: 60px;}
		@media (max-width: @t){padding-left: 45px; padding-right: 30px;}
		@media (max-width: @tp){padding-left: 25px;}
		@media (max-width: @m){padding-left: 10px; padding-right: 10px; padding-bottom: 0; padding-top: 10px;}
	}
	@media (max-width: @m){flex-direction: column;}
}

.pp-small{
	display: flex; margin-bottom: 26px; align-items: center;
	.pp-image{width: var(--ppSmallImage); flex-grow: 0; flex-shrink: 0; background-color: #fff;}
	.pp-title{font-size: var(--ppSmallTitle);}
	.pp-cnt{padding: 5px 10px 5px 30px;}
	@media (max-width: @t){
		margin-bottom: 15px;
		.pp-cnt{padding-left: 15px;}
	}
	@media (max-width: @m){
		.pp-cnt{padding-right: 0;}
		align-items: flex-start;
	}
}

.pp-related{
	.pp-image{width: 260px;}
	@media (max-width: @t){
		.pp-image{width: 230px;}
		.pp-short-desc{display: block;display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;}
		.pp-title{padding-bottom: 3px; padding-top: 3px; font-size: 16px;}
	}
	@media (max-width: @m){
		.pp-image{width: 140px;}
		.pp-title{font-size: 12px; font-weight: normal;}
		.pp-short-desc{display: none;}
		.pp-cnt{padding-left: 12px;}
	}
}
@media (max-width: @t){
	.cd-pp-related{
		.pp-title{font-size: 14px;}
		.pp-image{width: 200px;}
	}
}
@media (max-width: @tp){
	.cd-pp-related{
		.pp-title{font-size: 13px;}
		.pp-image{width: 170px;}
	}
}
@media (max-width: @m){
	.cd-pp-related .pp-image{width: 140px;}
}
/*------- /publish post -------*/

/*------- publish detail -------*/
.page-article-detail{
	.header{
		padding-bottom: 0; background-position: center bottom 60px;
		@media (max-width: @t){background-image: url(images/header-t.jpg); background-size: 100% 450px; background-position: left top;}
	}
}
.pd-body{
	background: #fff; margin-top: -60px; padding-top: 60px; padding-bottom: 1px;
	@media (max-width: @tp){padding-left: 75px; padding-right: 75px;}
	@media (max-width: @m){padding-left: 15px; padding-right: 15px;}
}
.pd-wrapper{
	width: 1230px; margin: auto;
	@media (max-width: @l){width: 1000px;}
	@media (max-width: @t){width: auto; padding-left: 30px; padding-right: 30px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px;}
	@media (max-width: @m){padding-left: 15px; padding-right: 15px;}
}
.pd-header{
	text-align: center; color: #fff; padding-top: 10px; position: relative;
	@media (max-width: @m){color: @textColor; text-align: left;}
}
.pd-title{
	font-size: 40px; padding-bottom: 7px;
	@media (max-width: @t){font-size: 30px;}
	@media (max-width: @tp){font-size: 28px;}
	@media (max-width: @m){font-size: 20px;}
}
.pd-headline{
	font-size: 18px; line-height: 1.5;
	@media (max-width: @t){font-size: 16px;}
	@media (max-width: @tp){font-size: 14px;}
	@media (max-width: @m){font-size: 12px;}
}
.pd-date{
	position: relative; margin-left: 10px; padding-left: 10px; opacity: .5; margin-right: auto;
	&:before{.pseudo(1px,auto); background: @textColor; top: 2px; bottom: 2px; left: 0; opacity: .5;}
	@media (max-width: @m){
		padding: 0; margin: 0;
		&:before{display: none;}
	}
}
.pd-all-articles{
	position: relative;
	&:before{.icon-arrow-left(); font: 16px/16px @fonti; color: @green; position: absolute; left: -26px; top: 2px;}
	@media (max-width: @m){
		flex-grow: 1; margin-left: 21px;
		&:before{font-size: 13px; line-height: 13px; left: -21px;}
	}
}
.pd-info-feedback{display: flex; font-size: 11px;}
.pd-desc{padding-top: 20px;}
.bc-pd{
	color: @lightGreen;
	a{
		color: @lightGreen;
		&:last-of-type{
			padding-right: 0; margin-right: 0;
			&:after{display: none;}
		}
	}
	span{display: none;}
	@media (max-width: @m){display: none;}
}
.pd-hero-image{
	margin-top: 23px;
	img{display: block; border-radius: @borderRadius; width: 100%; height: auto; margin: auto;}
	@media (max-width: @m){
		margin-left: -15px; margin-right: -15px; margin-top: 15px;
		img{border-radius: 0; width: 100%; height: auto;}
	}
}
.pd-content-wrapper{
	width: 740px; margin: auto;
	@media (max-width: @tp){width: auto;}
}
.pd-short-description{
	font-size: 20px; line-height: 1.6; box-shadow: 0 10px 30px 0 rgba(0,0,0,0.1); background: url(images/bg.jpg); padding: 40px 50px 25px; margin: -60px -50px 0; position: relative; z-index: 1; border-radius: @borderRadius;
	@media (max-width: @t){font-size: 16px;}
	@media (max-width: @tp){margin: -45px -25px 0; padding: 28px 40px 15px;}
	@media (max-width: @m){margin: -15px 0 0; padding: 15px 20px 0; font-size: 14px;}
}
.pd-info{
	font-size: 12px; padding-bottom: 25px; padding-top: 30px; display: flex;
	@media (max-width: @m){padding: 0; font-size: 11px; margin-bottom: 10px;}
}
@media (max-width: @m){
	.pd-info-link{margin-top: 20px;}
}
.pd-info-link-products{border-left: 1px solid #DEDEDE; margin-left: 10px; padding-left: 10px;}
.pd-footer{
	display: flex; justify-content: space-between; align-items: flex-end; font-size: 14px; margin-bottom: 55px;
	@media (max-width: @m){flex-direction: column-reverse; align-items: flex-start; margin-top: 25px; margin-bottom: 40px;}
}
.pd-share{
	margin: 0;
	@media (max-width: @t){padding-top: 20px;}
	@media (max-width: @m){font-size: 12px; padding-top: 25px;}
}
.pd-author{
	text-align: right; color: @gray2;
	@media (max-width: @m){text-align: left; font-size: 12px;}
}
.pd-all-author-posts{color: @green;}

.tags{
	border: 1px solid @borderColor; background: #fff; margin-top: 30px; margin-bottom: 35px; border-radius: @borderRadius; padding: 11px 20px 13px 51px; font-size: 14px; position: relative;
	&:before{.icon-tag(); font: 23px/23px @fonti; .rotate(-45deg); position: absolute; top: 12px; left: 18px; color: @green;}
	a{
		color: @green;
		&:hover{color: @darkGreen;}
	}
	.comma:last-of-type{display: none;}
	@media (max-width: @t){margin-top: 20px; margin-bottom: 25px;}
	@media (max-width: @m){
		font-size: 12px; padding: 8px 20px 9px 45px; margin: 10px 0;
		&:before{font-size: 20px; line-height: 20px; left: 14px; top: 8px;}
	}
}
@media (max-width: @m){
	.pd-related-products{padding-top: 10px; margin-left: -15px; margin-right: -15px;}
	.pd-related-recipes .subtitle{padding-left: 0;}
}
.pd-related-btns{
	padding: 25px 0 60px; text-align: right;
	@media (max-width: @m){padding: 15px 15px 40px 121px;}
}
.btn-add-all{
	height: 54px; min-width: 270px; padding: 0 20px;
	&>span{
		display: flex;
		&:before{.icon-cart(); font: 23px/23px @fonti; color: #fff; margin-right: 12px;}
	}
	@media (max-width: @t){
		height: 47px; min-width: 245px;
		&>span:before{font-size: 20px; line-height: 20px;}
	}
	@media (max-width: @tp){min-width: 235px;}
	@media (max-width: @m){
		min-width: 0; width: 100%; padding: 0;
		&>span:before{display: none;}
	}
}
.related-products-add-message{
	background: @green; border-radius: @borderRadius; font-size: 14px; text-align: left; color: #fff; padding: 10px 20px; margin-bottom: 15px; display: none;
	&.active{display: block;}
}
.subtitle{
	font-size: 26px; font-weight: bold; padding-bottom: 15px;
	@media (max-width: @t){font-size: 22px;}
	@media (max-width: @m){font-size: 16px;}
}
@media (max-width: @m){
	.related-title{padding-left: 15px;}
}

.pd-related{
	padding: 45px 0;
	@media (max-width: @t){background-image: url(images/bg.jpg);}
	@media (max-width: @tp){padding-left: 75px; padding-right: 75px;}
	@media (max-width: @m){padding: 25px 15px;}
}
.pd-related-posts{
	padding-bottom: 20px;
	@media (max-width: @m){
		.related-title{padding-left: 0;}
	}
}

.pd-navigation{
	position: absolute; overflow: hidden; width: 95px; height: 95px; text-align: left; text-decoration: none; overflow: hidden; font-size: 15px; bottom: calc(~"50% - 105px"); line-height: 1.4; color: @textColor!important; font-weight: bold; .transition(all);
	&>span{position: absolute; display: flex; align-items: center; top: 0; right: 95px; width: 295px; height: 100%;}
	span span{padding-left: 25px; padding-right: 25px;}
	&:hover{
		text-decoration: none; width: 390px; background: #fff;
		&:after{opacity: 1;}
	}
	&:after{.pseudo(95px,100%); .gradient-green; top: 0; right: 0; opacity: 0; .transition(opacity);}
	&:before{.pseudo(95px,100%); display: flex; align-items: center; justify-content: center; .icon-arrow-right(); z-index: 10; font: 36px/36px @fonti; color: #fff; top: 0; right: 0;}
	@media (max-width: @l){
		bottom: calc(~"50% - 130px");
		width: 80px; height: 80px;
		&:after, &:before{width: 80px;}
	}
	@media (max-width: @t){display: none;}
}
.pd-navigation-prev{
	left: calc(~"(100vw - 1450px) / 2"); border-top-right-radius: @borderRadius; border-bottom-right-radius: @borderRadius;
	&:before{.scaleX(-1); right: auto; left: 0;}
	&:after{right: auto; left: 0;}
	&:hover{left: 0;}
	&>span{right: auto; left: 95px;}
	@media (max-width: @l){left: calc(~"(100vw - 1000px) / 2 - 100px");}
}
.pd-navigation-next{
	right: calc(~"(100vw - 1450px) / 2"); border-top-left-radius: @borderRadius; border-bottom-left-radius: @borderRadius;
	&:hover{right: 0;}
	@media (max-width: @l){right: calc(~"(100vw - 1000px) / 2 - 100px");}
}

.pd-thumbs{
	img{display: block;}
	a{display: block; margin-bottom: 30px;}
}
/*------- /publish detail -------*/

/*------- filter -------*/
.recipes-sidebar-hellobar:not(.hello-bar-none){
	.cf-recipes{
		top: 256px;
		@media (max-width: @t){top: 231px;}
	}
}
.cf-recipes{
	background: #fff; padding: 25px 30px; border-radius: @borderRadius; .box-shadow-yellow; position: absolute; top: 160px; width: 360px;
	@media (max-width: @l){width: 300px;}
	@media (max-width: @t){width: 245px; top: 135px; padding: 20px 25px;}
	.toggle-icon{display: none;}

	.cf-item{
		margin-bottom: 30px;
		&:last-child{margin-bottom: 0;}
	}
}
.cf-filter-header{
	display: none;
	@media (max-width: @tp){display: block; font-size: 15px; padding: 15px 20px; background: url(images/header-t.jpg); background-size: cover; color: #fff;}
	&>span{display: flex; align-items: center;}
	@media (max-width: @m){padding: 14px 15px; font-size: 14px;}
}
.cf-title{
	font-size: 15px; font-weight: bold; position: relative; cursor: default;
	.toggle-icon{position: absolute; right: 0; top: 5px;}
	@media (max-width: @t){font-size: 14px;}
}
/*
.cf-row{
	position: relative; margin-bottom: 2px;
	input[type=checkbox]+label{font-size: 12px; width: 100%; padding-right: 30px;}
	input[type=checkbox]:checked+label{font-weight: bold;}
	@media (max-width: @t){margin-bottom: 6px;}
}
*/
.cf-row-unavailable .cf-counter{display: none;}
.cf-counter{position: absolute; top: 3px; right: 0; color: @gray2; font-weight: normal;}
/*
.cf-item{
	input[type=text]{width: 100%; height: 45px;}
	@media (min-width: @tp){
		&.active{
			.cf-title .toggle-icon:before{display: none;}
			.cf-item-wrapper{max-height: 300px; padding-top: 8px;}
		}
	}
}
*/
.cf-item-categories .cf-row-level1{
	font-weight: bold; padding-top: 5px;
	input[type=checkbox]+label{
		padding-left: 0;
		&:before{display: none;}
	}
	input[type=checkbox]:disabled + label{color: @textColor; cursor: default!important;}
}
/*
.cf-item-wrapper{
	overflow: auto; max-height: 0; padding-right: 7px; margin-right: -7px; .transition(all);
	&::-webkit-scrollbar{-webkit-appearance: none; width: 3px; background: #E0E3DF; border-radius: @borderRadius;}
	&::-webkit-scrollbar-thumb {background-color: #9FA1A3; border-radius: @borderRadius;box-shadow: 0 0 1px rgba(255,255,255,.5);}
}
*/

.cf-active{margin-bottom: 30px; font-size: 12px;}

.cf-active-item{margin-bottom: 3px;}
.cf-active-item-link{
	text-decoration: none; position: relative; padding-left: 20px; cursor: pointer; .transition(color);
	&:before{.icon-cross(); font: 11px/11px @fonti; position: absolute; left: 0; top: 3px; .transition(color);}
	&:hover{
		color: @green; text-decoration: underline;
		&:before{color: @green;}
	}
}
.cf-active-title{font-weight: bold; font-size: 15px; padding-bottom: 6px;}
.btn-cf-active-clear{
	background: #E9ECEB; font-size: 14px; text-decoration: none; padding: 10px 25px; border-radius: @borderRadius; display: inline-flex; margin-top: 11px; cursor: pointer; .transition(all);
	&:hover{text-decoration: none; background: #E9ECEB/1.05; color: @green;}
	@media (max-width: @m){font-size: 11px; font-weight: bold;}
}
.cf-btns{display: none;}

.cf-products .cf-item{
	border-top: 1px solid @borderColor; padding: 10px 0;
	@media (max-width: @tp){border-top: 0; padding: 0;}
}

.nav-categories-sidebar{
	font-size: 12px; padding-bottom: 5px; list-style: none; padding: 0; margin: 0;
	a{
		text-decoration: none; display: block; padding-right: 30px;
		&:hover{color: @green; text-decoration: underline;}
	}
	li{margin-top: 5px; position: relative;}
	.selected a{color: @gray2;}
}

@media (max-width: @tp){
	.cf{background: #fff; position: relative; top: auto; width: auto; box-shadow: none; padding: 0; height: 100%; width: 100%; border-radius: 0; box-shadow: 0 15px 30px 0 rgba(228,197,113,0.2); order: 3;}
	.cf-item{
		border-bottom: 1px solid @borderColor; margin: 0!important;
		&.active{
			.cf-item-wrapper{max-height: inherit; overflow-y: inherit; padding-left: 16px; padding-right: 16px; padding-bottom: 13px;}
			.toggle-icon:before{display: none;}
		}
	}
	.cf-item-wrapper{margin-right: 0; overflow: auto; max-height: 0; transition: height 0.3s, padding-bottom 0.3s;}
	.cf-title{
		font-size: 16px; padding: 12px 20px; position: relative;
		.toggle-icon{position: absolute; right: 20px; top: 17px; display: block;}
	}
	.cf-row{
		margin-bottom: 5px;
		input[type=checkbox]+label{font-size: 14px;}
	}
	.cf-active{margin: 20px 0 10px;}
	.cf-body{overflow: auto; max-height: calc(~"100% - 52px");}
	.cf-btns{
		display: flex; padding: 20px; justify-content: space-between; width: 100%; gap: 8px;
		.btn{width: calc(~"50% - 5px"); padding: 0; flex-grow: 1;}
		&.active .btn{flex-grow: 0;}
	}
	.active-filter{
		overflow: hidden;
		.p-recipes-sidebar{display: block;}
		.zEWidget-launcher{visibility: hidden; pointer-events: none;}

		.c-col1{display: block; position: fixed; top: 0; left: 0; right: 0; bottom: 0; width: 100%; margin: 0; z-index: 600; background: rgba(0,0,0,.5);}
	}
}
@media (max-width: @m){
	.cf{width: 100%;}
	.cf-title{
		font-size: 14px; padding-left: 15px; padding-right: 15px;
		.toggle-icon{right: 15px;}
	}
	.cf-item{
		&.active{
			.cf-item-wrapper{padding-left: 15px; padding-right: 15px;}
		}
	}
	.cf-btns{padding: 15px;}
	.cf-row input[type=checkbox]+label{font-size: 12px;}
	.cf-active{padding: 5px 0 20px; margin: 0; border: 0; display: flex; flex-wrap: wrap;}
	.cf-active-title{display: none;}
	.cf-active-btns{width: 100%;}
	.cf-active-item{margin-right: 15px;}
}
.btn-m-cf-confirm{
	display: none;
	&.active{display: block;}
}
/*------- /filter -------*/

/*------- recipes -------*/
.page-recipes{
	.header{
		padding-bottom: 0; background-size: cover; background-position: center top; background-image: url(images/bg-dark.jpg);
		@media (max-width: @t) and (min-width: @m){background-image: url(images/bg-dark-t.jpg);}
	}
	@media (max-width: @t){background-image: url(images/bg.jpg);}
}
.p-items-recipes{
	grid-gap: 25px; grid-template-columns: repeat(auto-fill, calc(~"33.333% - 17px"));
	@media (max-width: @l){grid-gap: 15px; grid-template-columns: repeat(auto-fill, calc(~"33.333% - 10px"));}
	@media (max-width: @tp){grid-gap: 10px; grid-template-columns: repeat(auto-fill, calc(~"33.333% - 7px"));}
	@media (max-width: @m){grid-template-columns: repeat(auto-fill, calc(~"50% - 5px"));}
}
.p-recipes-header{
	color: #fff; padding: 0 0 15px 415px; font-size: 18px;
	@media (max-width: @l){padding-left: 355px;}
	@media (max-width: @t){padding-left: 305px; font-size: 14px; padding-right: 30px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px; padding-bottom: 5px; background: #fff; color: @textColor; position: relative;}
	@media (max-width: @m){
		padding-left: 15px; padding-right: 15px;
		.cf-active{padding-bottom: 10px;}
	}
}
.btn-toggle-filter{
	display: none; align-items: center; border: 1px solid @borderColor; height: 47px; border-radius: @borderRadius; text-decoration: none;  min-width: 180px; padding: 0 25px;
	&:hover{text-decoration: none;}
	span{position: relative; display: flex; align-items: center;}
	@media (max-width: @tp){display: flex;}
	@media (max-width: @m){height: 40px; font-size: 12px; max-width: 140px; padding: 0 15px; max-width: none; min-width: 0;}
}
.btn-toggle-recipe-filter{
	position: absolute; right: 20px; bottom: 20px;
	@media (max-width: @tp){position: relative; right: auto; bottom: auto; display: inline-flex;}
	@media (max-width: @m){position: relative; bottom: auto; right: auto; width: auto; margin-bottom: 10px; display: flex; max-width: 50%;}
}
.btn-toggle-catalog-filter{
	height: 50px; font-size: 14px;
	@media (max-width: @m){order: 1; margin: 0; width: calc(~"50% - 5px"); height: 40px; font-size: 12px;}
}
.btn-toggle-search-filter{
	width: 180px; margin: 20px 20px 0 auto;
	@media (max-width: @m){width: 44%; margin-right: auto; margin-left: 15px;}
}
.filter-icon{
	position: relative; width: 16px; height: 2px; margin-right: 15px; background: @lightGreen;
	&:before, &:after{.pseudo(auto, 2px); background: @lightGreen;}
	&:before{top: -6px; left: -3px; right: -3px;}
	&:after{bottom: -6px; left: 4px; right: 4px;}
}
.cf-filter-close{
	display: flex; align-items: center; justify-content: center; height: 50px; width: 46px; position: absolute; top: 0; right: 0;
}
.cf-filter-close-icon{
	width: 18px; height: 18px; position: relative;
	&:before, &:after{.pseudo(18px,2px); background: #fff; top: 8px;}
	&:before{.rotate(-45deg);}
	&:after{.rotate(45deg);}
}

@media (max-width: @t){
	.p-recipes-title{font-size: 30px;}
}
@media (max-width: @m){
	.p-recipes-title{font-size: 24px;}
}
.p-recipes-row{
	padding: 30px 0 70px;
	@media (max-width: @t){padding-left: 30px; padding-right: 30px;}
	@media (max-width: @tp){padding: 20px 20px 20px;}
	@media (max-width: @m){padding: 15px;}
}
.p-recipes-sidebar{
	width: 360px; flex-grow: 0; flex-shrink: 0; margin-right: 55px;
	@media (max-width: @l){width: 300px;}
	@media (max-width: @t){width: 245px; margin-right: 30px;}
	@media (max-width: @tp){margin: 0; display: none; position: fixed; left: 0; top: 0; bottom: 0; right: 0; width: 100%; z-index: 600; background: rgba(0,0,0,.5);}
}
.p-recipes-main{flex-grow: 1;}
.p-recipes-bc{
	padding: 37px 0 5px;
	a{color: @green;}
	@media (max-width: @t){padding-top: 20px;}
	@media (max-width: @m){padding-top: 15px;}
}
.p-recipes-description{
	max-width: 910px;
	@media (max-width: @m){max-width: none; font-size: 12px;}
}
.btn-load-more-recipes{
	margin-top: 45px; margin-bottom: 30px;
	@media (max-width: @m){margin-top: 20px;}
}
/*------- /recipes -------*/

/*------- recipe detail -------*/
.page-recipe-detail{
	.header{
		padding-bottom: 0; background-position: center bottom; background-size: cover; background-image: url(images/bg-dark.jpg);
		@media (max-width: @t){background-image: url(images/bg-dark-t.jpg);}
	}
}
.pd-recipe-main{background: #fff; border-top-left-radius: @borderRadius;}
.pd-recipe-intro{
	font-size: 14px; margin-bottom: 5px;
	a{color: #fff;}
	@media (max-width: @t){font-size: 12px;}
	@media (max-width: @m){
		order: 2; font-size: 11px; flex-wrap: wrap;
		a{color: @textColor;}
	}
}
.pd-recipe-rates{
	margin-left: 15px;
	@media (max-width: @m){margin-left: 0;}
}
.pd-recipe-link-comments{
	margin-left: 15px; font-size: 12px;
	a:hover{color: @lightGreen;}
}
.pd-recipe-info{
	justify-content: space-between; font-size: 12px; margin: 18px 0 20px;
	@media (max-width: @m){margin: 0 0 10px 0;}
}
@media (max-width: @m){
	.pd-recipe-info-container{
		min-height: 20px;
		.pd-recipe-info{display: flex;}
	}
}
.pd-recipe-image{
	img{border-radius: @borderRadius 0 0 @borderRadius; display: block;}
	@media (max-width: @m){
		margin-left: -15px; margin-right: -15px; margin-bottom: 15px;
		img{border-radius: 0;}		
	}
}
.pd-recipe-category{
	text-transform: uppercase; color: @lightGreen; font-weight: bold;
	@media (max-width: @m){width: 100%; padding-bottom: 6px; color: @green;}
}
.pd-recipe-date{color: @gray2;}
.pd-recipe-header-wrapper{
	padding: 0 0 405px 125px;
	@media (max-width: @t){padding: 10px 70px 285px;}
	@media (max-width: @tp){padding-bottom: 160px;}
	@media (max-width: @m){padding: 15px 15px 10px; display: flex; flex-direction: column;}
}
.pd-recipe-title{
	color: #fff; font-size: 40px; max-width: 750px;
	@media (max-width: @l){font-size: 36px;}
	@media (max-width: @t){font-size: 30px;}
	@media (max-width: @tp){font-size: 28px; max-width: 470px;}
	@media (max-width: @m){color: @textColor; font-size: 20px; order: 1;}
}
.pd-recipe-sidebar{
	width: 480px; flex-grow: 0; flex-shrink: 0; background: #fff; box-shadow: 0 10px 30px 0 rgba(0,0,0,0.1); border-radius: @borderRadius; border-top-left-radius: 0; align-self: flex-start;
	@media (max-width: @l){width: 390px;}
	@media (max-width: @t){width: 350px;}
	@media (max-width: @tp){width: 250px;}
	@media (max-width: @m){
		width: 100%; box-shadow: none; margin-bottom: 15px; position: relative; padding-bottom: 7px; display: none;
		&:after{.pseudo(auto,1px); left: -15px; right: -15px; bottom: 0; background: @borderColor;}
	}
}
@media (max-width: @m){
	.pd-recipe-sidebar-m{
		//min-height: 220px;
		.pd-recipe-sidebar{display: block;}
	}
}
.pd-recipe-sidebar-top{
	padding: 30px 45px;
	@media (max-width: @t){padding: 25px;}
	@media (max-width: @tp){padding: 17px 20px;}
	@media (max-width: @m){padding: 0;}
}
.pd-recipe-bc{
	padding: 0 0 10px;
	span{display: none;}
	a{
		color: @lightGreen;
		&:last-of-type:after{display: none;}
	}
	@media (max-width: @m){display: none;}
}
.pd-recipe-wrapper{
	position: relative; margin-top: -390px;
	@media (max-width: @t){padding-left: 30px; padding-right: 30px; width: auto; margin-top: -270px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px; margin-top: -150px;}
	@media (max-width: @m){margin-top: 0; padding-left: 15px; padding-right: 15px; display: block;}
}
.pd-recipe-attr{
	font-size: 14px; padding-left: 30px; position: relative;
	@media (max-width: @tp){font-size: 13px;}
	@media (max-width: @m){font-size: 12px;}
}
.pd-recipe-attr-time{
	position: relative; margin-bottom: 15px;
	&:before{.icon-clock(); font: 20px/20px @fonti; color: @green; position: absolute; left: 0; top: 1px;}
	@media (max-width: @tp){
		&:before{font-size: 17px;}
	}
	@media (max-width: @m){margin-bottom: 10px;}
}
.pd-recipe-attr-complexity{
	margin-bottom: 30px;
	&.last{margin-bottom: 0;}
	@media (max-width: @tp){margin-bottom: 15px;}
	@media (max-width: @m){
		margin-bottom: 10px;
		&.last{margin-bottom: 10px;}	
	}
}
.pd-recipe-attr-image{
	position: absolute; left: 0; top: 0;
	img{width: 17px;}
}
.pd-recipe-attr-value{font-weight: bold;}

.pd-recipe-attr-ingredients{
	font-size: 16px; line-height: 1.4;
	p{padding: 0 0 7px;}
	a{
		color: @green;
		&:hover{color: @darkGreen;}
	}
	ul{list-style: none;}
	li{padding-bottom: 7px;}
	@media (max-width: @t){font-size: 14px;}
	@media (max-width: @tp){font-size: 13px;}
	@media (max-width: @m){font-size: 12px;}
}
.pd-recipe-attr-ingredients-title{
	font-size: 24px; line-height: 1.5; font-weight: bold; position: relative; padding-bottom: 6px;
	&:before{.icon-ingredients(); font: 20px/20px @fonti; color: @green; position: absolute; left: -30px; top: 8px;}
	@media (max-width: @t){
		font-size: 20px;
		&:before{top: 5px;}
	}
	@media (max-width: @tp){
		font-size: 18px;
		&:before{font-size: 17px; line-height: 17px;}
	}
	@media (max-width: @m){
		font-size: 16px;
		&:before{top: 3px;}
	}
}
.pd-recipe-attr-counter{color: @green; font-weight: normal; font-size: 14px;}
.pd-recipe-cnt{
	padding-left: 130px; padding-right: 130px;
	@media (max-width: @l){padding-left: 50px; padding-right: 50px;}
	@media (max-width: @t){padding-left: 27px;}
	@media (max-width: @tp){padding-right: 20px;}
	@media (max-width: @m){padding-right: 0; padding-left: 0;}
	ol{
		margin-left: 0;
		li{margin-bottom: 30px;}
	}
}
.pd-recipe-share{
	padding: 0 0 60px;
	@media (max-width: @m){margin-bottom: 40px; padding: 15px 0 0 ;}
}
.pd-recipe-related-title{
	font-size: 26px; font-weight: bold; padding: 0 0 15px;
	@media (max-width: @t){font-size: 22px;}
	@media (max-width: @tp){font-size: 20px;}
	@media (max-width: @m){font-size: 16px;}
}
.pd-recipe-related{
	padding-bottom: 120px;
	@media (max-width: @t){padding-bottom: 60px;}
	@media (max-width: @tp){
		.pp-recipe-time{margin-bottom: 7px;}
	}
	@media (max-width: @m){padding-bottom: 15px; content-visibility: auto; contain-intrinsic-size: 350px;}
}
.pd-recipe-related-products-title{
	font-size: 24px; line-height: 1.3; font-weight: bold; padding: 0 0 20px 30px; margin: 10px 0 0 45px; position: relative;
	&:before{.icon-cart(); font: 22px/22px @fonti; color: @green; position: absolute; left: -1px; top: 3px;}
	@media (max-width: @t){
		margin-left: 25px; font-size: 20px;
		&:before{top: 1px;}
	}
	@media (max-width: @tp){
		margin-left: 0; padding-left: 35px;
	}
	@media (max-width: @m){
		font-size: 16px; padding-bottom: 15px;
		&:before{font-size: 20px; line-height: 20px; top: -1px;}
	}
}
@media (max-width: @m){
	.pd-recipe-products{margin-left: -15px; margin-right: -15px;}
}
.pd-recipe-products-btns{
	border-top: 1px solid @borderColor; padding: 20px;
	.btn{width: 100%;}
	@media (max-width: @tp){
		padding: 20px 0 50px; text-align: right;
		.btn{max-width: 240px;}
	}
	@media (max-width: @m){
		padding-left: 110px;
		.btn{max-width: none;}
	}
}
.btn-show-related{
	display: none;
	@media (max-width: @tp){display: flex; margin-top: 20px; padding: 0;}
	@media (max-width: @m){
		background: none; color: @green; text-decoration: underline; font-size: 12px; padding: 0; font-weight: bold; height: auto; margin: 5px 0 10px 30px; justify-content: flex-start;
		&:after{display: none;}
		&:hover{color: @green; text-decoration: underline;}
	}
}
@media (max-width: @t){
	.pd-recipe-related-items .pp-recipe-list{
		.pp-recipe-title{font-size: 14px;}
		.pp-recipe-attrs{font-size: 12px; justify-content: space-between;}
		.pp-recipe-attr{margin-right: 0;}
	}
}
@media (max-width: @tp){
	.pd-recipe-related-items .pp-recipe-list{
		.pp-recipe-image{width: 200px;}
		.pp-recipe-title{font-size: 13px;}
		.pp-recipe-category{font-size: 11px;}
		.pp-recipe-attr{margin-right: 15px;}
	}
}
@media (max-width: @m){
	.pd-recipe-related-products{padding-top: 20px;}
	.pd-recipe-related-items .pp-recipe-list .pp-recipe-image{width: 140px;}
}
/*------- /recipe detail -------*/

/*------- recipes widget -------*/
@media (max-width: @t){
	.wrapper-recipes{padding-left: 30px; padding-right: 30px;}
}
@media (max-width: @tp){
	.wrapper-recipes{padding-left: 20px; padding-right: 20px;}
	.pw-recipe-items{grid-gap: 10px; grid-template-columns: repeat(auto-fill, calc(~"50% - 5px"));}
}
@media (max-width: @m){
	.wrapper-recipes{flex-direction: column; padding: 0;}
	.pw-recipe-items{
		display: flex; overflow: auto;
		.pp-recipe{
			min-width: 145px; margin: 0 10px 10px 0;
			&:first-child{margin-left: 15px;}
		}
	}
}
.pw-recipes{
	background-size: cover; background-position: center bottom; background-repeat: no-repeat; color: #fff; margin: -210px 0 0; padding: 345px 0 135px 0; contain-intrinsic-size: 1200px;
	.pp-recipe{border: 0;}
	@media (max-width: @l){margin-top: -175px; padding-top: 280px;}
	@media (max-width: @t){margin-top: -135px; padding-top: 200px; padding-bottom: 60px;}
	@media (max-width: @tp){padding-top: 185px;}
	@media (max-width: @m){margin-top: 0; padding-top: 0; background: none!important; padding-bottom: 35px;}
}
.pw-recipes-title{
	position: relative; .circle-icon; font-size: 42px; line-height: 1.3; font-weight: bold; margin-bottom: 20px;
	&:before{.icon-spoon(); font-size: 30px; position: absolute; left: -85px; top: -3px;}
	@media (max-width: @l){
		font-size: 38px;
		&:lang(de){font-size: 32px;}
	}
	@media (max-width: @t){
		font-size: 28px;
		&:before{left: -65px; top: -7px;}
	}
	@media (max-width: @tp){
		&:before{font-size: 25px;}
	}
	@media (max-width: @tp){font-size: 26px;}
	@media (max-width: @m){
		font-size: 18px; padding: 0 0 0 40px; margin-bottom: 15px;
		&:before{left: 0; top: -2px; font-size: 16px;}
	}
}
.pw-recipes-col1{
	width: 528px; flex-shrink: 0; flex-grow: 0; padding-left: 95px;
	@media (max-width: @l){width: 400px;}
	@media (max-width: @t){width: 310px; padding-left: 65px;}
	@media (max-width: @m){padding: 25px 15px 20px; width: auto; background: url(images/recipes-m.jpg) no-repeat bottom right; background-size: cover;}
}
.pw-recipes-col2{
	flex-grow: 1; padding-top: 15px;
	@media (max-width: @t){padding-top: 5px;}
	@media (max-width: @m){padding: 15px 0;}
}
.nav-recipes{
	list-style: none; padding: 0; margin: 0 0 32px; font-size: 15px; font-weight: bold;
	li{margin-bottom: 6px;}
	a{
		color: #fff; text-decoration: none; position: relative; padding: 0 0 0 20px;
		&:before{.pseudo(6px,6px); background: @green; border-radius: 100px; left: 0; top: 8px; .transition(all);}
		&:hover{
			color: @lightGreen;
			&:before{width: 12px; height: 12px; top: 5px; left: -3px;}
		}
	}
	@media (max-width: @t){font-size: 13px;}
	@media (max-width: @m){
		display: flex; flex-wrap: wrap; font-size: 12px; margin: 0 0 0 40px;
		li{width: 50%;}
		a{
			padding: 0; text-decoration: underline;
			&:before{display: none;}
		}
	}
}
.recipes-sidebar-title{
	font-size: 18px; font-weight: bold; color: @lightGreen; padding: 0 0 8px;
	@media (max-width: @t){font-size: 16px;}
}
@media (max-width: @m){
	.recipes-sidebar-filters, .recipes-sidebar-title{display: none;}
}
.pw-btns{
	text-align: center; padding-top: 40px;
	@media (max-width: @tp){
		text-align: left; padding-top: 0;
		.btn{padding: 0 30px;}
	}
	@media (max-width: @m){
		padding: 0 15px;
		.btn{width: 100%;}
	}
}
/*------- /recipes widget -------*/

/*------- recipe post -------*/
.pp-recipe{
	background: #fff; border-radius: @borderRadius; overflow: hidden; display: flex; flex-direction: column; color: @textColor; text-decoration: none; position: relative; border: 1px solid @borderColor;
	&:hover{text-decoration: none;}
	&:before{.pseudo(auto,auto); left: 0; top: 0; right: 0; bottom: 0; .gradient-green; opacity: 0; .transition(opacity);}
	@media (min-width: @t){
		&:hover{
			color: #fff; border-color: transparent; z-index: 10;
			&:before{opacity: 1;}
			.pp-recipe-category, .pp-recipe-attr:before, .pp-recipe-comments-counter{color: #fff;}
		}		
	}
	@media (max-width: @m){border: 0; background: none; margin-bottom: 25px;}
}
.pp-recipe-image{
	display: flex; align-items: center; justify-content: center; padding: 12px; position: relative; z-index: 1;
	a{display: block; width: 100%;}
	img{width: auto; height: auto; display: block; border-radius: @borderRadius; margin: auto;}
	&.loaded{background-color: transparent;}
	@media (max-width: @l){padding: 8px;}
	@media (max-width: @m){padding: 0; margin-bottom: 6px;}
}
.pp-recipe-title{
	font-weight: normal; font-size: 16px; line-height: 1.4; padding: 0; flex-grow: 1; display: block;
	@media (max-width: @t){font-size: 13px;}
	@media (max-width: @m){font-size: 12px;}
}
.pp-recipe-cnt{
	padding: 0 25px 15px; flex-grow: 1; display: flex; flex-direction: column; position: relative; z-index: 1;
	@media (max-width: @l){padding: 0 15px 15px;}
	@media (max-width: @m){padding: 0;}
}
.pp-recipe-category{
	text-transform: uppercase; color: @green; font-weight: bold; font-size: 12px; line-height: 1.4; margin-bottom: 2px; .transition(all); padding-right: 70px;
	@media (max-width: @t){font-size: 11px;}
	@media (max-width: @m){font-size: 10px;}
}
.pp-recipe-attrs{
	font-size: 14px; padding-top: 30px; justify-content: space-between;
	&:lang(de){flex-direction: column; gap: 5px;}
	@media (max-width: @l){font-size: 12px;}
	@media (max-width: @t){display: block;}
	@media (max-width: @tp){
		padding-top: 10px; font-size: 11px; display: flex; flex-wrap: wrap;
		&:lang(de){gap: 0;}
	}
}
.pp-recipe-attr{
	position: relative; display: flex; align-items: center;
	&:before{.icon-clock(); font: 20px/20px @fonti; color: @green; margin: 0 7px 0 0; .transition(all);}
	strong{padding-left: 4px;}
	@media (max-width: @t){
		padding-top: 6px;
		&:before{font-size: 17px; line-height: 17px;}
	}
	@media (max-width: @m){
		width: 100%;
		&:before{font-size: 14px; line-height: 14px;}
	}
}
.pp-recipe-ingredients:before{.icon-ingredients();}
.pp-recipe-rate{
	font-size: 10px; display: block; position: absolute; top: 2px; right: 25px; display: flex; align-items: center;
	@media (max-width: @t){display: none;}
}
.pp-recipe-comments-counter{font-size: 12px; color: @gray2; .transition(color);}

.pp-recipe-list{
	flex-direction: row; background: none; margin-bottom: 25px; overflow: visible; align-items: center; border: 0;
	.pp-recipe-image{
		width: 260px; min-height: 135px; flex-grow: 0; flex-shrink: 0; padding: 0;
		@media (max-width: @l){width: 235px;}
	}
	&:before{background: #fff; top: -12px; right: -12px; bottom: -12px; left: -12px; .box-shadow-yellow; border-radius: @borderRadius;}
	&.gray-shadow:before{.box-shadow-gray;}
	&:hover{
		color: @textColor;
		.pp-recipe-category, .pp-recipe-attr:before{color: @green;}
		.pp-recipe-comments-counter{color: @gray2;}
	}
	.pp-recipe-attrs{justify-content: flex-start; padding-top: 10px;}
	.pp-recipe-attr{margin-right: 40px;}
	.pp-recipe-cnt{padding: 5px 0 5px 30px;}
	.pp-recipe-title{flex-grow: 0; font-weight: bold;}
	.pp-recipe-rate{position: relative; right: auto; top: auto; padding-bottom: 2px;}
	@media (max-width: @t){
		.pp-recipe-rate{display: block;}
		.pp-recipe-title{font-size: 16px;}
		.pp-recipe-attrs{display: flex; font-size: 14px;}
		.pp-recipe-attr{padding-top: 0;}
		.pp-recipe-category{font-size: 12px;}
		.pp-recipe-image{width: 230px; min-height: 0;}
	}
	@media (max-width: @tp){
		align-items: flex-start;
		.pp-recipe-cnt{padding-left: 15px;}
		.pp-recipe-attr{margin-right: 0;}
		.pp-recipe-time{
			display: block; margin-right: 15px;
			&:before{position: relative; top: 2px;}
		}
	}
	@media (max-width: @m){
		align-items: flex-start;
		.pp-recipe-image{width: 140px;}
		.pp-recipe-title{font-size: 12px; font-weight: normal;}
		.pp-recipe-rate{display: none;}
		.pp-recipe-attrs{font-size: 12px;}
		.pp-recipe-attr:before{font-size: 14px; line-height: 14px;}
		.pp-recipe-cnt{padding-left: 12px;}
		.pp-recipe-time{margin-bottom: 3px;}
	}
}
@media (max-width: @t){
	.cd-pp-recipe{
		.pp-recipe-title{font-size: 14px;}
		.pp-recipe-image{width: 200px;}
		.pp-recipe-cnt{padding-left: 15px;}
		.pp-recipe-attrs{font-size: 12px; display: flex; flex-flow: column; gap: 5px;}
	}
}
@media (max-width: @tp){
	.cd-pp-recipe{
		.pp-recipe-title{font-size: 13px;}
		.pp-recipe-image{width: 170px; padding-top: 10px;}
	}
}
@media (max-width: @m){
	.cd-pp-recipe{
		.pp-recipe-image{width: 140px;}
		.pp-recipe-cnt{padding-left: 12px;}
	}
}
/*------- /recipe post -------*/

/*------- instashop -------*/
body.instashop-active{overflow: hidden;}
.iw{
	margin: 90px 0 60px; content-visibility: auto; contain-intrinsic-size: 900px;
	@media (max-width: @t){margin: 65px 30px 50px;}
	@media (max-width: @tp){margin: 45px 20px 60px;}
	@media (max-width: @m){background: url(images/bg.jpg); margin: 0; padding: 25px 15px 35px;}
}
.iw-title{
	text-align: center; font-size: 42px; font-weight: bold; padding: 0 0 45px;
	span{color: @green;}
	@media (max-width: @t){font-size: 28px; padding-bottom: 30px;}
	@media (max-width: @tp){font-size: 26px;}
	@media (max-width: @m){font-size: 20px; text-align: left; padding-bottom: 15px;}
}
.iw-btns{
	text-align: center; padding-top: 50px;
	.btn{min-width: 180px;}
	@media (max-width: @tp){padding-top: 35px;}
	@media (max-width: @m){padding-top: 20px;}
}
.instashop-items{
	display: grid; grid-template-columns: repeat(4, 1fr); grid-gap: 20px;
	@media (max-width: @l){grid-gap: 15px;}
	@media (max-width: @tp){grid-gap: 10px;}
	@media (max-width: @m){
		grid-template-columns: repeat(2, 1fr);
	}
}
@media (max-width: @tp){
	.instashop-title{font-size: 28px; padding: 0;}
}
.pd-instashop-close, .instashop-nav{
	display: none!important; top: 5px; right: 5px;
	&.active{display: flex!important;}
	@media (max-width: @instashopTp){display: flex!important;}
}
@media (max-width: @instashopTp){
	.instashop-nav{position: absolute; top: calc(~"50% - 25px"); font-size: 0; z-index: 100; left: -12px; right: -12px;}
	.instashop-nav-btn{
		width: 50px; height: 50px; display: flex; position: absolute; top: 0; align-items: center; justify-content: center; background: #fff; border-radius: 100px; text-decoration: none;
		&:before{.icon-arrow-right(); font: 18px/18px @fonti; color: @textColor; text-indent: -5px;}
		&:hover{text-decoration: none;}
	}
	.instashop-nav-prev{left: -10px;
		&:before{.scaleX(-1);}
	}
	.instashop-nav-next{right: -10px;}
	.pd-instashop-body{position: relative; overflow: hidden;}
}
@media (max-width: @m){
	.instashop-title{font-size: 20px;}
	.pd-instashop-close{width: 30px; height: 30px; top: 5px; right: 5px;}

	.instashop-nav-next{right: 0;}
	.instashop-nav-prev{left: 0;}
}

.instashop-active, .active-cart-modal{
	.zEWidget-launcher{visibility: hidden; pointer-events: none;}
}
.page-instashop{
	background-image: url(images/bg.jpg);
	.header{padding-bottom: 0;}
}
.instashop-header{
	text-align: center; padding: 30px 0;
	@media (max-width: @m){padding: 15px 0 20px; text-align: left;}
}
.instashop-title span{color: @lightGreen;}
.bc-instashop{
	padding: 0 0 10px;
	a{color: @lightGreen;}
}
.wrapper-instashop{
	margin-bottom: 100px;
	@media (max-width: @t){padding-left: 30px; padding-right: 30px;}
	@media (max-width: @tp){margin-bottom: 75px;}
	@media (max-width: @m){padding-left: 15px; padding-right: 15px; margin-bottom: 30px;}
}
.btn-load-more-instashop{margin-top: 50px;}
.pd-instashop-body{
	display: flex; padding: 40px;
	@media (max-width: @l){padding: 12px;}
	@media (max-width: @instashopTp){display: block; padding: 20px;}
	@media (max-width: @m){padding: 0;}
}
.pd-instashop-col1{
	width: 100%; flex-grow: 0; flex-shrink: 0; max-width: 792px;
	@media (max-width: 1700px){max-width: 47%;}
	@media (max-width: @instashopTp){max-width: 100%;}
	@media (max-width: 800px){aspect-ratio: 1;}
	/*
	@media (max-width: @instashopL){width: 550px;}
	@media (max-width: @instashopT){width: 350px;} 
	@media (max-width: @instashopTp){width: 100%; margin-bottom: 20px; position: relative;}
	@media (max-width: @m){padding: 15px; margin: 0; overflow: hidden;}
	*/
}
.pd-instashop-col2{
	flex-grow: 1; margin-left: 20px;
	@media (max-width: @instashopTp){margin-left: 0;}
}
.pd-instashop-image{
	position: relative; z-index: 50;
	img{display: block; border-radius: @borderRadius; width: 100%;}
}
.pd-instashop-point{
	position: absolute; height: 50px; text-decoration: none; font-size: 13px; display: block;
	&.active{
		text-decoration: none; color: #fff; z-index: 20;
		.pd-instashop-point-tip{opacity: 1; pointer-events: auto;}
		.pd-instashop-point-num:after{opacity: 1;}
	}
	@media (max-width: @m){font-size: 11px;}
}
.pd-instashop-point-num{
	display: flex; position: relative; align-items: center; justify-content: center; font-weight: bold; width: 36px; height: 36px; background: #fff; border-radius: 100px;
	span{position: relative; z-index: 1;}
	&:after{.pseudo(auto,auto); top: 0; right: 0; bottom: 0; left: 0; .gradient-green; border-radius: 100px; border: 1px solid @lightGreen; opacity: 0; .transition(opacity);}
	@media (max-width: @m){width: 26px; height: 26px;}
}
.pd-instashop-point-tip{
	position: absolute; top: 50px; .gradient-green; color: #fff; width: 160px; height: 30px; display: flex; align-items: center; justify-content: center; border-radius: @borderRadius; box-shadow: 0 -10px 20px 0 rgba(0,0,0,0.6); left: -62px; opacity: 0; pointer-events: none; border: 1px solid @lightGreen; .transition(opacity);
	span{position: relative; z-index: 1;}
	a{text-decoration: none; color: #fff;}
	&:before{.pseudo(10px,10px); background: @lightGreen; .rotate(45deg); left: 50%; margin-left: -5px; top: -5px;}
	@media (max-width: @m){top: 37px; left: -67px;}
}
.fancybox-instashop{
	.fancybox-nav{width: 80px; background: none;}
	.fancybox-prev{left: -80px;}
	.fancybox-next{right: -80px;}
	.fancybox-prev, .fancybox-next{
		span{
			background: none; width: 40px; height: 60px; visibility: visible;
			&:before{.icon-arrow-right(); font: 60px/60px @fonti; color: #fff; display: block; .transition(color);}
			@media (max-width: @l){
				&:before{font-size: 45px;}
			}
			@media (max-width: @tp){
				&:before{font-size: 35px;}
			}
		}
		&:hover span:before{color: @lightGreen;}
	}
	.fancybox-prev span:before{.scaleX(-1);}
	@media (max-width: @t){
		.fancybox-nav{width: 60px;}
		.fancybox-prev{left: -60px;}
		.fancybox-next{right: -60px;}
	}
}
.fancybox-inner{border-radius: @borderRadius;}
/*------- /instashop -------*/

/*------- instashop post -------*/
.ip{
	position: relative; color: #fff; font-size: 15px; font-weight: bold; border-radius: @borderRadius;
	img{display: block; width: 100%; border-radius: @borderRadius;.transition(all);}
	@media (min-width: @t){
		&:hover{
			color: #fff;
			img{box-shadow: 0 15px 40px 0 rgba(36,69,56,0.3);}
			.ip-overlay{opacity: 1;}
		}
	}
}
.ip-overlay{
	position: absolute; top: 0; left: 0; right: 0; bottom: 0; border-radius: @borderRadius; display: flex; align-items: center; justify-content: center; z-index: 1; text-align: center; background: rgba(36,69,56,.8); opacity: 0; .transition(opacity);
	span:before{.icon-search(); font: 25px/25px @fonti; display: block; color: @lightGreen; margin-bottom: 10px;}
}
/*------- /instashop post -------*/

/*------- search -------*/
.page-search{
	.header{padding-bottom: 0;}
	.wrapper-publish{margin-top: 0; padding-top: 30px;}
	.cf-recipes{top: auto; position: relative; box-shadow: none; border: 1px solid @borderColor;}
	@media (max-width: @tp){
		.cf-recipes{border: 0; top: 0;}
	}
	@media (max-width: @m){
		.p-index-items, .header{background: none;}
		.wrapper-publish{padding-top: 0;}
	}
}
.s-header-wrapper{
	text-align: center;
	@media (max-width: @m){text-align: left; padding: 15px 15px 0; color: @textColor;}
}
.s-nav{
	list-style: none; display: flex; justify-content: center; font-size: 15px; font-weight: bold;
	li{
		.gradient-green; margin-right: 1px; border-radius: @borderRadius @borderRadius 0 0; overflow: hidden; position: relative;
		&.selected{
			background: #fff;
			a{color: @textColor!important;}
			.s-counter{color: @green; opacity: 1;}
			&:after{display: none;}
		}
		&:after{.pseudo(auto,auto); top: 0; right: 0; bottom: 0; left: 0; opacity: 0; .gradient-green-hover; .transition(opacity);}
		&:hover{
			&:after{opacity: 1;}
		}
	}
	a{height: 54px; color: #fff; display: block; display: flex; position: relative; z-index: 1; align-items: center; justify-content: center; min-width: 180px; text-decoration: none;}
	@media (max-width: @tp){
		font-size: 13px;
		a{min-width: 160px; height: 50px;}
	}
	@media (max-width: @m){
		border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; margin-left: -15px; margin-right: -15px; font-size: 11px; justify-content: space-between; font-weight: normal; padding: 10px 15px;
		li{
			background: none;
			&:after{display: none;}
			&.selected{
				font-weight: bold;
				a{color: @green!important;}
			}
		}
		a{min-width: 0; height: auto; color: @textColor;}
	}
}
.s-h1{
	color: #fff; font-size: 28px; padding-bottom: 5px;
	@media (max-width: @tp){font-size: 22px;}
	@media (max-width: @m){color: @textColor; font-size: 16px;}
}
.s-headline{
	font-size: 14px; color: @gray2; position: relative; padding-left: 22px;
	&:before{.icon-search(); font: 15px/15px @fonti; position: absolute; left: 0; top: 2px; color: @green;}
	@media (max-width: @tp){font-size: 12px;}
}
.s-counter{font-weight: normal; font-size: 12px; line-height: 1.3; padding-left: 3px; opacity: .6;}
.s-keyword{display: block; padding: 5px 0 10px 0;}
.s-wrapper{
	max-width: 720px; margin: auto; padding: 40px 0 70px;
	@media (max-width: @tp){max-width: 540px; padding-bottom: 30px;}
	@media (max-width: @m){padding: 20px 15px;}
}
.s-item{
	margin-bottom: 30px; font-size: 14px; line-height: 1.6; display: block;
	@media (max-width: @m){font-size: 12px;}
}
.s-item-title{
	font-size: 20px; padding: 0 0 5px; margin: 0;
	a:hover{color: @green; text-decoration: underline;}
	@media (max-width: @tp){font-size: 18px;}
	@media (max-width: @m){font-size: 16px; text-decoration: underline;}
}
/*------- /search -------*/

/*------- cart -------*/
.page-cart{
	.header{padding-bottom: 0;}
	.page-wrapper{overflow: visible;}
}
.w-title{color: #fff;}
.w-title-counter{
	&:before{.icon-cart(); font-size: 38px; line-height: 38px;}
	span{background: @orange; color: #fff;}
	@media (max-width: @t){
		&:before{font-size: 30px; line-height: 30px;}
	}
	@media (max-width: @tp){
		&:before{color: @green!important;}
	}
}
@media (max-width: @m){
	.w-col .free-delivery-container{margin-top: 20px;}
}
.w-col1{
	flex-grow: 1; width: 940px;
	@media (max-width: @l){width: auto;}
	@media (max-width: @m){
		.w-loyalty{margin-bottom: 0;}
	}
}
.w-col2{
	width: 420px; flex-grow: 0; flex-shrink: 0; padding-bottom: 60px; position: sticky; top: 190px; align-self: flex-start;
	@media (max-width: @l){width: 360px;}
	@media (max-width: @t){width: 315px;}
	@media (max-width: @tp){width: 100%; display: flex;}
	@media (max-width: @m){flex-wrap: wrap; padding: 0 15px 30px;}
}
.w-col-cnt{
	margin-top: -85px; background: #fff; padding: 30px 50px 35px; position: relative; border-radius: @borderRadius; box-shadow: 0 10px 30px 0 rgba(0,0,0,0.1);
	@media (max-width: @l){padding-left: 35px; padding-right: 35px;}
	@media (max-width: @t){padding-left: 20px; padding-right: 20px; padding: 25px 20px; margin-top: -70px;}
	@media (max-width: @tp){box-shadow: none; padding: 0; margin: 0;}
}
.w-col1-cnt{
	padding: 20px 120px 100px 0;
	@media (max-width: @l){padding-right: 60px;}
	@media (max-width: @t){padding-right: 30px; padding-bottom: 75px;}
	@media (max-width: @tp){padding: 0 0 55px 0;}
	@media (max-width: @m){padding: 0 0 20px;}
}
.w-col-pickup{
	width: 100%; background: @red; border-radius: 2px; padding: 10px 16px 10px 44px; min-height: 40px; font-size: 14px; line-height: 16px; font-weight: bold; color: white; display: none; align-items: center; margin-bottom: 14px; position: relative;
	&:before{.pseudo(auto,auto); left: 16px; top: 10px; font: 18px/18px @fonti; color: white; .icon-danger();}
	&.active{display: flex;}
	@media (max-width: @m){
		font-size: 11px; line-height: 14px; margin-bottom: 0; border: unset; padding: 7px 15px; border-radius: 0;
		&:before{display: none;}
	}
}
.wp-pickup-products{
	padding: 16px 50px 24px 25px; border-radius: 2px; border: 1px solid @red; color: #375126; font-weight: bold; font-size: 14px; line-height: 18px; position: relative; margin-top: 35px;
	@media(max-width: @m){padding: 12px; font-size: 12px; line-height: 18px; margin-left: 15px; margin-right: 15px;}
}
.wp-pickup-products-info{
	padding-bottom: 18px;
	span{color: @red;}
}
.wp-pickup-select-label{display: block; padding-bottom: 8px;}
#field-shipping_pickup_location{
	font-weight: normal; width: 340px;
	@media(max-width: @tp){width: 100%; height: 40px; font-size: 12px; line-height: 16px; padding: 0 15px; background-position: right 11px center;}
}

@media (max-width: @tp){
	.w-col2-cnt-top{flex-grow: 1; margin-right: 30px;}
	.w-col2-cnt{width: 330px; flex-grow: 0; flex-shrink: 0;}
}
@media (max-width:  @m){
	.w-col2-cnt-top{margin-right: 0;}
	.w-col2-cnt{width: 100%;}
}
.w-btn-finish{
	width: 100%; margin-top: 15px; font-size: 17px; font-weight: bold;
	@media (max-width: @t){font-size: 14px;}
}
.wp-unable-to-completeorder{
	background: @red; border-radius: 2px; padding: 12px 18px 12px 36px; color: white; font-weight: bold; font-size: 12px; line-height: 16px; position: relative; margin-top: 16px;
	&:before{.pseudo(auto,auto); left: 10px; top: 10px; .icon-danger-white2(); font: 16px/16px @fonti; color: white;}
	@media(max-width: @m){
		padding: 8px 12px 8px 32px; font-size: 12px; line-height: 16px;
		&:before{font: 14px/14px @fonti; left: 8px; top: 8px;}
	}
}
.cart-totals{
	font-size: 15px; line-height: 1.4;
	@media (max-width: @t){font-size: 13px;}
	@media (max-width: @tp){grid-column: 2;}
}
.cart-totals-title{
	font-size: 20px; line-height: 1.4; font-weight: bold; padding: 0 0 7px;
	@media (max-width: @t){font-size: 16px;}
}
.ct-row{display: flex; flex-wrap: wrap; margin: 5px 0;}
.w-totals-label{flex-grow: 1; max-width: 100%;}
.w-totals-value{flex-grow: 1; text-align: right;}
.w-totals-multitax{width: 100%; text-align: right;}
.cart-total{font-weight: bold;}

.free-delivery-container{
	position: relative; display: block; margin-top: 25px; text-align: center; font-size: 14px;
	@media (max-width: @t){font-size: 12px;}
	@media (max-width: @tp){margin-top: 33px;}
	@media (max-width: @m){margin-top: 15px;}
}
@media (max-width: @m){
	.step3 .free-delivery-container{margin-top: 0;}
}
.free-delivery-scale{position: relative; display: flex; width: 100%; border: 1px solid @borderColor; height: 36px; padding: 1px;}
.free-delivery-title{
	padding-bottom: 8px; padding: 0 10px 8px 10px; max-width: 100%; margin-inline: auto; line-height: 1.4;
	@media (max-width: @m){max-width: 100%; padding: 0 15px 8px 15px;}
}
.free-delivery-scale-amount{
	position: absolute; top: 5px; right: 0; left: 0; text-align: center; font-weight: bold; font-size: 14px; z-index: 10;
	@media (max-width: @t){font-size: 12px; top: 9px;}
}
.free-delivery-scale-bar{background: @lightGreen; height: 100%; .transition(width);}
.w-loyalty{
	border: 1px solid @borderColor; background: #F9FAF9; padding: 20px 40px; max-width: calc(~"100% - 185px"); margin-bottom: 40px; border-radius: @borderRadius; position: relative;
	&:after{
		.pseudo(266px,199px); top: -45px; right: -210px; background: url(images/card-hr.png) no-repeat left top; background-size: contain;
		&:lang(de){background: url(images/card-de.png) no-repeat left top;}
		&:lang(en){background: url(images/card-en.png) no-repeat left top;}
		&:lang(sl){background: url(images/card-si.png) no-repeat left top;}
	}
	.loyalty-cnt{max-width: none; padding-bottom: 10px;}
	@media (max-width: @t){
		max-width: calc(~"100% - 45px"); padding: 15px 20px;
		&:after{right: -70px; top: -45px; width: 260px; height: 195px;}
		.loyalty-cnt{max-width: 290px;}
	}
	@media (max-width: @tp){
		max-width: 95%;
		&:after{width: 240px; height: 180px; top: -35px; right: -60px;}
	}
	@media (max-width: @m){
		max-width: none; border-right: 0; border-left: 0; font-size: 12px; padding-left: 90px; padding-right: 15px; margin-bottom: 20px;
		&:after{width: 65px; height: 50px; right: auto; left: 11px; top: 10px;}
		.loyalty-cnt{padding-right: 0;}
	}
}
.w-wishlist{
	padding-top: 70px;
	@media (max-width: @t){padding-top: 50px;}
	@media (max-width: @m){padding-top: 30px; padding-bottom: 25px;}
}
.w-wishlist-btns{display: flex; align-items: center;}
.w-wishlist-header{
	margin-bottom: 15px; border-bottom: 1px solid @borderColor; padding-bottom: 15px;
	@media (max-width: @m){flex-wrap: wrap; padding-left: 15px; padding-right: 15px; border: 0; padding-bottom: 10px;}
}
.w-wishlist-footer{
	margin-top: 15px;
	@media (max-width: @m){
		margin: 0; padding: 0 15px;
		.btn-move-to-cart{margin-top: 10px;}
	}
}
.w-wishlist-title{
	font-size: 32px;
	.wishlist-title-counter{
		span{background: @green; color: #fff;}
		&:before{.icon-heart(); font-size: 33px;}
	}
	@media (max-width: @t){
		font-size: 26px;
		.wishlist-title-counter:before{font-size: 28px; line-height: 31px; color: @textColor;}
	}
	@media (max-width: @tp){padding-left: 55px;}
	@media (max-width: @m){
		width: 100%; font-size: 20px; padding-left: 45px;
		.wishlist-title-counter:before{font-size: 23px; line-height: 24px;}
	}
}
.btn-move-to-cart{
	margin-left: auto; color: @orange; font-weight: bold; padding: 0 30px;
	span{
		position: relative; display: flex; align-items: center;
		&:before{.icon-cart(); font: 22px/22px @fonti; color: @orange; margin-right: 10px;}
	}
	@media (max-width: @m){
		width: 100%; height: 40px; margin: 20px 0 0; font-size: 12px;
		span:before{font-size: 19px; line-height: 19px; position: relative; top: -2px;}
	}
}
.empty-cart{
	padding: 30px 0;
	@media (max-width: @t){
		padding: 30px;
		h2{font-size: 25px;}
	}
	@media (max-width: @tp){
		h2{padding-top: 0; font-size: 20px;}
	}
	@media (max-width: @m){padding: 20px;}
}
@media (max-width: @t){
	.w-row{padding-left: var(--wrapperOffset); padding-right: var(--wrapperOffset);}
}
#items_shoppingcart{
	.wp{padding-top: 25px; margin-bottom: 0; margin-top: -1px;}
	/*.wp-pickup-only{
		.wp-image{opacity: .4;}
	}*/
	/*.wp-pickup-only{
		position: relative; border: unset;
		&:before{.pseudo(auto,auto); right: -20px; left: -20px; bottom: 0; top: 0; border: 1px solid @red; pointer-events: none;}
	}*/
	@media (max-width: @m){
		.wp{padding-top: 15px;}
		/*.wp-pickup-only{
			border: 2px solid @red;
			&:before{display: none;}
		}*/
	}
}
@media (max-width: @tp){
	.w-row{display: block; padding-left: 40px; padding-right: 40px;}
	.w-header{background: #fff; padding-left: 35px; padding-right: 35px;}
	.w-title{color: @textColor;}
}
@media (max-width: @m){
	.w-row{padding-left: 0; padding-right: 0;}
	.w-header{padding-left: 15px; padding-right: 15px;}
}

.free-delivery-widget{
	overflow: hidden; height: 0;
	&.active{
		overflow: visible; height: auto; padding-top: 30px;
		&+.w-wishlist{padding-top: 15px;}
		@media (max-width: @m){padding-top: 10px; padding-bottom: 10px; margin-bottom: 20px;}
	}
	@media (max-width: @t){padding-left: 30px; padding-right: 30px;}
	@media (max-width: @m){padding-left: 0; padding-right: 0;}
}
.fdw-title{
	font-size: 26px; line-height: 1.4; font-weight: bold; padding: 0 0 25px; display: flex; align-items: center; justify-content: space-between;
	@media (max-width: @t){font-size: 18px; padding: 0 0 15px;}
	@media (max-width: @m){
		padding-left: 15px; padding-right: 15px; font-size: 17px;
		.btn{padding: 0 20px; text-align: center; line-height: 1.2; flex-shrink: 0;}
	}
}
.fwd-slider{
	.slick-list{padding-bottom: 65px;}
	.cp-qty{display: none;}
	.slick-arrow:before{font-size: 25px;}
	.cp-brand img{max-width: 65px;}
	@media (max-width: @l){
		width: calc(~"100vw - 605px");
		.cp-unavailable-label{font-size: 11px;}
	}
	@media (max-width: @t){width: calc(~"100vw - 480px");}
	@media (max-width: @tp){width: 100%;}
	@media (max-width: @m){
		height: auto; overflow: auto; display: flex; padding-bottom: 10px;
		.cp{
			width: 100%; flex-grow: 0; flex-shrink: 0;
		}
		.cp-btn-addtocart-single{
			width: 100%;
			span:before{margin-right: 8px;}
		}
		.cp-qty:not(.cp-qty-single){display: block;}
	}
}

.priority-order{
	font-size: 14px; padding-bottom: 25px;
	span{
		font-size: 15px; font-weight: bold; color: @red; padding-bottom: 2px; display: block;
		@media (max-width: @l){font-size: 14px;}
		@media (max-width: @t){font-size: 13px; padding-bottom: 5px; padding-top: 2px;}
	}
	input[type=checkbox]+label{
		line-height: 1.3; padding-top: 0; border-radius: 2px;
		&:before{border-color: @red;}
		&>span{display: block;}
		@media (max-width: @t){font-size: 13px;}
	}
	input[type=checkbox]:checked + label{
		&:before{background: @red;}
	}
	
}
/*------- /cart -------*/

/*------- orders -------*/
.wp{
	display: flex; border-bottom: 1px solid @borderColor; font-size: 14px; line-height: 1.4; padding-bottom: 25px; margin-bottom: 15px; width: 100%;
	@media (max-width: @m){font-size: 11px; position: relative; padding-right: 15px; padding: 15px 15px 35px 15px; margin-bottom: 0;}
}
@media (max-width: @m){
	.wp-auth-wishlist{
		padding-right: 0;
		.wp-total{right: 0;}
		.cp-code{padding-top: 2px;}
		&:last-child{border: 0;}
	}
}
.wp-image{
	width: 90px; flex-grow: 0; flex-shrink: 0; margin-right: 10px; text-align: center;
	img{width: auto; height: auto;}
	@media (max-width: @t){width: 70px;}
	@media (max-width: @m){width: 50px;}
}
.wp-title{
	padding-bottom: 5px;
	a{
		text-decoration: none;
		&:hover{color: @green;}
	}
}
.wp-attribute, .wp-code{
	font-size: 12px; color: @gray2; padding-bottom: 3px;
	@media (max-width: @m){font-size: 11px; line-height: 14px;}
}
.wp-content{display: flex; flex-grow: 1;}
.wp-cnt{
	flex-grow: 1; padding-right: 20px;
	@media (max-width: @m){padding-right: 15px;}
}
.wp-badge{
	background: @red; display: inline-flex; font-size: 12px; line-height: 14px; padding: 4px 8px; min-height: 24px; height: auto; border-radius: 2px;
	@media (max-width: @m){font-size: 11px; line-height: 14px; min-height: 22px; letter-spacing: -0.24px; margin-top: 5px;}
}
.wp-total{
	width: 200px; text-align: right; font-size: 14px; flex-grow: 0; flex-shrink: 0; line-height: 1.4;
	@media (max-width: @l){width: 160px;}
	@media (max-width: @t){width: 120px;}
	@media (max-width: @m){position: absolute; right: 15px; bottom: 15px; font-size: 12px;}
	ins{padding: 0 2px;}
}
.wp-current-price, .wp-discount-price{font-weight: bold;}
.wp-discount-price{color: @red;}
.wp-old-price{
	font-size: 12px;
	span{text-decoration: line-through;}
	@media (max-width: @m){font-size: 11px;}
}
.wp-lowest-price{font-size: 9px; line-height: 1.5; max-width: 120px; margin-left: auto; padding-top: 3px; display: block;}
.wp-qty-count{color: @gray2; font-size: 11px;}
.wp-qty-container{
	width: 85px; position: relative; flex-grow: 0; flex-shrink: 0;
	@media (max-width: @m){width: 75px;}
}
.wp-message{
	position: absolute; font-size: 11px; width: 180px; text-align: center; left: -45px; top: 53px; z-index: 50;
	@media (max-width: @m){width: auto; left: -10px; right: -10px; font-size: 10px; line-height: 1.2; top: 50px; background: #fff; padding: 2px;}
}
.wp-btns{
	font-size: 12px; padding-top: 18px; display: flex;
	@media (max-width: @tp){
		flex-wrap: wrap;
	}
	@media (max-width: @m){flex-wrap: wrap; width: 100%; margin: 0; flex-direction: column-reverse; min-height: 60px; font-size: 11px; position: relative; bottom: -16px;}
}
.wp-btn{
	position: relative; text-decoration: none; margin-right: 30px; padding-left: 25px; padding-right: 0; height: auto; background: unset; color: @textColor; font-size: 12px; line-height: 1.3; overflow: unset; cursor: pointer;
	@media (max-width: @t){margin-right: 20px;}
	&:before{.icon-trash(); font: 18px/18px @fonti; position: absolute; left: 0; top: -2px; color: @textColor;}
	&:hover{color: @green; text-decoration: none;}
	&.wp-wishlist{margin-right: 0;}
	@media (max-width: @m){
		margin-right: 0; display: flex;
		&.wp-wishlist{margin-bottom: 10px;}
	}
	&.loading{
		color: transparent!important; pointer-events: none; font-size: 0; line-height: 0;
		&>span{opacity: 0; display: none;}
		&:after{display: none;}
	}
}
.wp-btn-add{
	padding-left: 30px; 
	.b{display: none;}
	&:after{display: none !important;}
	&:before{.icon-cart(); font-size: 20px; color: @orange;}
	&.active{
		pointer-events: none; opacity: .4;
		.a{display: none;}
		.b{display: inline;}
	}
	&:hover{color: @orange;}
	&.loading{
		color: transparent!important; pointer-events: none;
	}
	@media (max-width: @m){
		margin-bottom: 10px; padding-left: 25px; justify-content: flex-start;
		&:before{font-size: 18px; line-height: 18px; left: -2px;}
	}
}
.wp-wishlist:before{.icon-heart(); color: @green; font-size: 16px; line-height: 16px; top: 0;}

.wp-auth-wishlist{
	.cp-title{font-size: 12px;}
	@media (max-width: @tp){
		.wp-btn{
			width: 100%; margin-bottom: 15px; padding-left: 30px;
			&:last-child{margin-bottom: 0;}
		}
	}
}

.wp-pickup-notavailable{
	min-height: 40px; padding: 10px 20px 10px 45px; position: relative; border-radius: 2px; background: @red; font-size: 14px; line-height: 16px; color: white; font-weight: bold; margin-bottom: 8px; margin-top: 35px; display: flex; align-items: center;
	&:before{.pseudo(auto,auto); .icon-danger-white2(); color: white; left: 16px; top: 11px; font: 18px/18px @fonti;}
	@media(max-width: @m){
		padding: 8px 10px 8px 30px; font-size: 12px; line-height: 18px; margin-top: 15px; margin-left: 15px; margin-right: 15px;
		&:before{left: 8px; top: 11px; font: 14px/14px @fonti;}
	}
}
.wp-notavailable-for-pickup-products{
	.wp-pickup-only{
		.wp-image{opacity: .4;}
	}
	.wp-btns{display: none;}
}
.wp-pickup-btn-delete{
	display: inline-flex; border: 1px solid @red; border-radius: 2px; min-height: 40px; align-items: center; justify-content: center; font-size: 14px; line-height: 16px; font-weight: bold; color: @red; padding: 0 16px 0 39px; margin-right: 0;
	&:before{left: 17px; top: 10px; font: 16px/16px @fonti; color: @red;}
	@media(max-width: @m){
		width: calc(~"100% - 30px"); margin-left: 15px; margin-right: 15px; padding: 0 15px;
		span{
			position: relative;padding-left: 22px;
			&:before{.pseudo(auto,auto); left: 0; .icon-trash(); font: 16px/16px @fonti; color: @red; top: 0px;}
		}
		&:before{display: none;}
	}
}
/*------- /orders -------*/

/*------- cart widget -------*/
.ww{
	grid-row: 2; grid-column: ~"6/7"; width: 55px; height: 100%; margin-left: -12px;
	&.active{
		.ww-counter{display: flex;}
		.ww-preview-bottom{display: block;}
		.empty-shopping-cart-preview{display: none;}
	}
	@media (max-width: @tp){width: 100%; margin-left: 0;}
	@media (max-width: @m){width: 40px; height: 100%; position: absolute; top: 0; right: 60px;}
}
.ww-items{
	display: flex; height: 100%; align-items: center; justify-content: center; align-items: center; font-size: 0; text-decoration: none; position: relative;
	&:before{.icon-cart(); color: #fff; font: 25px/25px @fonti; .transition(all);}
	&:hover{
		text-decoration: none; color: #fff;
		&:before{color: @lightGreen;}
	}
	@media (max-width: @t){
		&:before{font-size: 21px; line-height: 21px;}
	}
}
.ww-counter{background: @orange;}

.ww-preview{
	position: fixed; display: flex; flex-direction: column; right: -400px; top: 0; bottom: 0; background: #fff; width: 380px; z-index: 600; box-shadow: 0 15px 40px 0 rgba(0,0,0,0.2); color: @textColor; text-align: left; z-index: 500; overflow: hidden; .transition(transform);
	&.active{.translate(-400px); overflow: initial;}
	@media (max-width: @m){
		width: 100vw; right: -100vw; box-shadow: none; .transition(none);
		&.active{.translate3d(-100vw);}
	}
	.wp-message{left: auto; right: 0; text-align: right; top: 25px; width: auto; white-space: nowrap;}
	@media (min-width: @t){
		height: 100vh;
	}	
}
@media (min-width: @tp){
	.admin_toolbar{
		.ww-preview{z-index: 9999;}
		&.fixed-header{
			.ww-preview{height: calc(~"100vh - 40px");}
		}
	}
}
.ww-preview-header{
	padding: 18px 25px 17px; border-bottom: 1px solid @borderColor; position: relative;
	@media (max-width: @m){padding: 10px 25px;}
}
.ww-preview-title{
	font-size: 20px; font-weight: bold; position: relative;
	&:before{.icon-cart(); font: 22px/22px @fonti; margin-right: 18px; position: relative; top: 1px;}
	&.active .ww-preview-count{visibility: visible;}
	@media (max-width: @m){
		font-size: 16px;
		&:before{top: 2px; margin-right: 15px; font-size: 20px; line-height: 20px;}
	}
}

.ww-preview-items-container{
	padding: 15px 15px; border-bottom: 1px solid @borderColor; flex-grow: 1; overflow: auto;
	&::-webkit-scrollbar { -webkit-appearance: none; width: 4px; background: @borderColor; }
	&::-webkit-scrollbar-thumb {
		background-color: @green; border-radius: 50px;
		box-shadow: 0 0 1px rgba(255,255,255,.5);
	}
	@media (max-width: @tp){border-bottom: 0;}
}
.ww-preview-count{font-size: 12px; font-weight: normal; padding-left: 2px; visibility: hidden;}
.ww-preview-close{
	display: block; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; background: @darkGreen; position: absolute; border-radius: 100px; top: 17px; right: 25px; text-decoration: none; cursor: pointer;
	&:before{.icon-cross(); font: 14px/14px @fonti; color: #fff;}
	@media (max-width: @m){
		width: 25px; height: 25px; right: 15px; top: 10px;
		&:before{font-size: 9px; line-height: 9px;}
	}
}
.ww-preview-bottom{
	padding: 0 25px 20px; display: none;
	@media (max-width: @m){padding: 0 15px 15px;}
}
.ww-preview-buttons{
	display: flex; padding: 15px 0 0;
	.btn{/*width: calc(~"50% - 5px");?*/ width: 100%; padding: 0 5px; margin: 0;}
	@media (max-width: @m){padding: 10px 0 0;}
}
.ww-btn-view{margin-right: 10px;}

.ww-item{display: flex; font-size: 12px; margin-bottom: 15px;}
.ww-title{
	width: 100%; line-height: 1.5; margin-bottom: 5px;
	a{
		text-decoration: none;
		&:hover{text-decoration: underline; color: @green;}
	}
}
.ww-image{
	width: 50px; margin-right: 10px; flex-grow: 0; flex-shrink: 0;
	img{display: block; margin: auto;}
}
.ww-cnt{flex-grow: 1; display: flex; flex-wrap: wrap;}
.ww-bottom{width: 100%;}
.ww-price{flex-grow: 1; font-weight: bold; line-height: 1.4;}
.ww-old-price{
	font-weight: normal; padding-right: 4px;
	span{text-decoration: line-through;}
}
.ww-lowest-price{font-size: 9px; max-width: 150px; line-height: 1.5; font-weight: normal;}
.ww-qty{
	height: 25px; width: 65px;
	.wp-input-qty, .qty-input{padding: 0 17px;}
	.wp-btn-qty{width: 18px; .scale(1);}
	.toggle-icon{
		width: 9px; height: 9px;
		&:before{width: 1px; left: 4px;}
		&:after{height: 1px; top: 4px;}
	}
}
.ww-btn-delete{
	margin: 0 15px 0 0; padding: 0; width: 25px; height: 25px; display: flex; align-items: center; justify-content: center;
	&:before{position: relative; top: auto; font-size: 15px; line-height: 15px;}
	&:hover:before{color: @orange;}
}
.ww-discount-price{color: @red;}
.ww-qty-container{
	width: auto; display: flex; align-items: center;
	input{width: 100%; height: 25px;}
	.qty-input-container{
		height: 25px; width: 65px; position: relative;
		.wp-input-qty, .qty-input{padding: 0 17px; text-align: center;}
		.wp-btn-qty, .qty-btn{
			width: 18px; .scale(1);
			&:before{width: 9px; height: 1px;}
			&:after{width: 1px; height: 9px; left: 4px;}
		}
	}
	.qty-status{display: none!important;}
}
.ww-btn-finish{
	color: #fff;
	span{
		position: relative; display: flex; align-items: center;
		&:after{.icon-arrow-left(); margin-left: 8px; font: 13px/13px @fonti; .scaleX(-1); position: relative; top: 1px;}
	}
}
.ww-totals{
	position: relative; padding-top: 12px;
	.w-totals-label{flex-grow: 0; margin-left: auto;}
	.w-totals-value{display: inline-block; width: 120px; text-align: right;}
	&:before{.pseudo(auto,1px); left: -25px; right: -25px; top: 0; background: @borderColor;}
	@media (max-width: @m){
		&:before{left: -15px; right: -15px;}
	}
}
.ww-loyalty{
	margin-bottom: 10px; margin-top: 3px; position: relative; padding-top: 12px;
	&:before{.pseudo(auto,1px); background: @borderColor; left: -25px; right: -25px; top: 0;}
	@media (max-width: @m){
		&:before{left: -15px; right: -15px;}
		input[type=checkbox]+label{padding-top: 1px; font-size: 13px;}
	}
}
.ww-minprice{background: @red; padding: 10px 20px; color: #fff; font-size: 13px; margin: 15px 0 10px; border-radius: @borderRadius;}
.empty-shopping-cart-preview{
	padding-top: 10px; text-align: center; max-width: 265px; margin: auto;
	h2{padding: 0 0 10px; font-size: 18px;}
}
@media (max-width: @m){
	.active-cart, .active-cart-modal, body.active{
		.zEWidget-launcher, #launcher{visibility: hidden; pointer-events: none;}
		.page-wrapper{position: absolute; top: 0; right: 0; bottom: 0; left: 0;}
		.page-wrapper>*:not(.header), .header>*:not(.header-body){overflow: hidden; height: 0;}
	}
}
.quick-priority-order{
	padding: 12px 25px 6px; font-size: 15px; margin: 0 -25px; border-top: 1px solid @borderColor;
	input[type=checkbox]+label{font-size: 0;}
	span{
		font-size: 14px; padding-top: 1px;
		@media (max-width: @t){font-size: 13px; padding-bottom: 2px;}
	}
}
/*------- /cart widget -------*/

/*------- quick order -------*/
.page-quickorder{
	.page-wrapper{overflow: visible;}
	.header{padding-bottom: 0;}
	@media (max-width: @tp){
		.footer{margin-bottom: 70px;}
		&.ftr .zEWidget-launcher{bottom: 0!important;}
	}
	@media (max-width: @m){
		.footer{margin-bottom: 90px;}
		.zEWidget-launcher{display: none!important;}
	}
}
@media (max-width: @tp){
	.quickorder-header{background: #fff; padding-top: 25px; padding-left: 10px;}
}
@media (max-width: @m){
	.quickorder-header{padding-top: 15px;}
}
.title-quickorder{
	color: #fff; position: relative; padding: 0 0 30px 65px; font-size: 42px; margin-top: 20px;
	&:before{.icon-quickorder(); font: 40px/40px @fonti; position: absolute; left: 0; top: 6px;}
	@media (max-width: @t){
		font-size: 34px; padding-left: 80px;
		&:before{font-size: 33px; line-height: 33px; left: 30px; top: 4px;}
	}
	@media (max-width: @tp){
		color: @textColor; margin: 0;
		&:before{color: @green;}
	}
	@media (max-width: @m){
		font-size: 20px; padding-left: 40px; padding-bottom: 20px;
		&:before{left: 4px; font-size: 23px; line-height: 23px; top: 1px;}
	}
}

/*
@-webkit-keyframes fadeInUp {
  from {opacity: 0;-webkit-transform: translate3d(0, 130px, 0);transform: translate3d(0, 130px, 0);}
  to {opacity: 1;-webkit-transform: translate3d(0, 0, 0);transform: translate3d(0, 0, 0);}
}
@keyframes fadeInUp {
  from {opacity: 0;-webkit-transform: translate3d(0, 130px, 0);transform: translate3d(0, 130px, 0);}
  to {opacity: 1;-webkit-transform: translate3d(0, 0, 0);transform: translate3d(0, 0, 0);}
}
.product_info_clone{-webkit-animation-name: fadeInUp; animation-name: fadeInUp;}
*/

.qo-col1{
	flex-grow: 1; padding: 30px 120px 120px 0;
	@media (max-width: @l){padding-right: 60px; padding-bottom: 50px;}
	@media (max-width: @t){padding-bottom: 80px;}
	@media (max-width: @tp){padding: 15px 0 25px;}
	@media (max-width: @m){
		position: relative; margin-top: 10px; padding-top: 10px; padding-bottom: 20px;
		/*&:before, &:after{.pseudo(auto,1px); background: @borderColor; left: -15px; right: -15px; top: 0;}*/
		&:after{top: auto; bottom: 0; display: none;}
	}
}
.qo-col2{
	width: 400px; flex-grow: 0; flex-shrink: 0; padding-bottom: 120px; position: sticky; top: 210px; align-self: flex-start;
	@media (max-width: @l){width: 360px;}
	@media (max-width: @t){width: 300px; padding-bottom: 80px;}
	@media (max-width: @tp){width: 100%; padding: 0; text-align: center; position: relative; top: auto; z-index: 10;}
	@media (max-width: @m){padding-right: 0; padding-bottom: 10px; position: relative;}
}
.btn-qo-toggle-desc{
	display: none;
	@media (max-width: @m){display: inline-block; margin-bottom: 5px; color: @green;}
}
@media (max-width: @m){
	.qo-col-desc{font-size: 12px; overflow: hidden; max-height: 0; .transition(max-height);}
	.qo-container{margin: 0 -15px;}
}
.qo-col-body{
	.box-shadow-gray; border-radius: @borderRadius; background: #fff; position: relative; z-index: 1; font-size: 14px; margin-top: -85px;
	@media (max-width: @t){font-size: 12px; margin-top: -70px;}
	@media (max-width: @tp){margin-top: 0; box-shadow: none; font-size: 13px;}
	@media (max-width: @m){position: static;}
}
.qo-col-title{
	font-size: 20px; font-weight: bold; padding: 0 0 5px;
	@media (max-width: @t){font-size: 16px;}
	@media (max-width: @m){font-size: 14px; color: @green; text-decoration: underline;}
}
.qo-col2-footer{
	@media (max-width: @tp){margin-bottom: 30px;}
	@media (max-width: @m){text-align: left; margin-bottom: 0;}
}
.qo-free-shipping{
	position: relative; display: inline-block; vertical-align: top; padding-left: 55px; margin-top: 35px; font-size: 13px;
	&:before{.icon-shipping(); font: 25px/25px @fonti; color: @green; position: absolute; left: 0; top: 0;}
	strong{color: @green;}
	p:last-child{display: none;}
	@media (max-width: @l){
		font-size: 12px; padding-left: 50px;
		&:before{font-size: 22px; line-height: 22px; top: -2px;}
	}
	@media (max-width: @t){
		padding-left: 40px;
		&:before{font-size: 19px; line-height: 19px;}
	}
	@media (max-width: @tp){margin-top: 5px;}
}
.qo-col-cnt-desc{
	padding-top: 25px; padding-bottom: 15px;
	@media (max-width: @t){padding-top: 20px; padding-bottom: 10px;}
	@media (max-width: @m){
		position: relative;
		&.active{
			.qo-col-title{text-decoration: none;}
			.qo-col-desc{max-height: 1000px;}
		}
		&:after{.pseudo(auto,1px); background: @borderColor; bottom: 0; left: -15px; right: -15px;}
	}
}
.qo-col-cnt{
	padding-left: 50px; padding-right: 50px;
	@media (max-width: @l){padding-left: 40px; padding-right: 40px;}
	@media (max-width: @t){padding-left: 25px; padding-right: 25px;}
	@media (max-width: @tp){padding: 0; text-align: left;} 
	@media (max-width: @m){padding-bottom: 10px;}
}
.qo-col-cnt-totals{
	border-top: 1px solid @borderColor; padding-top: 20px; padding-bottom: 25px; display: none;
	&.active{
		display: block;
		@media (max-width: @tp){display: flex;}
		@media (max-width: @m){flex-wrap: wrap;}
	}
	@media (max-width: @tp){
		border: 0; padding-bottom: 15px; position: fixed; bottom: 0; left: 0; right: 0; background: #fff; z-index: 100; border-top: 1px solid @borderColor; padding: 12px 20px;
	}
	@media (max-width: @m){border: 0; padding-bottom: 15px; position: fixed; bottom: 0; left: 0; right: 0; background: #fff; z-index: 100; border-top: 1px solid @borderColor; padding: 0;}
}
.qo-col-totals{
	@media (max-width: @tp){flex-grow: 1; display: flex; align-items: center; justify-content: flex-end; padding-right: 20px;}
	@media (max-width: @m){padding: 10px; font-size: 12px; width: 100%; justify-content: center;}
}
.qo-buttons{
	margin-top: 15px;
	@media (max-width: @tp){margin-top: 0;}
	@media (max-width: @m){margin: 0; width: 100%;}
}
.btn-add-to-cart-quick{
	width: 100%;
	&.active{display: flex;}
	@media (max-width: @m){border-radius: 0;}
}
.qo-totals-row{display: flex; justify-content: space-between;}
.qo-totals-row-total{
	font-weight: bold;
	@media (max-width: @tp){
		.w-totals-label{padding-right: 5px;}
	}
}

.qo-form{
	label{display: block; width: 100%; font-size: 15px; font-weight: bold; padding: 0 0 5px;}
	input{
		width: 100%; font-size: 16px; .placeholder(@gray2,@gray2*1.3);
		&:hover, &:focus{border-color: @borderColor;}
	}
	@media (max-width: @m){
		label{font-size: 12px;}
		input{font-size: 14px;}
	}
}
.qo-field-tip{
	color: @gray2; font-size: 12px; padding: 5px 0 30px 25px;
	@media (max-width: @tp){padding-bottom: 5px;}
	@media (max-width: @m){padding-left: 17px; padding-bottom: 20px;}
}
.qo-row-header{
	background: #F4F5F3; border-radius: @borderRadius; display: none; padding: 10px 20px; font-size: 14px; margin-bottom: 15px;
	&.active{display: flex;}
	@media (max-width: @m){margin-left: -15px; margin-right: -15px; margin-bottom: 0; border-radius: 0; font-size: 12px; padding: 7px 15px; font-weight: bold;}
}
.qo-row-header-products{flex-grow: 1;}
.qo-row-header-total{
	width: 105px; flex-grow: 0; flex-shrink: 0;
	@media (max-width: @m){text-align: right;}
}
.qo-row-header-qty{
	width: 85px; flex-grow: 0; flex-shrink: 0; text-align: center; margin-right: 50px;
	@media (max-width: @m){display: none;}
}
.qo-field{
	position: relative;
	.autocomplete-container{top: calc(~"100% - 2px"); width: 100%; border: 0; position: absolute; left: 0;}
	.ui-autocomplete{height: auto; max-height: 300px; overflow: auto; border: 1px solid @borderColor;}
	.ui-menu-item a{
		display: flex; font-size: 12px; align-items: center;
		&.ui-state-focus{background: #F4F5F3; color: @textColor; text-decoration: none;}
	}
	.search-content{padding-right: 10px;}
	.search-image{
		width: 60px; height: 60px; flex-grow: 0; flex-shrink: 0; margin-right: 10px; display: flex; align-items: center; justify-content: center;
		img{max-height: 60px;}
	}
	.autocomplete-showall{display: none!important;}
	.qo-autocomplete-item{display: flex; width: 100%; align-items: center;}
	.search-title{font-size: 12px;}
	.search-prices{display: flex; padding-top: 4px;}
	.search-basic_price{
		text-decoration: line-through;
		&.active{padding-right: 10px;}
	}
	.search-price{padding-top: 0;}
	.search-col{line-height: 1.5; padding-bottom: 3px;}
	.search-unit_description, .search-code{display: none!important;}
}
.wp-qty-label{display: none;}
.qo-row{
	.wp-title{font-size: 12px; padding-top: 8px;}
	.wp-btn{
		font-size: 0; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; padding: 0; margin: 0; top: -5px;
		&:before{position: relative; top: auto; left: auto; .transition(color);}
		&:hover{
			text-decoration: none;
			&:before{color: @orange;}
		}
	}
	.wp-total{padding: 5px 0 0 0;}
	.ww-old-price{padding: 0; font-size: 12px;}
	.wp-btns{width: 75px; flex-grow: 0; flex-shrink: 0; padding: 0; justify-content: flex-end;}
	@media (max-width: @m){
		padding: 0; margin: 0 -15px 0; padding: 10px 0; width: auto;
		.wp-title{padding-top: 0;}
		.wp-qty-container{position: absolute; left: 60px; bottom: 15px; display: flex; align-items: center; width: 130px;}
		.wp-content{padding-bottom: 55px; padding-right: 100px;}
		.wp-total{bottom: auto; top: 10px; padding: 2px 0 0 0;}
		.wp-btns{position: absolute; right: 0; bottom: 21px; min-height: 0; width: auto; right: 15px;}
		.wp-btn{
			width: auto; height: auto; font-size: 12px; top: auto; padding-right: 23px;
			&:before{margin: 0; position: absolute; right: 0; top: -2px;}
		}
		.wp-qty-label{display: block; font-weight: bold; padding-right: 10px; font-size: 12px;}
		.ww-old-price{font-size: 11px;}
		&:last-child{margin-bottom: 0;}
		&:after{.pseudo(1px,auto); top: 0; bottom: 0; right: 100px; background: @borderColor;}
	}
}
@media (max-width: @t){
	.qo-wrapper{padding-left: var(--wrapperOffset); padding-right: var(--wrapperOffset);}
}
@media (max-width: @tp){
	.qo-wrapper{flex-wrap: wrap; padding-left: 40px; padding-right: 40px;}
}
@media (max-width: @m){
	.qo-wrapper{padding-left: 15px; padding-right: 15px;}
}
/*------- /quick order -------*/

/*------- sweepstake -------*/
.sweepstake{padding: 0 0 15px; font-size: 14px;}
.sweepstake-title{padding: 0 0 10px; margin: 0; font-size: 22px;}


.sweepstake-question-title{
	font-size: 18px; padding: 0;
	@media (max-width: @m){font-size: 14px; line-height: 1.5;}
}
.sweepstake-field{padding-bottom: 25px;}
.sweepstake-note{
	font-size: 14px; padding-top: 5px;
	@media (max-width: @m){font-size: 11px;}
}
.sweepstake-question-fields{
	padding-top: 10px;
	&>span{display: block; margin-bottom: 5px;}
	input[type=checkbox]+label, input[type=radio]+label{
		font-size: 14px;
		@media (max-width: @m){font-size: 12px;}
	}
}
.sweepstake-cnt{
	padding-bottom: 20px;
	@media (max-width: @m){font-size: 12px;}
}
.sweepstake-date{
	padding-bottom: 10px; font-size: 14px;
	@media (max-width: @m){font-size: 12px;}
}
.sweepstake-userfields{
	padding-top: 40px;
	h2{padding-bottom: 10px;}
	@media (max-width: @m){padding-top: 25px;}
}

.sweepstake-rate-title{
	font-size: 20px; padding-bottom: 15px; font-weight: bold;
	@media (max-width: @m){font-size: 16px;}
}
.sweepstake-rate-item-title{padding-bottom: 8px; line-height: 1.5;}
.sweepstake-rate-item{padding-bottom: 25px; display: flex; flex-direction: column;}
.sweepstake-rate-col{flex-grow: 1;}
.sweepstake-rate-image{
	margin-right: 20px; width: 100px; flex-grow: 0; flex-shrink: 0; padding-top: 5px;
	@media (max-width: @m){padding-top: 0; padding-bottom: 5px;}
}
.sweepstake-rate-body{
	border: 1px solid @borderColor; border-radius: @borderRadius; background: #f5f5f5; padding: 12px 23px; margin-top: 15px;
	@media (max-width: @m){margin: 15px -15px 0; border-radius: 0; border-left: 0; border-right: 0; padding: 15px;}
}
.sweepstake-rate-item-comment{
	padding-top: 15px;
	textarea{height: 80px;}
}
.sweepstake-rate-item-score{
	display: flex; align-items: center;
	p{padding: 0; padding-right: 15px;}
	label{margin-right: 10px;}
	.error{padding-left: 0;}
}
.btn-sweepstake{margin-top: 20px;}
/*------- /sweepstake -------*/

/*------- landing -------*/
.page-landing{
	background: #fff;
	.header{padding-bottom: 0;}
	.nw-bottom{
		margin-top: 140px;
		@media (max-width: @t){margin-top: 80px;}
		@media (max-width: @tp){margin-top: 50px;}
		@media (max-width: @m){margin-top: 0;}
	}
	@media (min-width: @m){
		&.has-menu{
			.header{display: none;}
		}
	}
	/*
	&.fixed-header{
		.ln-nav-container{height: 60px; position: fixed; top: 0; left: 0; right: 0;}
		.logo{height: 60px; width: 60px;}
		.ln-nav-wrapper{padding: 0;}
		&.admin_toolbar .ln-nav-container{top: 40px;}
		.header-placeholder{height: 150px;}
		.ln-nav{
			font-size: 15px;
			li{margin-left: 25px;}
		}
	}
	*/
}
.ln-wrapper{width: var(--pageWidth); margin: auto;}
.ln-section{
	margin-bottom: 110px;
	.tab-content{padding: 0;}
	&.spacing{
		padding-bottom: 75px;
		@media (max-width: @tp){padding-bottom: 40px;}
	}
	@media (max-width: @t){margin-bottom: 60px;}
	@media (max-width: @tp){
		margin-bottom: 45px;
		.slick-carousel .slick-list{padding-bottom: 30px;}
	}
	@media (max-width: @m){margin-bottom: 40px;}
}

@media (max-width: @m){
	.pp-ln{
		margin-bottom: 25px;
		.pp-title{font-size: 14px;}
	}
}

.ln-nav-container{
	background: url(images/header.jpg); background-size: cover; z-index: 100;
	.logo{
		flex-grow: 0; flex-shrink: 0; width: 78px; height: 110px; margin: 0;
		@media (max-width: @l){height: 80px;}
		@media (max-width: @t){width: 55px;}
		@media (max-width: @tp){width: 41px; height: 60px;}
		@media (max-width: @m){display: none;}
	}
	&.active{
		.ln-nav{max-height: 1000px; padding: 0 0 15px 0;}
		.toggle-icon:before{display: none;}
		.btn-ln-toggle-nav{color: @lightGreen;}
	}
	@media (max-width: @m){background-image:url(images/header-t.jpg); background-size: 700px auto;}
}
.ln-nav-wrapper{
	display: flex; justify-content: space-between; padding: 20px 0; align-items: center;
	@media (max-width: @t){padding: 10px 30px;}
	@media (max-width: @tp){padding: 10px 20px;}
	@media (max-width: @m){padding: 0; display: block; border-top: 1px solid rgba(255,255,255,.1);  border-bottom: 1px solid rgba(255,255,255,.1);}
}
.ln-nav{
	list-style: none; display: flex; font-weight: bold; font-size: 18px; .transition(all);
	li{margin-left: 40px;}
	a{
		text-decoration: none; color: #fff;
		&:hover{color: @lightGreen;}
	}
	@media (max-width: @l){font-size: 16px;}
	@media (max-width: @t){
		font-size: 16px;
		li{margin-left: 25px;}
	}
	@media (max-width: @tp){
		font-size: 12px;
		li{margin-left: 20px;}
	}
	@media (max-width: @m){
		flex-direction: column; width: 100%; padding: 0; overflow: hidden; max-height: 0; .transition(all);
		a{display: block; padding: 5px 15px;}
		li{margin: 0; font-size: 14px;}
	}
}
.btn-ln-toggle-nav{
	display: none; color: #fff; font-size: 16px; font-weight: bold; padding: 13px 15px; text-decoration: none; position: relative;
	@media (max-width: @m){display: block;}
	.toggle-icon{
		position: absolute; top: 18px; right: 15px;
		&:before, &:after{background: #fff;}
	}
	&:hover{
		text-decoration: none; color: @lightGreen;
	}
}
.ln-nav-container2 .ln-nav-wrapper{
	justify-content: center; padding: 30px 0;
	@media (max-width: @t){padding: 30px;}
	@media (max-width: @tp){padding: 20px;}
	@media (max-width: @m){padding: 0;}
}
.ln-nav2{
	li{margin: 0 20px;}
	@media (max-width: @t){
		justify-content: space-between; width: 100%;
		li{margin: 0;}
	}
	@media (max-width: @tp){font-size: 14px;}
}

.ln-section-intro{
	background-size: cover; background-repeat: no-repeat; background-position: center top; color: #fff; margin-bottom: 0;
	.image{
		margin-bottom: 25px;
		@media (max-width: @m){margin-bottom: 15px;}
	}
	img{display: block;}
	.title{
		font-size: 54px; font-weight: bold; padding: 0 0 20px;
		@media (max-width: @l){font-size: 50px;}
		@media (max-width: @t){font-size: 34px;}
		@media (max-width: @tp){font-size: 32px; padding: 0 0 15px;}
		@media (max-width: @m){font-size: 22px; padding: 0 0 10px;}
	}
	.subtitle{
		font-size: 24px; line-height: 1.4; font-weight: normal; padding-bottom: 20px;
		@media (max-width: @l){font-size: 20px;}
		@media (max-width: @t){font-size: 18px; padding-bottom: 10px;}
		@media (max-width: @tp){font-size: 16px;}
	}
	.cnt{
		font-size: 18px;
		ul{margin-left: 0;}
		ul li:before{width: 8px; height: 8px; top: 13px;}
		@media (max-width: @t){
			font-size: 14px;
			ul li:before{width: 6px; height: 6px; top: 10px;}
		}
	}
	.btn, .btn-ln-intro{
		margin-top: 10px;
		@media (max-width: @m){margin-top: 0; width: 100%;}
	}
	&.spacing{
		padding-bottom: 0; margin-bottom: 75px;
		@media (max-width: @tp){margin-bottom: 40px;}
	}
	@media (max-width: @t){
		img{max-height: 30px; width: auto;}
	}
}
.ln-section-intro-light{color: @textColor;}
.section-intro-cnt{
	max-width: 50%; padding: 150px 0 200px;
	@media (max-width: @l){
		max-width: 70%; padding: 100px 0 120px;
	}
	@media (max-width: @t){padding: 40px 30px 80px;}
	@media (max-width: @tp){padding: 25px 20px 35px;}
	@media (max-width: @m){padding: 20px 15px; max-width: none;}
}

.ln-section-intro-center{
	.wrapper{
		padding-top: 85px;
		@media (max-width: @t){
			padding-top: 70px;
			@media (max-width: @tp){padding-top: 40px;}
		}
		@media (max-width: @m){padding-top: 20px;}
	}
	.section-intro-cnt{
		max-width: 700px; margin: auto; text-align: center;
		@media (max-width: @tp){max-width: 500px;}
	}
	img{margin: auto;}
	.logo{margin: auto;}
	@media (max-width: @m){
		.logo{
			position: relative; top: auto; left: auto; background: url(images/logo.svg?v5) no-repeat left top; background-size: contain; width: 60px; height: 80px;
			&:before{display: none;}
		}
	}
}

.ln-section-image{
	display: flex; align-items: center; font-size: 16px; background: #fff; margin: 0;
	.ln-section-col{
		width: 50%;
		@media (max-width: @m){width: 100%;}
	}
	.ln-section-col2{
		padding: 0 8vw;
		@media (max-width: @l){padding: 0 4vw;}
		@media (max-width: @t){padding: 0 30px;}
		@media (max-width: @tp){padding: 15px 20px;}
		@media (max-width: @m){padding: 15px 15px 20px;}
	}
	img{display: block;}
	.title{
		font-size: 32px; padding: 0 0 15px;
		@media (max-width: @l){font-size: 28px;}
		@media (max-width: @t){font-size: 22px; padding: 0 0 8px;}
		@media (max-width: @m){font-size: 20px;}
	}
	@media (max-width: @t){font-size: 14px; line-height: 1.4;}
	@media (max-width: @m){
		flex-direction: column;
		.btn{width: 100%;}
	}
}
.ln-section-image-right{
	flex-direction: row-reverse;
	@media (max-width: @m){flex-direction: column;}
}
.ln-section-title{
	text-align: center; margin-bottom: 20px;
	.section-content-title{
		font-size: 38px; padding: 0 0 15px;
		@media (max-width: @t){font-size: 26px; padding-bottom: 10px;}
		@media (max-width: @m){font-size: 20px; margin: 5px 0;}
	}
	.section-content-body{
		max-width: 680px; margin: auto;
		iframe{display: block; width: 1000px; margin: 20px 0 10px; .translate3d(-160px); height: calc(~"100% / 1.65");}
		@media (max-width: @t){
			iframe{width: 100%; height: 400px; transform: none;}
		}
		@media (max-width: @tp){
			max-width: 540px;
			iframe{height: 300px;}
		}
		@media (max-width: @m){
			iframe{height: calc(~"(100vw - 15px) / 2"); margin: 0;}
		}
	}
	&.spacing{
		padding-bottom: 75px;
		@media (max-width: @tp){padding-bottom: 40px;}
	}
	@media (max-width: @m){text-align: left; padding: 15px 15px 5px; margin-bottom: 0;}
}
@media (max-width: @m){
	.ln-products-slider{
		width: 100%;
		.cp:first-child{margin-left: 0;}
	}
}
.ln-section-promo{
	display: grid; grid-template-columns: repeat(3, 1fr); margin-bottom: 50px; column-gap: 20px; justify-content: center; text-align: center; line-height: 1.4;
	.title{
		font-size: 18px; font-weight: bold; padding: 0 20%; display: block;
		@media (max-width: @t){font-size: 16px; padding: 0 10px;}
		@media (max-width: @m){font-size: 14px;}
	}
	.image{
		margin-bottom: 15px;
		@media (max-width: @tp){
			img{width: 100%;}
		}
		@media (max-width: @m){margin-bottom: 10px;}
	}
	a{
		display: block; text-decoration: none;
		&:hover{
			color: @green;
			img{box-shadow: 0 10px 30px 0 rgba(36,69,56,0.3);}
		}
	}
	img{border-radius: @borderRadius; display: block; .transition(all);}
	&.spacing{
		margin-bottom: 100px;
		@media (max-width: @t){margin-bottom: 70px;}
		@media (max-width: @tp){margin-bottom: 50px;}
	}
	@media (max-width: @t){padding-left: 30px; padding-right: 30px; column-gap: 12px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px; margin-bottom: 40px;}
	@media (max-width: @m){grid-gap: 0; display: block; padding-left: 15px; padding-right: 15px;}
}
@media (max-width: @m){
	.ln-promo{margin-bottom: 20px;}
	.ln-section-promo2{
		padding-left: 0; padding-right: 0;
		img{border-radius: 0;}
	}
}
.ln-section-promo2{grid-template-columns: repeat(2, 1fr);}

.ln-section-instashop{
	.section-title{
		font-size: 42px; font-weight: bold; text-align: center; padding: 0 0 30px 0;
		span{color: #AABD51;}
		@media (max-width: @t){font-size: 28px;}
		@media (max-width: @m){font-size: 20px; text-align: left; padding-bottom: 15px;}
	}
	@media (max-width: @t){padding-left: 30px; padding-right: 30px;}
	@media (max-width: @tp){
		padding-left: 20px; padding-right: 20px;
		.section-title{padding: 0 0 20px;}
	}
	@media (max-width: @m){padding-left: 15px; padding-right: 15px;}
}

.ln-section-publish{
	.section-title{
		text-align: center; padding: 0 0 30px; font-size: 38px;
		@media (max-width: @t){font-size: 26px;}
		@media (max-width: @tp){padding-bottom: 20px;}
		@media (max-width: @m){font-size: 20px; text-align: left;}
	}
	@media (max-width: @t){padding-left: 30px; padding-right: 30px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px;}
	@media (max-width: @m){
		padding-left: 15px; padding-right: 15px;
		.p-items{display: block;}
	}
}

.ln-section-products-slider{
	background: url(images/bg.jpg); border-top: 1px solid @borderColor;
	.section-header{
		max-width: 700px; margin: auto; padding-bottom: 35px; text-align: center;
		@media (max-width: @t){padding-bottom: 10px;}
		@media (max-width: @tp){max-width: 540px;}
		@media (max-width: @m){text-align: left;}
	}
	h2{
		font-size: 38px; padding: 0 0 15px;
		@media (max-width: @t){font-size: 26px;}
		@media (max-width: @tp){font-size: 22px; padding-bottom: 10px;}
	}
	.wrapper{
		padding-top: 85px;
		@media (max-width: @t){padding: 60px 30px 20px;}
		@media (max-width: @tp){padding-top: 40px;}
		@media (max-width: @m){padding: 20px 0;}
	}
}
.ln-section-products{
	padding-left: 30px; padding-right: 30px;
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px;}
	@media (max-width: @m){
		padding-left: 0; padding-right: 0;
		.cp{width: 50%;}
	}
}
/*------- /landing 	-------*/

/*------- coupons -------*/
.ww-coupons{
	position: relative; margin-bottom: 25px;
	&.active{
		.ww-coupons-form, .ww-coupons-list{display: none;}
		.ww-coupons-active{display: block;}
	}
	@media (max-width: @t){margin-bottom: 25px;}
}
.ww-coupons-form{position: relative;}
.ww-coupons-label{
	font-size: 15px; font-weight: bold; padding: 0 0 10px 37px; display: flex; position: relative; align-items: center;
	&:before{.icon-coupon(); font: 25px/17px @fonti; color: @green; position: absolute; left: 0; top: 5px;}
	@media (max-width: @t){
		font-size: 13px;
		&:before{top: 2px;}
	}
	@media (max-width: @m){
		padding-left: 30px;
		&:before{font-size: 22px; line-height: 15px;}
	}
}
.ww-coupons-fields{
	position: relative; display: flex; height: 50px; border: 1px solid @borderColor; border-radius: @borderRadius;
	input{flex-grow: 1; height: 100%; border: 0; width: 100%;}
}
.ww-btn-add{
	padding: 0; width: 95px; display: flex; align-items: center; justify-content: center; background: #E9ECEB; height: 100%; text-decoration: none; font-weight: bold; font-size: 14px; .transition(all);
	&:hover{text-decoration: none; background: #E9ECEB/1.1;}
	@media (max-width: @t){width: 80px; flex-shrink: 0; flex-grow: 0; font-size: 12px;}
}
.coupon_message_response_ok{color: @green;}
.coupon_message_response_error{color: @red;}
.ww-coupons-active{display: none;}
.ww-coupon-delete{
	text-decoration: none; font-weight: bold; display: inline-flex; align-items: baseline; margin-left: 5px; position: relative; color: @red;
	&:before{.icon-cross(); font: 13px/13px @fonti; color: @red; margin: 0 5px 0 0;}
	&:hover{
		text-decoration: none; color: @textColor;
		&:before{color: @textColor;}
	}
}
.ww-coupons-list{margin: 15px 0 0 0;}
.ww-coupons-title span{font-weight: bold;}
.ww-coupons-table{
	width: 100%; border: 0;
	td{
		padding: 7px 0; border: 0; border-top: 1px solid @borderColor; font-size: 14px;
		@media (max-width: @m){font-size: 12px;}
	}
	.col-code{width: 175px; color: @red;}
	.col-type{width: 80px; text-align: center;}
	.col-link{
		text-align: right; font-weight: bold; text-decoration: underline;
		a{text-decoration: none;}
	}
	.col-value{width: 150px; text-align: right;}
	.col-valid{text-align: right; width: 200px;}
}
.ww-coupons-table-header{display: none;}
.ww-coupons-list-title{font-weight: bold; padding: 0 0 5px; font-size: 14px;}
.btn-coupon-remove{display: none!important;}
.cart_info_total_extra_coupon_code{font-weight: bold;}
.auth-have-coupon.active{display: none;}


.ww-auth-coupons{
	position: absolute; top: 52px; right: 100px; margin: 0;
	&:lang(de){position: relative; top: auto; right: auto; margin: 0 0 20px;}
	.ww-coupons-fields{
		width: auto; flex-grow: 1;
	}
	.coupon_message{position: absolute; background: #fff; border-radius: @borderRadius; padding: 5px 25px; top: 50px; width: 297px; right: 0; font-weight: bold; font-size: 13px;}
	@media (max-width: @l){right: 60px;}
	@media (max-width: @t){
		position: relative; top: auto; right: auto;
		.ww-coupons-fields{width: auto; flex-grow: 1; width: 400px;}
		.coupon_message{width: 415px;}
	}
	@media (max-width: @tp){
		.ww-coupons-fields{flex-grow: 0; flex-shrink: 0; width: 295px;}
		.coupon_message{width: 295px;}
	}
	@media (max-width: @m){
		.ww-coupons-fields{width: 100%;}
		.coupon_message{position: relative; background: none; top: auto; right: auto; font-size: 12px; width: 100%; padding-left: 15px;}
	}
}
.ww-auth-coupons-form{
	display: flex; align-items: center;
	label{
		padding-right: 20px; padding-bottom: 0;
		p{padding: 0;}
	}
	@media (max-width: @t){
		margin-bottom: 20px;
		label{flex-grow: 1;}
	}
	@media (max-width: @m){
		flex-wrap: wrap;
		label{margin-bottom: 8px; padding-left: 30px;}
	}
}
.col-coupon-num{
	width: 150px; flex-grow: 0; flex-shrink: 0;
	@media (max-width: @tp){flex-grow: 1;}
}
.col-coupon-desc{
	flex-grow: 1;
	@media (max-width: @tp){display: none;}
}
.col-coupon-total{
	width: 150px;
	@media (max-width: @tp){width: 125px; flex-grow: 0; flex-shrink: 0; padding-left: 0; padding-right: 0;}
}
.w-table-col{
	padding: 15px 5px 15px 20px;
	&.col-coupon-valid{
		width: 200px; text-align: right; padding-right: 20px; flex-shrink: 0;
		@media (max-width: @tp){width: 160px;}
		@media (max-width: @m){width: 100%; text-align: left;}
	}
	&.col-coupon-total{text-align: center; padding-right: 20px;
		@media (max-width: @m){text-align: left;}
	}
	@media (max-width: @m){padding: 0 0 3px; width: 100%!important;}
}
.coupon-row .label{
	display: none;
	@media (max-width: @m){display: inline; font-weight: bold;}
}
@media (max-width: @m){
	.coupon-row{display: block;}
}


.ww-coupons-preview{
	margin-bottom: 0; padding: 10px 0;
	&.preview-active, &.active{
		.ww-coupons-container{display: block;}
	}
	&.active{
		.ww-coupons-label{display: none;}
		.ww-coupons-container{padding-top: 0;}
	}
	@media (max-width: @tp){
		padding: 13px 25px; border-bottom: 1px solid @borderColor;
	}
}
.ww-coupons-container{display: none; padding-top: 10px;}
.ww-preview-coupons-label{
	text-decoration: underline; color: @green; padding-bottom: 0; cursor: pointer;
	p{padding-bottom: 0;}	
}
.preview-coupon-container{
	position: relative; margin-top: 10px;
}
.coupon-message{position: absolute; top: 100%; left: 0; padding-left: 20px;}	
.ww-coupons-add{
	width: auto; padding: 0 20px; white-space: nowrap; color: @darkGreen; flex-shrink: 0; border-radius: unset;
	&:hover{background: #d4d7d6; color: @green;}
	&:after{display: none;}
}
/*------- /coupons -------*/

/*------- testimonials -------*/
.testimonials{
	position: relative; z-index: 1;
	@media (max-width: @t){padding-left: 30px; padding-right: 30px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px;}
	@media (max-width: @m){padding: 0; content-visibility: auto; contain-intrinsic-size: 450px;}
}
.wrapper-testimonials{
	background: #fff; box-shadow: 0 25px 50px 0 rgba(0,0,0,0.2); display: flex; height: 420px; align-items: center;
	@media (max-width: @l){height: 350px;}
	@media (max-width: @t){
		height: 320px;
		&:lang(de){height: 355px;}
	}
	@media (max-width: @m){
		flex-direction: column; flex-direction: column-reverse; height: auto; box-shadow: none;
		&:lang(de){height: auto;}
	}
}
.ts-col1{
	width: calc(~"100% - 520px"); padding: 0 120px; flex-grow: 1;
	@media (max-width: @l){padding: 0 60px;}
	@media (max-width: @t){padding: 0 45px;}
	@media (max-width: @tp){padding: 0 30px 0 25px;}
	@media (max-width: @m){width: 100%;}
	@media (max-width: @m){padding: 20px 15px;}
}
.ts-col2{
	width: 520px; font-size: 18px; line-height: 1.5; flex-grow: 0; flex-shrink: 0; color: #fff; display: flex; align-items: center; align-self: flex-start; height: 100%;
	&:lang(de){font-size: 16px;}
	h3{
		font-size: 36px; padding: 0 0 8px; font-weight: normal;
		&:lang(de){font-size: 30px;}
	}
	img{position: relative; margin: 0 0 -35px -30px;}
	.btn{
		margin-right: 10px; margin-top: 5px; height: 50px; min-width: 170px;
		&:lang(de){padding: 0 15px; min-width: 0; font-size: 14px;}
		&:last-child{margin-right: 0;}
	}
	@media (max-width: @l){
		width: 430px; font-size: 16px;
		&:lang(de){width: 465px;}
		img{max-width: 170px; margin: 0 0 -25px -22px;}
		h3{
			font-size: 30px;
			&:lang(de){font-size: 25px;}
		}
		.btn{min-width: 45%; padding: 0;}
	}
	@media (max-width: @t){
		width: 325px; font-size: 15px;
		img{max-width: 115px; margin: 0 0 -20px -16px;}
		h3{font-size: 24px;}
		.btn{margin-top: 0; font-size: 14px; height: 47px; margin-top: 0; min-width: 0; width: calc(~"100% - 5px");}
	}
	@media (max-width: @tp){
		width: 255px; font-size: 14px;
		&:lang(de){font-size: 14px;}
		h3{font-size: 22px;}
		img{max-width: 95px; margin: 0 0 -15px -12px;}
	}
	@media (max-width: @m){
		width: 100%;
		&:lang(de){width: 100%;}
		img{position: absolute; left: 3px; top: 0; max-width: 75px;}
		h3{
			padding: 11px 0 15px 72px; font-size: 18px;
			&:lang(de){font-size: 18px;}
		}
		.btn{height: 45px;}
	}
}
.ts-btns{
	@media (max-width: @tp){display: flex; justify-content: space-between;}
	@media (max-width: @m){flex-direction: column; gap: 10px}
}
.ts-col2-cnt{
	margin: -15px 80px 0;
	@media (max-width: @l){margin-left: 60px; margin-right: 40px;}
	@media (max-width: @t){margin-left: 40px; margin-top: 0;}
	@media (max-width: @tp){margin: 20px 20px 15px 25px; position: relative;}
}
.ts-items{
	.swiper-button-prev{left: -145px;}
	.swiper-button-next{right: -145px;}
	@media (max-width: @l){
		.swiper-button-prev{left: -87px;}
		.swiper-button-next{right: -85px;}
	}
	@media (max-width: @t){
		.swiper-button-next{right: -65px;}
		.swiper-button-prev{left: -65px;}
		.slick-track{display: flex; align-items: center;}
	}
	@media (max-width: @tp){
		.swiper-button-next{right: -45px;}
		.swiper-button-prev{left: -38px;}
	}
	@media (max-width: @m){
		.swiper-button{box-shadow: none; border: 1px solid @borderColor; top: 55px;}
		.slick-track{align-items: flex-start;}
		.swiper-button-prev{left: auto; right: 45px;}
		.swiper-button-next{right: 0;}
	}
}
.ts{
	display: flex; font-size: 16px; align-items: center;
	@media (max-width: @t){font-size: 14px;}
	@media (max-width: @tp){font-size: 13px;}
	@media (max-width: @m){display: block; font-size: 12px;}
}
.ts-image{
	width: 180px; flex-grow: 0; flex-shrink: 0; margin-right: 60px; position: relative; display: flex; align-items: center; justify-content: center;
	img{border-radius: 100px; display: block; width: 180px; height: auto;}
	&:before{content:""; display: block; padding-top: 100%;}
	&:after{.pseudo(60px,60px); .icon-quote(); font: 27px/27px @fonti; display: flex; align-items: center; justify-content: center; color: @lightGreen; background: #fff; border-radius: 100px; position: absolute; bottom: -10px; right: 0;}
	@media (max-width: @l){
		margin-right: 40px;
		img{width: 150px;}
	}
	@media (max-width: @t){
		width: 130px;
		img{width: 130px;}
		&:after{width: 50px; height: 50px; font-size: 23px; line-height: 23px;}
	}
	@media (max-width: @tp){
		width: 105px; margin-right: 25px;
		&:after{width: 45px; height: 45px; font-size: 18px; bottom: -17px;}
	}
	@media (max-width: @m){
		width: 75px; margin-bottom: 15px;
		&:after{width: 40px; height: 40px; font-size: 17px; right: -15px; bottom: -15px;}
	}
}
.ts-comment{
	font-style: italic; line-height: 1.6;
	@media (max-width: @t){line-height: 1.4;}
}
.ts-name{
	font-weight: bold; font-size: 15px; display: flex; align-items: center;
	&:before{content:url(images/fb.png); display: block; width: 18px; height: 18px; top: -1px; margin-right: 10px; position: relative;}
	@media (max-width: @m){font-size: 12px;}
}
.ts-cnt{
	img{display: block; margin-bottom: 10px;}
	@media (max-width: @tp){
		img{max-height: 14px; width: auto; margin-bottom: 5px;}
	}
	@media (max-width: @m){
		img{margin-bottom: 10px;}
	}
}
/*------- /testimonials -------*/

/*------- auth -------*/
.page-auth-index{
	.a-title{display: none;}
}
.page-auth:not(.page-auth-forgotten_password){
	.main-wrapper{
		flex-direction: row-reverse;
		@media (max-width: @m){
			flex-direction: column-reverse; flex-wrap: wrap;
		}
	}
	.sidebar{
		border: 0; border-right: 1px solid @borderColor; margin: 0; padding: 0;
		@media (max-width: @t){width: 300px;}
		@media (max-width: @tp){display: block; width: 175px; flex-shrink: 0; flex-grow: 0;}
		@media (max-width: @m){border: 0; border-bottom: 1px solid @borderColor; width: 100%;}
	}
	.main-content{
		margin: 0; padding: 60px 100px 50px;
		@media (max-width: @l){padding-left: 60px; padding-right: 60px;}
		@media (max-width: @t){padding-left: var(--wrapperOffset); padding-right: var(--wrapperOffset); padding-top: 50px;}
		@media (max-width: @tp){padding-top: 25px; padding-left: 30px; padding-right: 30px;}
		@media (max-width: @m){padding: 15px 15px 0;}
	}
	@media (max-width: @m){
		.lists ul{margin-left: 5px;}
		.global-success{margin-bottom: 30px;}
	}
}
.a-col{
	width: 50%; padding: 65px 140px; font-size: 14px;
	@media (max-width: @l){padding: 50px 80px;}
	@media (max-width: @t){padding: 35px 50px;}
	@media (max-width: @tp){padding: 25px 30px 40px;}
	@media (max-width: @m){
		padding: 15px; width: 100%; font-size: 12px;
		ul li:before{top: 8px;}
	}
}
.auth-forgotten-password-form{max-width: 520px;}
.a-col2{border-left: 1px solid @borderColor; padding-bottom: 30px;}
@media (max-width: @m){
	.page-auth-signup .a-col2{
		border-left: 0;
		border-top: 1px solid @borderColor; padding: 25px 15px; margin-top: 5px;		
	}
}
.a-subtitle, .a-col2 h2{
	font-size: 22px; font-weight: bold; padding: 0 0 10px;
	@media (max-width: @t){font-size: 20px;}
	@media (max-width: @tp){font-size: 18px;}
	@media (max-width: @m){font-size: 16px;}
}
.a-subtitle-personal{padding-top: 17px;}
.a-login-subtitle{padding-bottom: 5px; line-height: 1.3;}
.reg-row{
	padding-bottom: 24px;
	@media (max-width: @tp){padding-bottom: 18px;}
}
.reg-first_name{padding-bottom: 15px;}
.login-reset-pass{padding-bottom: 40px;}
.a-btns{display: flex; align-items: center;}
.btn-auth-submit{
	min-width: 220px; padding: 0 20px;
	@media (max-width: @t){min-width: 160px;}
	@media (max-width: @tp){min-width: 140px;}
	@media (max-width: @m){min-width: 120px;}
}
.btn-login{margin-top: 5px; min-width: 200px;}
.a-links{
	text-align: center; font-size: 14px; line-height: 1.8; flex-grow: 1; max-width: 300px;
	span{display: block;}
	@media (max-width: @t){font-size: 12px;}
	@media (max-width: @m){font-size: 11px;}
}
.a-signup-links{font-size: 12px;}
.auth-form-login{padding-top: 10px;}
.field-remember, .field-remember_me, :deep(.field-remember_me){
	margin-top: 5px;
	@media (max-width: @tp){
		input[type=checkbox]+label{font-size: 14px;}
	}
	@media (max-width: @m){
		input[type=checkbox]+label{padding-top: 0;}
	}
}
.field-warehouse_location{
	label{position: relative!important; top: auto!important; left: auto!important; font-weight: bold; font-size: 16px!important; display: block; padding: 10px 0 5px; color: @textColor!important;}
	&.ffl-floated select{padding-top: 0;}
	@media (max-width: @tp){
		label{font-size: 14px!important;}
	}
}
@media (max-width: @tp){
	.page-auth .field-warehouse_location label{font-size: 16px!important;}
}
@media (max-width: @m){
	.page-auth .field-warehouse_location label{font-size: 14px!important; padding-top: 20px;}
}
.reg-loyalty_request{
	padding: 0 0 20px 155px; position: relative; border-bottom: 1px solid @borderColor; margin-bottom: 5px; display: flex; flex-flow: column;
	&:before{.pseudo(129px,80px); background: url(images/card-small-hr.png) no-repeat left top; background-size: contain; left: 0; top: 0;}
	&:lang(de):before{background: url(images/card-small-de.png) no-repeat left top;}
	&:lang(en):before{background: url(images/card-small-en.png) no-repeat left top;}
	&:lang(sl):before{background: url(images/card-small-si.png) no-repeat left top;}
	@media (max-width: @t){
		padding-left: 100px;
		&:before{width: 85px;}
	}
	@media (max-width: @tp){
		padding-left: 80px;
		&:before{width: 65px;}
	}
	@media (max-width: @m){
		padding-left: 0;
		&:before{display: block; position: relative; top: auto; left: auto; width: 75px; height: 50px; margin-bottom: 10px;}
	}
}
.field-loyalty-note-inner{
	padding-top: 4px; display: block;
	p{padding-bottom: 0;}
}
.a-loyalty{
	font-weight: bold; font-size: 16px; padding: 0 0 10px;
	@media (max-width: @tp){font-size: 14px;}
	@media (max-width: @m){font-size: 12px;}
}
.reg-loyalty_request{
	.label-loyalty_request{display: none;}
	input[type=radio]:checked~.field-loyalty-note{display: block;}
}
.reg-newsletter{padding-bottom: 10px;}
.btn-signup{min-width: 220px;}
.field-loyalty-note{
	display: none; margin-left: 32px; position: relative; font-size: 13px; line-height: 1.4;
	label{text-align: left;}
	input{width: 100%!important; display: block; margin-top: 5px;}
}
.field-loyalty_code{display: none!important;}
.field-loyalty{
	padding-bottom: 13px;
	&:last-child{padding-bottom: 0;}
	input[type=radio]+label{font-size: 14px;}
}
.field-newsletter, .field-accept_terms{
	input[type=checkbox]+label{
		font-size: 14px;
		@media (max-width: @m){font-size: 12px;}
	}
	.error{padding-left: 30px;}
}
.field-accept_terms{
	font-weight: bold;
	a{color: @green;}
}
.reg-have-acc{
	font-size: 22px; font-weight: bold; padding: 40px 0 10px;
	a{text-decoration: none;}
	@media (max-width: @t){font-size: 20px;}
	@media (max-width: @tp){font-size: 18px;}
	@media (max-width: @tp){padding-top: 20px;}
	@media (max-width: @m){font-size: 16px;}
}

.social-login{
	margin-top: 50px;
	@media (max-width: @t){margin-top: 40px;}
	@media (max-width: @m){
		justify-content: space-between;
		.btn-social{width: calc(~"50% - 5px"); min-width: 0; margin: 0;}
	}
}
.social-login-title{
	font-weight: bold; font-size: 16px; padding: 0 0 10px;
	@media (max-width: @t){font-size: 14px;}
	@media (max-width: @m){font-size: 12px;}
}
.login-social-buttons{
	display: flex;
	.btn-social{
		height: 50px; display: flex; align-items: center; min-width: 140px; justify-content: center; border: 1px solid @borderColor; border-radius: @borderRadius; margin-right: 10px; padding: 0; font-size: 0; .transition(border-color); cursor: pointer;
		&:hover{border-color: @borderColor/1.2;}
		@media (max-width: @m){height: 45px;}
	}
	img{display: block;}
	.btn-social-google img{margin-bottom: -4px;}
	@media (max-width: @m){justify-content: space-between;}
}
.quick-social-login{
	padding: 20px 35px 25px; margin: 0; border-top: 1px solid @borderColor;
	.social-login-title{font-size: 14px;}
	.btn-social{width: calc(~"50% - 5px"); margin: 0;}
	.btn-social-fb{margin-right: 10px;}
}

.a-sidebar-cnt{
	padding: 33px 50px 0;
	@media (max-width: @l){padding-left: 30px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px; padding-top: 25px;}
	@media (max-width: @m){padding: 0 15px 10px;}
}
.a-dashboard-title{
	font-size: 16px; line-height: 1.5; margin-bottom: 20px;
	span{font-weight: bold; font-size: 26px; display: block;}
	@media (max-width: @t){
		font-size: 13px;
		span{font-size: 22px;}
	}
	@media (max-width: @tp){
		span{font-size: 28px;}
	}
	@media (max-width: @m){
		padding: 15px 15px 10px; margin-bottom: 15px; border-bottom: 1px solid @borderColor;
		span{font-size: 20px;}
	}
}
.auth-box-loyalty{
	background: url(images/bg-green.jpg); color: #fff; padding: 35px 50px;
	.error{color: #fff;}
	@media (max-width: @l){padding-left: 30px; padding-right: 30px;}
	@media (max-width: @tp){margin-top: 40px; padding: 25px 30px;}
	@media (max-width: @m){padding: 25px; margin-left: -15px; margin-right: -15px; margin-top: 0;}
}
.auth-loyalty-card{
	margin-bottom: 5px;
	img{display: block;}
	@media (max-width: @t){
		img{max-width: 90px;}
	}
	@media (max-width: @tp){
		display: flex; align-items: center;
		img{max-height: 30px; width: auto;}
	}
	@media (max-width: @m){
		img{max-height: 40px;}
	}
}
.auth-loyalty-card-label{
	display: none; font-size: 18px; padding-left: 10px; margin-bottom: 10px;
	@media (max-width: @tp){display: block;}
	@media (max-width: @m){margin-bottom: 0;}
}
.auth-loyalty-cnt{
	font-size: 14px; padding-bottom: 15px;
	p{padding: 0;}
	.btn{margin-top: 20px; padding: 0 25px;}
	.label{font-weight: bold;}
	@media (max-width: @t){font-size: 13px;}
	@media (max-width: @tp){
		padding-top: 5px;
		p{padding: 0 0 4px;}
		.btn{margin-top: 10px;}
	}
}
@media (max-width: @m){
	.has-loyalty-card{
		position: relative; padding-top: 15px;
		img{max-height: 34px; position: absolute; left: 20px; top: 28px;}
		.auth-loyalty-card-label{display: none;}
		.auth-loyalty-cnt{padding-left: 65px; padding-bottom: 0;}
	}
}
.auth-loyalty-btns{
	display: flex; align-items: center; padding-bottom: 25px;
	.btn{margin-right: 10px; width: 140px; padding: 0;}
	@media (max-width: @m){
		justify-content: space-between;
		.btn{width: calc(~"50% - 5px"); margin: 0;}
	}
}
.auth-title-loyalty{
	font-size: 22px;
	@media (max-width: @t){font-size: 18px;}
	@media (max-width: @tp){display: none;}
}
.auth-loyalty-field-card{
	input{width: 100%; color: @textColor; border: none; border-bottom-left-radius: 0; border-bottom-right-radius: 0; height: 45px;}
	@media (max-width: @tp){
		display: flex; flex-wrap: wrap;
		input{height: 47px; border-top-right-radius: 0; border-bottom-right-radius: 0; width: auto; flex-grow: 1;}
	}
	@media (max-width: @m){
		input{height: 40px;}
	}
}
.btn-loyalty-add{
	width: 100%; border-top-left-radius: 0; border-top-right-radius: 0;
	@media (max-width: @tp){width: 160px; flex-shrink: 0; flex-grow: 0; padding: 0;}
	@media (max-width: @m){width: 100%;}
}
.a-intro{
	margin-bottom: 60px; font-size: 14px;
	@media (max-width: @t){margin-bottom: 50px;}
	@media (max-width: @tp){font-size: 12px;}
	@media (max-width: @m){background: @borderColor; display: block; margin: -15px -15px 0;}
}
@media (max-width: @m){
	.a-intro-left{display: none;}
	.orders-container{margin-top: 15px; margin-bottom: 25px;}
}
.a-intro-user{
	border-left: 1px solid @borderColor; width: 420px; flex-grow: 0; flex-shrink: 0; padding-left: 60px;
	p{padding: 0 0 10px;}
	@media (max-width: @l){width: 320px; padding-left: 35px;}
	@media (max-width: @t){width: 200px; padding-left: 25px;}
	@media (max-width: @tp){width: 225px;}
	@media (max-width: @m){width: 100%; padding: 15px;}
}
.a-intro-title{
	font-size: 20px; font-weight: bold; line-height: 1.3; padding: 0 0 8px;
	@media (max-width: @t){font-size: 16px;}
	@media (max-width: @m){font-size: 14px;}
}
.lists .a-menu{
	margin: 0;
	a{
		color: @green;
		&:hover{color: @green/1.2;}
	}
	@media (max-width: @tp){
		li{
			margin-bottom: 4px;
			&:before{top: 8px;}
		}
	}
	p{padding-bottom: 0;}
}
.btn-auth-edit{
	height: 42px;
	@media (max-width: @m){background: #fff;}
}
.a-title{
	font-size: 26px; padding: 0 0 20px;
	@media (max-width: @tp){font-size: 22px;}
	@media (max-width: @m){display: none;}
}
.a-title-counter{color: @green; font-size: 16px; font-weight: normal;}
.a-title-wishlist-counter{
	display: none;
	&.active{display: inline;}
}
.auth-form-edit{
	max-width: 540px;
	@media (max-width: @l){width: 425px;}
	@media (max-width: @m){width: 100%; padding-bottom: 20px;}
}
.a-edit-subtitle{
	font-weight: bold; font-size: 16px; padding: 0 0 6px;
	@media (max-width: @tp){padding-bottom: 10px;}
	@media (max-width: @m){font-size: 14px;}
}
.field-edit-warehouse_location{
	margin: 10px 0 15px;
	@media (max-width: @m){margin-top: 0;}
}
#field-company_oib{width: 100%!important;}
.field-edit-newsletter{margin: 10px 0 5px;}

.w-table{
	display: flex; font-size: 14px; text-decoration: none; line-height: 1.2; align-items: center;
	@media (max-width: @t){font-size: 13px;}
	@media (max-width: @m){display: block; font-size: 12px; position: relative;}
}
.w-table-label{
	display: none;
	@media (max-width: @m){display: inline; font-weight: bold; padding-right: 2px;}
}
.w-table-head{
	background: #F4F5F3; font-weight: bold; border-radius: @borderRadius;
	.w-table-col{padding-top: 10px; padding-bottom: 10px;}
	@media (max-width: @m){display: none;}
}
.col-num{width: 130px;
	@media (max-width: @t){width: 115px;}
	@media (max-width: @m){color: @textColor!important;}
}
.col-date{
	width: 110px;
	@media (max-width: @t){width: 95px;}
}
.col-total{
	width: 140px;
	span:not(.w-table-label){display: block;
		@media (max-width: @m){display: inline-block;}
	}
	ins{
		display: none;
		@media (max-width: @m){display: inline-block;}
	}
	@media (max-width: @t){width: 105px;}
}
.col-status{flex-grow: 1; font-size: 12px;}
.col-td-status{
	line-height: 1.3;
	@media (max-width: @m){margin-top: 4px;}
}
.col-btns{
	width: 160px; text-align: right; padding-right: 10px;
	@media (max-width: @tp){width: 50px; padding: 0 10px 0 0;}
	@media (max-width: @m){position: absolute; top: 0; right: 0; padding: 0;}
}
.order-status{width: 100%; height: 3px; border-radius: @borderRadius; background: #E9ECEB; margin-top: 5px;}
.order-status-bar{display: block; height: 100%; background: @lightGreen;}
.col-total{font-weight: bold;}
.order-details{display: none;}
.order-row{
	border-bottom: 1px solid @borderColor;
	&.active{
		.col-num{color: @green;}
		.order-details{display: block;}
	}
	@media (max-width: @m){
		margin: 0 -15px 10px; padding: 0 15px 10px;
		&:last-child{border: 0; margin-bottom: 0;}
	}
}
.auth-box-orders{
	.order-row{border-bottom: 0;}
}
.btn-order-details{
	position: relative; display: flex; align-items: center; justify-content: flex-end; font-size: 12px; .transition(color);
	.btn-active{display: none;}
	.toggle-icon{.scale(0.8); margin-left: 7px;}
	&:hover{color: @green; cursor: pointer;}
	&.active{
		color: @green;
		.toggle-icon:before, .btn-inactive{display: none;}
		.btn-active{display: inline;}
	}
	@media (max-width: @tp){width: 100%; height: 100%; font-size: 0;}
	@media (max-width: @m){font-size: 12px;}
}

.wp-details{
	font-size: 12px; display: flex; padding: 15px 0 8px; margin: 0; border: 0;
	@media (max-width: @m){
		.wp-total{bottom: auto; top: 16px; width: 110px; right: 0;}
		.wp-cnt{padding-right: 110px;}
	}
}
.wp-details-content{
	display: flex; flex-grow: 1;
	@media (max-width: @m){flex-direction: column;}
}
.wp-details-btns{
	width: 165px; display: flex; justify-content: flex-end; align-items: flex-start; flex-shrink: 0; flex-grow: 0;
	@media (max-width: @t){padding-right: 12px; padding-top: 2px;}
	@media (max-width: @tp){padding-top: 6px; width: 150px;}
	@media (max-width: @m){width: 100%; padding-right: 0; justify-content: flex-start;}
}
.btn-order-again{
	position: relative; text-decoration: none; display: flex; align-items: center; padding-left: 28px;
	&:before{.icon-cart(); font: 18px/18px @fonti; color: @orange; margin-right: 8px; position: absolute; left: 0; top: -2px;}
	&:hover{color: @orange;}
	@media (max-width: @t){
		padding-left: 25px;
		&:before{font-size: 16px; line-height: 16px;}
	}
}
.wp-total-sum{
	border-top: 1px solid @borderColor; font-size: 12px; line-height: 1.5; text-align: right; padding: 20px 0; margin: 20px 160px 10px 0;
	.label{font-weight: bold;}
	.value{text-align: left; width: 280px; display: inline-block; vertical-align: top; padding-left: 20px;}
	@media (max-width: @tp){margin-right: 0;}
	@media (max-width: @m){
		font-size: 11px; margin-bottom: 0; padding-bottom: 20px;
		.value{width: auto; padding-left: 2px;}
	}
}
@media (max-width: @m){
	.auth-box-btns{
		padding-left: 60px;
		.btn{padding: 0 30px; width: 100%;}
	}
}
.wp-total-row{
	margin-bottom: 5px;
	@media (max-width: @m){display: flex; margin-bottom: 2px;}
}
.wp-total-row-total{font-weight: bold;}
.a-section-title{
	font-size: 26px; font-weight: bold; padding: 0 0 15px; line-height: 1.5;
	a{text-decoration: none;}
	@media (max-width: @t){font-size: 22px;}
	@media (max-width: @m){font-size: 16px; padding: 0 0 10px;}
}
@media (max-width: @t){
	.page-login, .page-signup{
		background: url(images/bg.jpg);
		.main{background: #fff; width: auto; margin: -80px var(--wrapperOffset) 0; border-radius: @borderRadius;}
		.bottom{margin-top: -80px;}
		.header{padding-bottom: 85px;}
		.wrapper-bottom{padding-top: 140px;}
	}
}
@media (max-width: @tp){
	.page-login, .page-signup{
		.bottom{margin-top: -40px;}
		.wrapper-bottom{padding-top: 90px;}
	}
}
@media (max-width: @m){
	.page-login, .page-signup{
		.header{padding-bottom: 0;}
		.main, .bottom{margin: 0;}
		.main{border-radius: 0;}
		.wrapper-bottom{padding-top: 0;}
	}
	.a-row{display: block;}
}
/*------- /auth -------*/

/*------- gdpr -------*/
.gdpr-container-wrapper{
	z-index: 100000000!important; font-size: 14px; width: 555px; left: 20px!important; bottom: 20px!important; border-radius: @borderRadius;
	@media (max-width: @m){left: 10px!important; right: 10px!important; bottom: 10px!important; width: auto; font-size: 12px;}
}
.cookie-warning-wrapper{
	padding: 20px 30px 20px 30px!important;
	p{padding-bottom: 10px;}
	.gdpr-cookie-btns{padding: 0; font-size: 0;}
	@media (max-width: @m){
		padding: 20px 20px 15px 20px!important;
		.btn{display: flex!important;}
	}
}
body.gdpr-popup .cookie-warning-wrapper{padding-right: 65px!important;}
.gdpr-cookie-btns .btn{margin: 0 5px 0 0; font-size: 13px; height: 40px; padding: 0 20px; font-weight: normal;}
#gdpr_configurator{
	label{font-weight: bold;}
	input:disabled+label{color: @textColor;}
}
/*------- /gdpr -------*/

/*------- fancybox -------*/
.fancybox-close{
	background: none; text-decoration: none; width: 38px; height: 38px; line-height: 38px; border-radius: 100px; background: @darkGreen; border: 3px solid #fff; box-sizing: content-box; display: flex; align-items: center; justify-content: center; .transition(all);
	&:before{.icon-cross(); font: 14px/14px @fonti; color: #fff;}
	&:hover{background: @darkGreen/1.2; text-decoration: none;}
	@media (max-width: @l){
		width: 30px; height: 30px;
		&:before{font-size: 12px;}
	}
}
.fancybox-skin{border-radius: @borderRadius;}
.fancybox-overlay{
	background: rgba(28,34,25,0.75);
	@media (max-width: @m){height: 100vh!important;}
}
#fancybox-loading{
	background: #ffffff!important; border-radius: 4px;
	div{background-color: #fff; background-image: url(images/loader.svg)!important; background-size: 30px auto;}
}
#fancybox-thumbs{background: none!important; margin: 0;}
.quick-cnt{
	h1{font-size: 38px;}
	@media (max-width: @m){
		h1{font-size: 25px;}
	}
}
@media (max-width: @t){
	.fancybox-wrap{position: fixed!important; top: 0!important; right: 0!important; bottom: 0!important; left: 0!important; display: flex; align-items: center; justify-content: center; width: auto!important;}
}
/*------- /fancybox -------*/

/*------- footer -------*/
.bottom{
	background: url(assets/images/footer.jpg); background-position: center bottom; background-size: cover; font-size: 16px; color: #fff; margin-top: -335px;
	a{
		text-decoration: none; color: #fff;
		&:hover{color: @lightGreen;}
	}
	@media (max-width: @l){margin-top: -295px; font-size: 14px;}
	@media (max-width: @t){margin-top: -240px;}
	@media (max-width: @tp){font-size: 13px; margin-top: -205px;}
	@media (max-width: @m){background-image: url(images/header-t.jpg)!important; margin-top: 0;}
}
.bottom-title{
	font-weight: bold; color: @lightGreen; font-size: 20px; padding: 0 0 15px;
	a{color: @lightGreen;}
	p{padding: 0;}
	@media (max-width: @l){
		&:lang(de){font-size: 18px;}
	}
	@media (max-width: @t){
		font-size: 16px; padding-bottom: 10px;
		&:lang(de){font-size: 16px;}
	}
	@media (max-width: @tp){
		font-size: 14px;
		&:lang(de){font-size: 14px;}
	}
	@media (max-width: @m){
		font-size: 12px; color: #fff; padding: 12px 15px; border-top: 1px solid rgba(255,255,255,.2); position: relative;
		.toggle-icon{
			position: absolute; top: 14px; right: 15px;
			&:before, &:after{background: @lightGreen;}
		}
		a{color: #fff;}
	}
}
.bottom-locations{
	list-style: none; padding: 0; margin: 0; font-size: 14px;
	li{margin: 0 0 7px;}
	a{
		display: block; position: relative;
		&:before{.icon-pin(); font: 18px/18px @fonti; color: @lightGreen; margin: 2px 10px 0 0; top: 3px; position: relative;}
	}
	@media (max-width: @tp){
		font-size: 13px;
		a:before{font-size: 14px; line-height: 14px; top: 0; margin-right: 8px;}
	}
}
.nav-bottom{
	list-style: none; padding: 0; margin: 0;
	li{margin: 0 0 4px;}
	@media (max-width: @m){
		display: flex; flex-wrap: wrap;
		li{margin-bottom: 7px; width: 50%;}
	}
}
@media (max-width: @m){
	.bottom-col-cnt{overflow: hidden; max-height: 0; padding-left: 15px; padding-right: 15px; .transition(all);}
	.bottom-col.active{
		.bottom-col-cnt{max-height: 1000px; padding-bottom: 15px;}
		.toggle-icon:before{display: none;}
	}
}
.wrapper-bottom{
	padding: 445px 80px 160px;
	@media (max-width: @l){padding-top: 365px; padding-left: 40px; padding-right: 40px; padding-bottom: 100px;}
	@media (max-width: @t){padding-top: 285px; padding-left: 65px; padding-right: 65px;}
	@media (max-width: @tp){padding-left: 20px; padding-right: 20px; padding-top: 250px;}
	@media (max-width: @m){padding: 0; display: block;}
}
.bottom-col, .footer-col{
	width: 26%;
	&:last-child{width: auto; margin-left: auto;}
	@media (max-width: @t){width: 24%;}
	@media (max-width: @m){width: 100%!important;}
}
@media (max-width: @t){
	.bottom-col2{width: 22%;}
}
.bottom-col3{
	p{padding: 0 0 5px;}
	ul{
		.list; margin: 0;
		li{
			&:before{background: @lightGreen;}
			@media (max-width: @m){
				&:before{top: 10px;}
			}
		}
	}
	@media (max-width: @t){width: 27%;}
}
.exchange-note{
	padding-top: 50px;
	@media (max-width: @tp){padding-top: 25px;}
	@media (max-width: @m){padding: 0; width: 100%; position: absolute; top: 80px;}
}
.footer{
	background: #fff; font-size: 12px;
	a:hover{text-decoration: none; color: @lightGreen;}
	@media (max-width: @t){font-size: 12px;}
}
.wrapper-footer{
	padding: 30px 0 30px 80px; justify-content: space-between; align-items: center;
	@media (max-width: @l){padding: 25px 0 25px 45px;}
	@media (max-width: @t){position: relative; justify-content: flex-end; margin-right: 85px; margin-left: 70px; padding-left: 0; padding-right: 0; width: auto;}
	@media (max-width: @tp){margin-left: 20px; margin-right: 20px;}
	@media (max-width: @m){margin-left: 15px; margin-right: 15px; padding: 0; height: 165px;}
}
.footer-col{
	width: 25%;
	&:last-child{margin: 0; flex-grow: 1;}
	@media (max-width: @l){
		&:last-child{flex-grow: 0;}
	}
	@media (max-width: @tp){width: auto;}
}
@media (max-width: @t){
	.footer-col1{position: absolute; top: 15px; left: 0;}
	.footer-col2{width: auto; flex-grow: 1; position: absolute; left: 2px; bottom: 15px;}
}
@media (max-width: @m){
	.footer-col1{top: auto; bottom: 37px;}
}
.footer-col3{
	display: flex; align-items: center;
	img{margin: 0 3px 0 0; display: block;}
	@media (max-width: @l){
		img{margin: 0; width: auto; height: 20px;}
	}
	@media (max-width: @tp){
		img{height: 18px;}
	}
	@media (max-width: @m){
		position: absolute; top: 20px; left: 0; flex-wrap: wrap;
		a{margin-bottom: 10px;}
		img{margin-right: 1px;}
	}
}
.copy{
	position: relative; padding-left: 37px;
	&:before{.icon-symbol(); color: @lightGreen; font: 22px/22px @fonti; bottom: 0px; left: 0; position: absolute;}
	@media (max-width: @t){
		font-size: 11px; padding-left: 35px;
		&:before{font-size: 19px; line-height: 19px;}
	}
	@media (max-width: @m){
		padding-left: 25px;
		&:before{font-size: 14px; line-height: 14px;}
	}
}
.dev{
	a{
		text-decoration: none; position: relative;
		&:first-child{
			padding-left: 35px;
			&:before{.icon-marker(); font: 24px/24px @fonti; color: @lightGreen; position: absolute; left: 0; bottom: -4px;}
			@media (max-width: @t){
				&:before{font-size: 19px; line-height: 19px; bottom: -2px;}
			}
			@media (max-width: @m){
				padding-left: 25px;
				&:before{font-size: 14px; line-height: 14px; bottom: 0;}
			}
		}
	}
	@media (max-width: @t){font-size: 11px;}
}
.safe-purchase{
	margin-left: 20px; position: relative; font-size: 16px; text-decoration: none; padding-left: 30px; white-space: nowrap;
	&:before{.icon-shield(); font: 24px/24px @fonti; color: @lightGreen; position: absolute; left: 0; top: 0;}
	@media (max-width: @l){font-size: 14px;}
	@media (max-width: @t){
		font-size: 12px;
		&:before{font-size: 21px; line-height: 21px; top: -2px;}
	}
	@media (max-width: @m){display: block; margin-left: 0;}
}
.ontop{
	width: 60px; opacity: 0; pointer-events: none; height: 60px; border-radius: 200px; .gradient-green; display: flex; align-items: center; justify-content: center; text-decoration: none; position: fixed; bottom: 56px; right: 80px; box-shadow: 0 5px 20px 0 rgba(43,63,33,0.3); overflow: hidden; .transition(opacity); z-index: 101; cursor: pointer;
	&:before{.icon-arrow-down(); font: 10px/10px @fonti; color: #fff; .scaleY(-1); z-index: 5;}
	&:after{.pseudo(auto,auto); top: 0; right: 0; bottom: 0; left: 0; .gradient-green-hover; opacity: 0; .transition(opacity);}
	&:hover{
		&:after{opacity: 1;}
	}
	&.active{opacity: 1; pointer-events: auto;}
	@media (max-width: @l){width: 54px; height: 54px; right: 25px; bottom: 48px;}
	@media (max-width: @t){width: 48px; height: 48px; bottom: 100px;}
	@media (max-width: @m){bottom: 15px; right: 15px; width: 36px; height: 36px;}
}
@media (max-width: @m){
	.zEWidget-launcher{left: auto!important; right: 10px!important; bottom: 60px!important; margin: 0!important;}
}
#boxnowmap{position: relative; z-index: 999999;}
/*------- /footer -------*/

/*------- old browser info -------*/
.browser-note { 
	background:rgba(125,192,1,.98)!important; height: 100%; padding:8px 15px; font-size:14px; font-weight:bold; text-align:center; position: fixed; z-index:9999; top: 0; left: 0; right: 0;  z-index: 999999999999!important;
	a { color:#000; text-decoration:underline;}
	a:hover { color:#000; }
	img { margin: 10px 0; }
}
/*------- /old browser info -------*/

/*------- thank you -------*/
.invoice-container{border: 1px solid @borderColor; padding: 15px 20px; margin-bottom: 35px; margin-top: 10px;}
.btn-print{margin-top: 30px;}
.thank-you-wrapper{max-width: 500px; margin-bottom: 35px;}
.thank-you-login{margin: 15px 0; max-width: 300px;}
.thank-you-safe{font-size: 12px;}
.field-show-password input[type=checkbox]+label{font-size: 14px;}
@media (max-width: @m){
	.payment-transfer{max-width: calc(~"100vw - 30px"); overflow: auto;}
	.btn-download-invoice{height: auto; padding: 10px 15px; line-height: 1.3;}
	.thank-you-title{font-size: 20px;}
}
/*------- /thank you -------*/

/*------- loading -------*/
.form-loading{position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 100000001!important; color: #fff; font-size: 16px;}
.form-loading span{position: absolute; padding: 62px 20px 20px 20px; width: 200px; left: 50%; margin-left: -100px; top: 40%; text-align: center; box-shadow: 0px 0px 30px rgba(0,0,0,.4); color: #000; background: #fff url(images/loader.svg) no-repeat center 20px; border-radius: @borderRadius;}
.form-loading:before{content: ""; top: 0; bottom: 0; left: 0; right: 0; position: absolute; background: #000; background: rgba(0, 0, 0, .5);}
/*------- /loading -------*/


@media screen and (max-width: 980px) {
	/*------- 980 tables -------*/
	.table-wrapper { overflow-x: scroll; -webkit-overflow-scrolling:touch; }
	::-webkit-scrollbar {-webkit-appearance: none;width: 4px;}
	::-webkit-scrollbar-thumb {border-radius: 4px;background-color: rgba(0,0,0,.5);-webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);}	
	::-webkit-scrollbar-track {border: 1px solid #eaeaea; } 
	/*------- /980 tables -------*/	
}

.big-canvas-container{
	position: fixed; top: 0; right: 0; bottom: 0; left: 0; z-index: 9999; background: rgba(0,0,0,.5); display: none; align-items: center; justify-content: center;
	&.active{display: flex;}
	.big-canvas{width: 90vw; height: 80vh; max-width: 900px; max-height: 500px; background: #fff; border-radius: @borderRadius; box-shadow: 0 0 30px rgba(0,0,0,.5); padding: 10px; position: relative;}
}

@media (max-width: @m){
	.big-canvas-container .big-canvas{
		display: flex; flex-direction: column-reverse; max-height: 100%;
		#left-canvas, #right-canvas{float: none!important; width: 100%!important;}
		#searchinput{width: 100%!important; margin-left: 0!important;}
	}
}
.gm-style-mtc, #CybotCookiebotDialog {
	button:after{display: none!important;}
}
#CybotCookiebotDialog{
	input[type=checkbox], input[type=radio]{position: static; left: auto;}
}
.base-modal-toolbar-close{
	&:after{.icon-close2(); font: 18px/1 @fonti; color: #fff;}
}
.base-modal-cnt-close{
	.fancybox-close;
}
.base-modal-mask{
	background: rgba(28,34,25,0.75) !important;
}

.page-checkout{
	background: #fff; 
	.main,.wrapper{
		width: 1200px;
		@media screen and (max-width: @l){width: 1000px;}
		@media (max-width: @tp){width: auto;}
	}
	
	.hello-bar{display: none!important;}
	.footer-col1{
		flex-grow: 0; width: auto; flex-shrink: 0;
		@media (max-width: @m){position: relative; left: auto; bottom: auto;}
	}
	.footer-col2{
		display: none;
		@media (max-width: @t){display: block;}
		@media (max-width: @m){position: relative; left: auto; bottom: auto; margin-bottom: 5px;}
	}
	.page-wrapper{
		background: url(images/bg.jpg);
		@media (max-width: @m){background: none;}
	}
	.m-nav, .loyalty-quick, .m-nav-title{display: none!important;}
	.main{
		.box-shadow-yellow;
		@media (max-width: @l){width: 1200px;}
		@media (max-width: @t){background: #fff; border-radius: @borderRadius; width: auto; margin-left: 30px; margin-right: 30px;}
		@media (max-width: @tp){margin-left: 20px; margin-right: 20px;}
		@media (max-width: @m){box-shadow: none;}
	}

	@media (max-width: @tp){
		&:not(.page-webshop-login){
			.main{margin-left: 0; margin-right: 0; border-radius: 0; box-shadow: none;}
			&.main-offset-sm{
				.main{margin-top: 0;}
				.header{padding-bottom: 0;}
			}
			.footer{margin-top: 0; border-top: 1px solid @borderColor;}
		}
	}
	.header{z-index: 1;}
	.footer{
		margin-top: 70px;
		@media (max-width: @m){border-top: 1px solid @borderColor; margin-top: 25px;}
	}
	.wrapper-header{
		display: block; padding: 0; height: 120px;
		@media (max-width: @t){height: 100px;}
		@media (max-width: @m){height: 50px;}
	}
	.logo{
		position: absolute; left: 50px; top: 15px; width: 63px; height: 90px; margin: 0; z-index: 10;
		@media (max-width: @t){width: 50px; height: 70px;}
		@media (max-width: @tp){left: 23px;}
		@media (max-width: @m){
			left: 8px; height: 40px; top: 5px;
			&:before{font-size: 26px; line-height: 26px;}
		}
	}
	.checkout-return{
		position: absolute; font-size: 12px; left: 100px; top: 52px; z-index: 10;
		@media (max-width: 1600px){display: none;}
		@media (max-width: @m){display: block; left: auto; right: 7px; top: 7px;}
	}
	.btn-return-home{
		text-decoration: none; color: #fff; position: relative; padding: 0 0 0 23px;
		&:hover{text-decoration: underline; color: #fff;}
		&:before{.icon-arrow-left; font: 12px/10px @fonti; position: absolute; left: 0; top: 4px; color: #fff;}
		@media (max-width: @m){
			width: 35px; height: 35px; display: flex; align-items: center; justify-content: center; border-radius: @borderRadius; .gradient-orange; font-size: 0; padding: 0;
			&:before{.icon-cross; position: relative; top: auto; left: auto; font-size: 11px; line-height: 11px;}
		}
	}
	.checkout-title{
		padding: 0; position: absolute; left: 0; right: 0; top: 38px; font-size: 24px; line-height: 1.5;
		@media (max-width: @t){font-size: 20px;}
		@media (max-width: @m){font-size: 16px;}
		@media (max-width: @m){text-align: left; left: 65px; right: auto; top: 13px; font-size: 16px;}
	}
	.header-contact{
		position: absolute; top: 46px; right: 50px; font-weight: bold; z-index: 10;
		@media (max-width: @t){display: block;}
		@media (max-width: @tp){right: 20px;}
		@media (max-width: @m){position: relative; left: auto; top: auto; right: auto;}
	}
	.footer-col3{
		flex-grow: 1; justify-content: center;
		@media (max-width: @t){flex-grow: 0; width: auto;}
		@media (max-width: @m){justify-content: flex-start;}
	}
	@media (max-width: @m){
		.wc-row{display: block;}
		.footer-col3{position: relative; top: auto; margin: 15px 0 10px;}
	}
	.wc-col{
		width: 50%;
		@media (max-width: @m){width: 100%;}
	}
	.wc-col1{
		border-right: 1px solid @borderColor; display: flex; flex-direction: column;
		.wc-col-cnt{/*flex-grow: 1;*/ border-bottom: 1px solid @borderColor;}
		@media (max-width: @m){border-right: 0;}
	}
	@media (max-width: @m){
		.step2 .wc-col1{border-bottom: 1px solid @borderColor;}
	}
	.wc-step3-col1 .wc-col-cnt{
		padding-bottom: 35px;
		@media (max-width: @m){padding-bottom: 20px;}
	}
	.wc-title{
		font-size: 18px; line-height: 1.5; padding: 0 0 15px;
		@media (max-width: @tp){font-size: 16px;}
	}
	.wc-subtitle{font-size: 15px; line-height: 1.5; padding: 0 0 10px;}
	.wc-subtitle-login{
		font-size: 20px; padding-bottom: 5px;
		@media (max-width: @t){font-size: 18px;}
		@media (max-width: @m){font-size: 16px; padding-bottom: 2px;}
	}
	.form-label{
		input[type=checkbox]+label, input[type=radio]+label{
			font-size: 14px;
			@media (max-width: @m){font-size: 12px;}
		}
		.field-b_same_as_shipping{padding-bottom: 10px;}
		.field-message{margin-bottom: 5px;}
		.field{padding-bottom: 10px;}
		.field-phone{
			margin-bottom: 20px; margin-top: 10px;
			@media (max-width: @m){margin-bottom: 5px; margin-top: 5px;}
		}
	}
	#field-b_company_oib{width: 100%!important;}
	.wrapper-footer{
		padding-left: 50px; padding-right: 50px;
		@media (max-width: @t){padding-left: 0; padding-right: 0;}
		@media (max-width: @m){flex-direction: column-reverse; align-items: flex-start; padding-top: 15px; height: auto; padding-bottom: 15px;}
	}

	&.page-checkout-login .wc-col{
		padding: 50px 70px 65px;
		@media (max-width: @t){padding: 35px 50px 40px;}
		@media (max-width: @tp){padding: 25px 30px 35px;}
		@media (max-width: @m){border-right: 0; padding: 15px 15px 20px;}
	}
	.wc-cnt{
		font-size: 14px;
		@media (max-width: @m){font-size: 12px;}
	}
	.wc-col-cnt{
		padding: 25px 50px;
		@media (max-width: @t){padding-left: 40px; padding-right: 40px;}
		@media (max-width: @tp){padding: 15px 20px 20px;}
		@media (max-width: @m){padding: 15px;}
	}
	.wc-col-shipping{
		border-bottom: 1px solid @borderColor; padding-bottom: 35px;
		@media (max-width: @tp){padding-bottom: 25px;}
		@media (max-width: @m){margin-bottom: 5px;}
	}
	.wc-col-cart{
		padding-top: 10px;
		@media (max-width: @m){padding-bottom: 20px;}
	}
	.w-cart-title{
		padding: 0;
		.counter{font-weight: normal; font-size: 12px; color: @green;}
	}
	.ww-cart-header{
		display: flex; justify-content: space-between; align-items: center; padding-bottom: 25px;
		@media (max-width: @m){padding-bottom: 5px;}
	}
	.w-btn-change{font-size: 12px; color: @green; text-decoration: underline;}

	.ww-coupons{margin-bottom: 0;}
	.wc-login-form{margin-top: 15px;}
	:deep(.field-remember_me), .field-remember_me{
		margin-top: 5px;
		@media (max-width: @tp){
			input[type=checkbox]+label{font-size: 14px;}
		}
		@media (max-width: @m){
			input[type=checkbox]+label{padding-top: 0;}
		}
	}
	.btn-checkout{
		padding: 0 20px; min-width: 220px;
		@media (max-width: @t){min-width: 160px;}
		@media (max-width: @tp){min-width: 140px;}
	}
	.a-btns{margin-top: 5px;}
	.phone-tooltip{
		font-size: 12px; color: #959F90; display: block; padding: 5px 0 0 25px;
		@media (max-width: @m){padding-left: 15px;}
	}
	.btn-step2-submit{margin: 10px 0;}

	.ww-coupons label{position: relative; top: auto; left: auto; margin-bottom: 10px; font-size: 14px; padding: 0 0 0 31px;}
	.ww-coupons-label:before{font-size: 22px; line-height: 15px;}
	.ww-btn-add{
		width: 130px;
		@media (max-width: @t){width: 80px;}
	}
	@media (max-width: @m){
		.wc-coupons{margin-top: 10px;}
	}
	.wp-title{font-size: 12px;}
	.wp{
		border: 0; margin-bottom: 0;
		a{pointer-events: none;}
		@media (max-width: @m){padding-right: 0;}
	}
	@media (max-width: @tp){
		.wp-cnt{padding-right: 0;}
	}
	@media (max-width: @m){
		.wp-total{position: relative; bottom: auto; right: auto;}
	}
	.ww-cart-items{
		border-bottom: 1px solid @borderColor; overflow: auto; max-height: 265px; padding-right: 15px; margin-right: -15px;
		&::-webkit-scrollbar { -webkit-appearance: none; width: 3px; background: #E0E3DF; }
		&::-webkit-scrollbar-thumb {
			background-color: #9FA1A3; border-radius: 50px;
			box-shadow: 0 0 1px rgba(255,255,255,.5);
		}
		@media (max-width: @m){
			margin: 0; padding: 10px 0 0; border: 0; .transition(all);
			.wp{padding-right: 0; padding-left: 0; padding-top: 0;}
			&.active{max-height: 0; padding: 0;}
		}
	}
	.wc-col-totals{
		padding-top: 0;
		@media (max-width: @m){padding: 0 0 20px 0;}
	}

	.w-totals-value{flex-grow: 0;}
	.ww-totals{
		padding: 0;
		&:before{display: none;}
		@media (max-width: @m){
			.w-totals-label{margin-left: 0; padding-right: 5px; flex-grow: 1; max-width: 70%;}
			.w-totals-value{width: auto; flex-grow: 1;}
		}
	}

	.step{
		border-bottom: 1px solid @borderColor; font-weight: bold; font-size: 18px; position: relative;
		a{text-decoration: none; color: #959F90; display: block; padding: 20px 50px;}
		@media (max-width: @t){
			font-size: 16px;
			a{padding-left: 40px; padding-right: 40px;}
		}
		@media (max-width: @tp){
			a{padding: 15px 20px;}
		}
		@media (max-width: @m){
			a{padding: 15px;}
		}
	}
	.completed-step{
		&:after{.pseudo(50px,50px); display: flex; align-items: center; justify-content: center; border-radius: @borderRadius; .gradient-green; top: 10px; right: 10px; .icon-check; font: 15px/15px @fonti; color: #fff;}
		a{color: @textColor; background: #F7F8F6; border-top-left-radius: @borderRadius;}
		@media (max-width: @tp){
			&:after{width: 40px; height: 40px; top: 7px;}
		}
		@media (max-width: @m){
			&:after{width: 26px; height: 26px; font-size: 12px; top: 15px; right: 15px;}
		}
	}
	.last-step{border-bottom: 0;}
	.step3-step-shipping{border-top: 0; border-bottom: 1px solid @borderColor; border-top-left-radius: @borderRadius;}
	.selected-shipping{
		display: block; width: 100%; font-size: 12px; font-weight: normal; line-height: 1.2; padding-top: 5px;
		.shipping_info{display: none!important;}
	}
	.step-selected-shipping{
		display: none;
		a{background: none;}
		.change-data{top: 42px;}
		&:after{top: 36px;}
		@media (max-width: @m){display: block;}
	}
	.change-data{
		color: @green; text-decoration: underline; font-size: 12px; position: absolute; top: 24px; right: 80px; .transition(color);
		&:hover{color: @textColor;}
		@media (max-width: @tp){top: 19px; right: 70px;}
		@media (max-width: @m){right: 53px; font-size: 11px;}
	}
	.field-payment{
		margin-bottom: 25px;
		&>label{display: none;}
		&>span{display: block; margin-bottom: 10px;}
		@media (max-width: @m){
			margin-bottom: 20px;
			input[type=radio]+label{font-size: 14px;}
		}
	}
	.wp-total{line-height: 1.2;}
	.payment_info[data-payment_code=kekspay]{
		position: relative; max-width: none; padding-right: 95px;
		&:after{.pseudo(80px,35px); background: url(images/keks-logo-horizontal.svg) no-repeat left top; background-size: contain; position: absolute; right: 0; bottom: 0;}
	}
	.base-parcel-lockers-btn{text-decoration: underline;}
	.shipping-options{padding-bottom: 25px;}
	.shipping-row{
		position: relative; padding-bottom: 10px; display: block;
		&:first-of-type{
			margin-top: 0; border-top: 0; padding-top: 0;
			.cart_info_total_extra_shipping{top: 4px;}
		}
		.error{padding-left: 0;}
		&.not-available{display: none;}
		&.active{display: block;}
		.cart_info_total_extra_shipping{position: absolute; top: 13px; right: 0; font-weight: bold; padding: 0; color: @textColor; font-size: 12px; display: none; display: none!important;}
		@media (max-width: @m){
			input[type=radio]+label{font-size: 14px;}
		}
	}
	.shipping-data{
		p{padding-bottom: 0;}
	}


	.field-shipping{
		display: block; width: 100%; padding-bottom: 0!important;
		&>span{
			display: block; width: 100%; position: relative; border-top: 1px solid @borderColor; margin-top: 10px; padding-top: 10px;
			&:first-of-type{
				margin-top: 0; border-top: 0; padding-top: 0;
				.cart_info_total_extra_shipping{top: 4px;}
			}
		}
		&>label{display: none!important;}
	}

	.shipping-address{padding: 10px 0 5px;}
	.btn-change-address{margin-top: 5px; display: inline-block;}
	.step2 .btn-change-address{display: none;}
	.shipping-note, .payment_info, .shipping_info{
		display: block!important; font-size: 12px; line-height: 1.3; color: @gray2; /* padding: 0 0 0 30px; */ max-width: 400px;
		a[href^=tel]{color: @gray2;}

	}
	.shipping_info select{
		margin-top: 10px; width: auto; font-size: 14px!important; color: @textColor;
		@media (max-width: @m){font-size: 12px!important;}
	}
	#field-shipping_pickup_location{padding-top: 0; width: 100%;}

	.checkout-field-message{display: none!important;}
	.step2{
		.shipping-address{display: none!important;}
	}
	input[type=radio]:checked ~ .cart_info_total_extra_shipping{color: @green;}
	.shipping-location-address{
		font-size: 13px; color: @textColor; padding: 15px 0 0 0;
		&.active{display: block;}
		p{display: inline; padding: 0;}
		@media (max-width: @m){
			padding: 10px 0 0; font-size: 12px;
			strong{display: none;}
		}
	}
	.ww-cart-btns{
		display: none;
		@media (max-width: @m){display: flex; justify-content: space-between;}
	}
	.btn-toggle-cart{
		font-size: 12px; color: @green; display: inline;
		.s{display: none;}
		&.active{
			.s{display: inline;}
			.h{display: none;}
		}
	}
	.error-wolt{
		padding: 5px 0;
		@media (max-width: @m){padding: 5px 0;}
	}
	.wolt-delivery-checkbox{
		input[type=checkbox]+label, input[type=radio]+label{margin: 10px 0 0!important;}
	}
	.delivery-time-input{display: flex; align-items: center; margin-bottom: 5px; justify-content: space-between; padding-left: 30px;}
	.select-wrapper{
		width: 50%; margin-right: 5px; position: relative;
		select{padding-left: 50px; width: 100%;}
		&:before{.icon-calendar(); color: #809941; display: block; font: 20px/20px @fonti; position: absolute; top: 27px; left: 20px; z-index: 50; pointer-events: none;}
		@media (max-width: @m){
			select{padding-left: 40px;}
			&:before{font-size: 18px; line-height: 18px; top: 24px; left: 15px;}
			&:after{top: 27px;}
		}
	}
	.input-wrapper{
		width: 50%; margin-left: 5px; position: relative;
		input{
			width: 100%!important; margin-top: 10px; padding-left: 50px; background: none;
		}
		&:before{.icon-clock3(); display: block; font: 20px/20px @fonti; color: #809941; position: absolute; top: 27px; left: 20px; z-index: 50; pointer-events: none;}
		&:after{content: ""; width: 12px; height: 12px; position: absolute; right: 15px; top: 29px; display: block; background-image: url(images/icons/arrow-down.svg); background-repeat: no-repeat; background-position: center center; background-size: contain; pointer-events: none;}
		
		@media (max-width: @m){
			input{padding-left: 40px; font-size: 12px;}
			&:before{font-size: 18px; line-height: 18px; top: 24px; left: 15px;}
			&:after{top: 27px;}
		}
	}
	.error-scheduled-time{padding-left: 30px;}
	.cash-amount{
		padding-left: 30px;
		.label-wolt{display: block; padding-bottom: 10px; padding-top: 2px;}
	}
	.input-cash{
		position: relative; display: block;
		input{width: 165px; padding: 0 20px; color: #2B3F21;}
		&:before{content:"€"; font-size: 14px; line-height: 16px; position: absolute; color: #959F90; position: absolute; display: block; top: 19px; left: 136px; z-index: 50;}
		@media (max-width: @m){
			input{width: 125px;}
			&:before{top: 15px; left: 105px;}
		}
	}

	//Datepicker
	.sw-datepicker{
		#ui-datepicker-div{
			left: 0!important; top: 80px!important; z-index: 50!important;
			@media (max-width: @tp){top: 75px!important;}
			@media (max-width: @m){right: 0; left: auto!important;}
		}
	}
	.ui-datepicker{
		width: 205px; background: #FFFFFF; padding: 20px; display: none; max-width: 205px; margin-left: -20px; border-radius: 2px; box-shadow: 0 0 30px 0 rgba(0,0,0,0.15);
		@media (max-width: @tp){margin-left: -30px;}
		@media (max-width: @m){margin-left: -80px;}
	}
	.ui-datepicker:before{content:""; display: block; position: absolute; left: 50%; margin-left: -4px; top: -5px; width: 10px; height: 10px; .rotate(45deg); background: white;}
	.ui-datepicker-title{text-align: center; font-size: 14px; line-height: 16px; font-weight: bold; padding-bottom: 10px;}
	.ui-datepicker-calendar td a.ui-state-hover,
	.ui-datepicker-calendar td span.ui-state-hover,
	.ui-datepicker-calendar td a.ui-state-active,
	.ui-datepicker-calendar td span.ui-state-active{ background: linear-gradient(180deg, #ABC075 0%, #809941 100%); color:white; font-weight:normal; border-radius:4px; box-shadow:0 10px 15px 0 rgba(0,114,229,0.15)}
	.ui-datepicker-calendar td a, .ui-datepicker-calendar td span {color: black;}
	.ui-datepicker-current {display:none;}
	.ui_tpicker_time_label, .ui_tpicker_time{display: none;}
	.ui-datepicker-close{
		width: 100%; height: 44px; margin-top: 10px;
		&:hover{
			&:after{display: none;}
		}
	}
	.ui-timepicker-div{
		position: relative;
		dl{position: relative; height: 64px;}
		dt{width: 50%; top: 0; text-align: center; padding-right: 5px; font-size: 12px; line-height: 13px; color: #959F90; position: absolute;}
		.ui_tpicker_minute_label{right: 0; padding-right: 0; padding-left: 5px;}
		dd{
			width: 77px; height: 44px; position: absolute; bottom: 0;
			&:not(.ui_tpicker_minute):not(.ui_tpicker_hour){display: none;}
			select{height: 44px; width: 100%; padding: 0 20px; margin-top: 0;}
		}
		.ui_tpicker_minute{right: 0;}
	}

	/* .ui-datepicker.active-timepicker{
		display: block!important;
	} */

	.step3-footer{
		align-items: flex-start;
		.wc-col-totals{padding-left: 0; flex-grow: 1;}
		@media (max-width: @t){display: block;}
	}
	&.page-webshop-shipping{
		.free-delivery-container{
			display: none!important;
			@media (max-width: @m){display: block!important; margin-top: 0;}
		}
	}
	.free-delivery-container{
		margin: 0 0 25px; padding-left: 50px; padding-right: 50px; font-size: 12px;
		@media (max-width: @t){padding-left: 40px; padding-right: 40px; margin-bottom: 20px;}
		@media (max-width: @tp){padding-right: 20px; padding-left: 20px;}
		@media (max-width: @m){padding: 0; margin: 15px 0 19px; text-align: left;}
	}
	.free-delivery-title{
		max-width: none;
		@media (max-width: @m){max-width: 100%; text-align: center;}
	}
	.free-delivery-scale{height: 28px;}
	.free-delivery-scale-amount{font-size: 12px; top: 4px;}
	.webshop-accept-terms{
		margin: 0 0 25px 10px; position: relative;
		label{font-weight: bold;}
		a{color: @green;}
		.error{
			position: relative; display: flex; align-items: center; padding: 2px 0 0 30px;
			&:before{.icon-danger(); position: absolute; font: 20px/20px @fonti; color: @red; top: 0; left: 1px;}
		}
	}
	.wc-accept-terms-tooltip{
		background: @darkGreen; color: #fff; height: 30px; padding: 0 13px; position: absolute; font-size: 12px; border-radius: 2px; z-index: 50; display: none; align-items: center; top: -2px; .translate(calc(~"-100% - 15px"));
		&:after{.pseudo(10px,10px); background: @darkGreen; .rotate(45deg); top: 10px; right: -3px;}
		&.active{display: flex;}
		@media (max-width: @t){display: none!important;}
	}
	.webshop-alert-terms{
		background: @red; color: #fff; color: #fff; font-size: 12px; border-radius: @borderRadius; padding: 5px 15px; margin-bottom: 15px; position: relative;
		&:after{.pseudo(10px,10px); background: @red; .rotate(45deg); left: 16px; bottom: -3px;}
	}

	.w-loyalty{
		margin-left: 50px; margin-right: 50px; margin-top: 10px; margin-bottom: 20px; padding-left: 25px; font-size: 14px; line-height: 1.5;
		&:after{display: none;}
		.loyalty-cnt, .loyalty-checkbox{max-width: none;
			@media (max-width: @tp){padding-right: 0;}
		}
		@media (max-width: @t){margin-left: 40px; margin-right: 40px;}
		@media (max-width: @tp){margin-left: 20px; margin-right: 20px;}
		@media (max-width: @m){margin-left: 0; margin-right: 0;}
	}

	.cart-totals-title{text-align: right;}
	.step4{
		.ww-cart-items{
			border: 0; max-height: 350px;
			@media (max-width: @m){
				max-height: 1500px;
				&.active{max-height: 0;}
			}
		}
		.cart-totals{margin-bottom: 18px;}
		.cart-totals-title{display: none;}
		.cart-totals-top{margin-bottom: 0;}
		.wc-col1 .wc-col-cnt{
			padding-bottom: 50px;
			@media (max-width: @m){padding-bottom: 25px; border-bottom: 1px solid @borderColor;}
		}
	}
	.wc-custom-address{
		margin-bottom: 15px; font-size: 14px;
		@media (max-width: @m){font-size: 12px;}
	}
	.wc-custom-address-title{font-size: 15px; font-weight: bold;}
	.wc-step4-col1 .wc-col-cnt{border-bottom: 0;}
	.wc-terms{
		@media (max-width: @m){padding-top: 0;}
	}
	.wp{padding-bottom: 10px;}
	.wp-image{width: 70px;}
	.ct-review{
		position: relative; padding-left: 15px; font-size: 12px; line-height: 1.5; margin-bottom: 15px;
		&:before{.pseudo(3px,auto); left: 0; top: 0; bottom: 0; background: @borderColor;}
	}
	.ct-review-title-btn{color: @green; margin-left: 10px; font-size: 12px;}
	.ct-review-title{font-size: 14px; font-weight: bold;}
	.ct-row{margin-bottom: 0;}
	.cart-totals{font-size: 14px;}
	.priority-order{
		padding-bottom: 10px;
		span{padding-bottom: 4px;}
	}
	.priority-order-col-cnt{padding-top: 0; padding-bottom: 0;}

	/*------- credit card payment -------*/
	.cc_expire input, .cc_cvv input{padding: 0 5px; text-align: center;}
	.cc_installments{max-width: 170px;
		.chzn-container-single .chzn-single span{color: #000;}
	}
	.cc_type{position: absolute; padding-left: 10px !important;}

	.webshop_widget_payment_advanced{
		position: relative; padding-top: 6px;
		#field-error-cc{
			left: 100px;
			&:after{left: 7px;}
		}
		label{width: 100% !important; display: block !important; font-size: 16px !important;}
		div{padding-bottom: 8px; position: relative;}
		.error{left: 130px; right: auto;}
		.clear{padding: 0;}
		#field-cc_cvv{margin-right: 8px;}
		a{color: @textColor;}
		input[type=text]{
			&:hover, &:focus{background: #fff; color: #000;}
		}
	}

	/*------- /credit card payment -------*/
}