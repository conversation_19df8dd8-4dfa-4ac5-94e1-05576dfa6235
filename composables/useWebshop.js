export function useWebshop() {
	const endpoints = useEndpoints();
	const {getAppUrl} = useApiRoutes();
	const {relative} = useUrl();
	const cart = useState('cart', () => null);
	const order = useState('order', () => null);
	const customer = useState('customer', () => null);

	// payment gateway specific data
	const pg = useState('pg', () => {
		return {
			form: null,
		};
	});

	// user abandonment data
	async function fetchAbandonment(fetchOptions) {
		return await useApi(`${endpoints.get('_post_hapi_webshop_abandonment')}`, {
			method: 'POST',
			body: fetchOptions,
		});
	}

	// fetch cart
	async function fetchCart(options) {
		const res = await useApi(`${endpoints.get('_get_hapi_webshop_cart_full')}`, null, {appendLang: true});
		cart.value = res?.data;
		return options?.fullResponse ? res : res?.data;
	}

	// get cart data
	function getCartData(data) {
		if (data == 'products') {
			let cartItems = [];
			if (cart.value.parcels?.length) {
				cart.value.parcels.forEach(parcel => {
					cartItems.push(...parcel.items);
				});
			}
			return cartItems;
		}
		if (data == 'customer') return cart.value?.customer;
		return cart.value;
	}

	// check if cart is empty
	async function fetchIsCartEmpty(fetchOptions = null, options) {
		return await useApi(`${endpoints.get('_get_hapi_webshop_is_cart_empty')}`, fetchOptions, {appendLang: true, ...options});
	}

	// add to cart
	async function addProduct(options) {
		// add product to cart
		const res = await useApi(`${endpoints.get('_post_hapi_webshop_product')}`, {
			method: 'POST',
			body: options,
		});

		// once the product is added, refresh cart data
		await fetchCart();

		return res;
	}

	// update cart product
	async function updateProduct(options) {
		// add product to cart
		const productResponse = await useApi(`${endpoints.get('_post_hapi_webshop_product')}`, {
			method: 'PUT',
			body: options,
		});

		// once the product is updated, refresh cart data
		const cartResponse = await fetchCart();

		return {productResponse, cartResponse};
	}

	// remove product from cart
	async function removeProduct(options, preventRefresh) {
		// add product to cart
		const removeResponse = await useApi(`${endpoints.get('_post_hapi_webshop_product')}`, {
			method: 'DELETE',
			body: options,
		});

		// once the product is removed, refresh cart data
		if (preventRefresh) {
			return removeResponse;
		}

		const cartResponse = await fetchCart();
		return {removeResponse, cartResponse};
	}

	// fetch all coupons
	async function fetchActiveCoupons() {
		return await useApi(`${endpoints.get('_get_hapi_webshop_coupon')}`, null, {appendLang: true});
	}

	// add coupon
	async function addCoupon(options) {
		const res = await useApi(`${endpoints.get('_post_hapi_webshop_coupon')}`, {
			method: 'POST',
			body: options,
		});

		// fetch cart data if coupon is valid
		if (res.data?.label_name != 'error_coupon_invalid_code') {
			await fetchCart();
		}

		return res;
	}

	// remove coupon
	async function removeCoupon(options) {
		const res = await useApi(`${endpoints.get('_delete_hapi_webshop_coupon')}`, {
			method: 'DELETE',
			body: options,
		});

		await fetchCart();
		return res;
	}

	// fetch locations
	async function fetchLocations(options) {
		const urlParams = Object.keys(options).length ? '?' + new URLSearchParams(options).toString() : '';
		return await useApi(`${endpoints.get('_get_hapi_webshop_location')}${urlParams}`, null, {appendLang: true});
	}

	async function fetchCustomer() {
		const res = await useApi(`${endpoints.get('_get_hapi_customer')}`);
		customer.value = res?.data;
		return customer.value;
	}

	async function fetchCompany(options) {
		return await useApi(`${endpoints.get('_get_hapi_customer')}`, {
			method: 'POST',
			body: options,
		});
	}

	async function submitCustomerData(fetchOptions, options) {
		let type = options?.type || 'webshop.customer';

		// if type is set to false, don't include it in the request
		if (typeof options?.type == 'boolean' && options.type == false) {
			type = '';
		}

		if (type) {
			type = `?type=${type}`;
		}
		const res = await useApi(`${endpoints.get('_post_hapi_customer')}${type}`, {
			method: 'POST',
			body: fetchOptions,
		});

		if (options?.fetchCart != false) {
			await fetchCart();
		}
		return res;
	}

	async function getCustomerData() {
		return customer.value;
	}

	async function setParcelDefaultShipping() {
		const res = await useApi(`${endpoints.get('_post_hapi_webshop_parcel_default_shipping')}`, {
			method: 'POST',
		});
		await fetchCart();
		return res;
	}

	async function updateShipping(fetchOptions, options) {
		const res = await useApi(`${endpoints.get('_put_hapi_webshop_parcel_shipping')}`, {
			method: 'PUT',
			body: fetchOptions,
		});

		// once the shipping is updated, refresh cart data if fetchCart is true
		if (options?.fetchCart != false) {
			await fetchCart();
		}

		return res;
	}

	async function updatePayment(options) {
		const res = await useApi(`${endpoints.get('_post_hapi_customer')}`, {
			method: 'POST',
			body: options,
		});

		// once the payment is updated, refresh cart data
		await fetchCart();

		return res;
	}

	async function continueAsGuest(options) {
		const fetchOptions = {
			continue_as_guest: true,
			...options,
		};

		return await useApi(`${endpoints.get('_post_hapi_customer')}?type=webshop.guest`, {
			method: 'POST',
			body: fetchOptions,
		});
	}

	async function createOrder(options) {
		const res = await useApi(`${endpoints.get('_post_hapi_webshop_order')}`, {
			method: 'POST',
			body: options,
		});

		return res;
	}

	async function fetchOrder(options) {
		if (!options?.code) return useLog('fetchOrder Error: Order code is required', 'error');
		const orderEndpoint = endpoints.get('_get_hapi_webshop_order').replace('%ID%-%CODE%', options?.code);
		const newPayment = options?.newPayment ? '?new_payment=1' : '';
		const res = await useApi(orderEndpoint + newPayment, null, {appendLang: true});
		if (res?.success) {
			order.value = res.data;
		}
		return res;
	}

	async function fetchOrderStatus(options) {
		if (!options?.code) return useLog('fetchOrderStatus Error: Order code is required', 'error');
		const orderEndpoint = endpoints.get('_get_hapi_webshop_order_status').replace('%ID%-%CODE%', options?.code);
		return await useApi(orderEndpoint, null, {appendLang: true});
	}

	async function createPayment(options) {
		const orderEndpoint = endpoints.get('_get_hapi_webshop_create_payment').replace('%ID%-%CODE%', options?.code);
		return await useApi(orderEndpoint, null, {appendLang: true});
	}

	/* 
		Checkout order logic after order is created. Redirect user to right page based on payment type
		handleOrder({
			order:Object, // order data
			orderId:String, // order id
			failedCallback: () => { 
				// callback function if order failed
			}
		})
	*/
	async function handleOrder(options) {
		let orderData = null;

		// if order failed, redirect to failed order page
		if (!options?.order?.success) {
			order.value = null;
			await fetchCart();
			await navigateTo(getAppUrl('webshop_failed_order'));
			if (options?.failedCallback) options.failedCallback();
			return;
		}

		// if order was successful, fetch full order data by id_code
		if (options?.order?.success && options?.orderId) {
			await fetchCart();
			orderData = await fetchOrder({code: options?.orderId});
		}

		// simple cash payments. Redirect to thank you page
		if (orderData?.data?.cart?.payment_type == null && options?.orderId) {
			await navigateTo({
				path: getAppUrl('webshop_thank_you'),
				query: {order_identificator: options?.orderId},
			});
		}

		// payment gateway/credit card processing
		if (orderData?.data?.cart?.payment_type == 'redirect' && options?.orderId) {
			await handlePg({orderId: options?.orderId});
		}

		// Redirect user to PaymentCreate template and handle payment (KEKS Pay)
		if (orderData?.data?.cart?.payment_type == 'direct_credit_cart_form' && options?.orderId) {
			if (orderData.data.redirect_url_on_created) {
				await navigateTo(relative(orderData.data.redirect_url_on_created));
			} else {
				console.error('redirect response property is missing');
			}
		}

		// when credit card form is used on custom template and not on the payment gateway page
		if (orderData?.data?.cart?.payment_type == 'credit_cart_form' && options?.orderId) {
			await navigateTo({
				path: apiRoutes.getAppUrl('webshop_payment_create'),
				query: {order_identificator: options?.orderId},
			});
		}
	}

	/* 
		Handle payment gateway / credit card payment
		handlePg({
			orderId:String, // order id
		})
	*/
	async function handlePg(options) {
		const payment = await createPayment({code: options.orderId});

		// if payment failed, redirect to failed order page
		if (!payment.success) {
			return navigateTo(getAppUrl('webshop_failed_order'));
		}

		// redirect to page where hidden form will be displayed and auto-submitted. (PaymentFormAutosubmit.vue)
		if (payment.data?.hidden_form) {
			pg.value.form = payment.data;
			return navigateTo(getAppUrl('webshop_payment_form_autosubmit'));
		}

		// redirect to external payment gateway page
		const redirectUrl = payment.data.redirect_url;
		if (redirectUrl) {
			return (window.location.href = redirectUrl);
		}

		// if payment failed, redirect to new retry payment page (PaymentNew.vue)
		if (payment?.data?.label_name === 'error_empty_payment_form') {
			return navigateTo({
				path: getAppUrl('webshop_payment_new'),
				query: {order_identificator: options.orderId},
			});
		}
	}

	return {
		cart,
		getCartData,
		fetchIsCartEmpty,
		fetchAbandonment,
		fetchCart,
		addProduct,
		updateProduct,
		removeProduct,
		fetchActiveCoupons,
		addCoupon,
		removeCoupon,
		fetchLocations,
		fetchCompany,
		fetchCustomer,
		submitCustomerData,
		getCustomerData,
		setParcelDefaultShipping,
		updateShipping,
		updatePayment,
		continueAsGuest,
		createOrder,
		fetchOrder,
		fetchOrderStatus,
		order,
		createPayment,
		handleOrder,
		pg,
	};
}
