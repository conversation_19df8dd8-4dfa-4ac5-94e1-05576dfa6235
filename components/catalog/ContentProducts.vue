<template>
	<template v-if="props.item?.featured_products?.length">
		<div class="pd-content-products">
			<BaseCmsLabel code="standout_offer" class="pd-content-products-title" tag="h2" />
			<CatalogIndexEntrySmall v-for="product in props.item.featured_products" :key="product.id" :item="product" mode="list" list="Blog" />
		</div>
	</template>
</template>

<script setup>
	const props = defineProps({
		item: Object
	});
</script>

<style scoped lang="less">
	.pd-content-products{padding: 20px 0;}
	.pd-content-products-title{padding-top: 0; padding-bottom: 16px; font-size: 26px; line-height: 1.2;}
</style>
