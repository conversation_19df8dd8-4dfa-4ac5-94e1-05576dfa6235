<template>
	<article class="wp" :class="[{'unavailable': !item.is_available}, {'wp-auth-wishlist': mode == 'auth-wishlist'}]">
		<div class="wp-image">
			<figure>
				<NuxtLink :to="item.url_without_domain" target="_parent"><BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width70-height70-crop1']" default="/images/no-image-50.jpg" :title="item.main_image_title" :alt="item.main_image_description || item.title" /></NuxtLink>
				<!-- <img <?php echo Thumb::generate($item['main_image'], array('width' => 70, 'height' => 70, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?><?php if(!empty($item['main_image_title'])): ?> title="<?php echo Text::meta($item['main_image_title']); ?>"<?php endif; ?> alt="<?php echo ($item['main_image_description']) ? Text::meta($item['main_image_description']) : $item['title']; ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>" /> -->
			</figure>
		</div>

		<div class="wp-content">
			<div class="wp-cnt">
				<div class="cp-title">
					<NuxtLink :to="item.url_without_domain" target="_parent">{{ item.title }}</NuxtLink>
				</div>
				<template v-if="mode == 'auth-wishlist' || mode == 'cart'">
					<div v-if="item?.product_code" class="cp-code"><BaseCmsLabel code="code" />: {{item.product_code}}</div>
				</template>
				<template v-else>
					<div v-if="item?.code" class="cp-code"><BaseCmsLabel code="code" />: {{item.code}}</div>
				</template>

				<div class="wp-btns">
					<template v-if="mode == 'cart'">
						<BaseCatalogSetWishlist v-slot="{active, onToggleWishlist, loading}" :item="item">
							<div
								class="wp-btn"
								:class="[{
									'wp-btn-add': !active,
									'wp-btn-remove': active || mode == 'cart',
									'loading': loading
								}]"
								@click="onToggleWishlist">
								<span>
									<BaseCmsLabel v-if="!active" code="add_to_wishlist" />
									<template v-else>
										<BaseCmsLabel v-if="mode && mode === 'wishlist'" code="remove_from_wishlist" />
										<BaseCmsLabel v-else code="remove_from_wishlist" />
									</template>
								</span>
								<UiLoader class="small" v-if="loading" />
							</div>
						</BaseCatalogSetWishlist>
						<!-- <?php if (!empty($item['wishlist_widget'])): ?>
							<a class="wp-btn wp-btn-delete wishlist_set_<?php echo $content; ?> wishlist_active" href="javascript:cmswishlist.set('<?php echo $content; ?>', 'remove', '', 1, '_tracking:remove');">
								<?php echo Arr::get($cmslabel, 'remove_from_wishlist'); ?>
							</a>
						<?php endif; ?> -->
					</template>

					<template v-if="item.is_available">
						<BaseWebshopAddToCart :data="{modalData: item, shopping_cart_code: item.shopping_cart_code, quantity: item.qty}" v-slot="{onAddToCart, loading}">
							<button :class="['wp-btn wp-btn-delete wp-btn-add',{loading: loading,active: cartItems.some(cartItem => cartItem.id === item.product_id)}]" @click="onAddToCart">
								<UiLoader size="small" v-if="loading" />
								<template v-if="!cartItems.some(cartItem => cartItem.id === item.product_id)">
									<BaseCmsLabel code="move_to_cart" tag="span" />
								</template>
								<template v-else>
									<BaseCmsLabel code="product_in_cart" tag="span" />
								</template>
							</button>
						</BaseWebshopAddToCart>
					</template>
				</div>

				<!-- <template v-if="item.is_available">
					<?php $product_in_cart = Webshop::get_product_status($item['shopping_cart_code'], $shopping_cart_status); ?>
					<?php if($item['is_available']): ?>
						<a class="wp-btn wp-btn-delete wp-btn-add<?php if($product_in_cart): ?> active<?php endif; ?>" data-shoppingcart_product_active="<?php echo $item['shopping_cart_code']; ?>" title="<?php echo Arr::get($cmslabel, 'add_to_cart'); ?>"  href="javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:index', 'simple_loader', 'input', 1)">
							<span class="a"><?php echo Arr::get($cmslabel, 'move_to_cart'); ?></span>
							<span class="b"><?php echo Arr::get($cmslabel, 'product_in_cart'); ?></span>
						</a>
					<?php endif; ?> 
				</template>-->
			</div>

			<!-- FIXME @EP - vidjeti dok deni riješi svoje fixmeje iz CatalogIndexEntrya pa da vidimo šta treba i ovdje promijeniti -->
			<div class="wp-total">
				<template v-if="b2b">
					<div class="wp-current-price"><BaseUtilsFormatCurrency :price="item.price_b2b_custom" /></div>
				</template>
				<template v-else>
					<template v-if="hasLoyalty && item.loyalty_price < item.basic_price && item.type != 'coupon'">
						<div class="wp-old-price">
							<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
						</div>
						<div class="wp-current-price red"><BaseUtilsFormatCurrency :price="item.loyalty_price" /></div>
						<template v-if="item.extra_price_lowest > 0">
							<div class="wp-lowest-price"><BaseCmsLabel code="lowest_price" />: <BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></div>
						</template>
					</template>
					<template v-else>
						<template v-if="item.price_custom > 0">
							<template v-if="item.discount_percent_custom > 0">
								<div class="wp-old-price">
									<span><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></span>
								</div>
								<div class="wp-current-price red"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
								<div class="wp-lowest-price">
									<template v-if="item.extra_price_lowest > 0">
										<div class="wp-lowest-price"><BaseCmsLabel code="lowest_price" />: <BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></div>
									</template>
								</div>
							</template>
							<template v-else>
								<div class="wp-current-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>
								<div class="wp-lowest-price"></div>
							</template>
						</template>
					</template>
				</template>

				<!-- <?php if (!empty($info['user_b2b'])): ?>
						<?php if ($item['discount_percent_b2b_custom'] > 0): ?>
							<div class="wp-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>">
								<?php echo Utils::currency_format($item['basic_price_b2b_custom'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['basic_price_b2b_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
							<div class="wp-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
								<?php echo Utils::currency_format($item['price_b2b_custom'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['price_b2b_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
							<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
								<div class="wp-lowest-price">
									<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
									<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
									<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								</div>
							<?php endif; ?>
						<?php else: ?>
							<div class="wp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
								<?php echo Utils::currency_format($item['price_b2b_custom'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['price_b2b_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
						<?php endif; ?>
					<?php else: ?>
						<?php if($is_loyalty AND $item['loyalty_price'] < $item['basic_price']): ?>
							<div class="wp-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>">
								<span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
								<?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
							<div class="wp-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
								<?php echo Utils::currency_format($item['loyalty_price'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['loyalty_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
							<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
								<div class="wp-lowest-price">
									<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
									<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
									<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								</div>
							<?php endif; ?>
						<?php elseif ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price']): ?>
							<div class="wp-old-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>">
								<span><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
								<?php echo Utils::get_second_pricetag_string($item['basic_price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
							<div class="wp-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
								<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
							<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
								<div class="wp-lowest-price">
									<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
									<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
									<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								</div>
							<?php endif; ?>
						<?php else: ?>
							<div class="wp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
								<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</div>
						<?php endif; ?>
					<?php endif; ?> -->
			</div>
		</div>
	</article>
</template>

<script setup>
	const {b2b, hasLoyalty} = useProfile();
	const props = defineProps({
		item: Object,
		mode: String,
		list: String,
		extraclass: String,
		itemListId: String,
		itemListName: String,
		cartItems: Object,
	});

	//console.log(isLoggedIn.value);
	//const b2b = computed(() => isLoggedIn && user.value.b2b === '1')
</script>

<style lang="less" scoped></style>
