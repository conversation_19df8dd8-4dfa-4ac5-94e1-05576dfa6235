<template>
	<slot :loading="loading" :onUpdate="onUpdate" :onReset="onReset" :onDecrement="decrement" :onIncrement="increment" :status="status" :quantity="quantity" />
</template>

<script setup>
	const model = defineModel();
	const webshop = useWebshop();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	const config = useAppConfig();
	const props = defineProps({
		item: Object,
		quantity: {
			type: [Number, String],
			default: 1,
		},
		limit: {
			type: [Number, String],
			default: 0,
		},
		mode: String,
		steps: {
			type: Number,
			default: 1,
		},
	});
	const emit = defineEmits(['update']);

	// Ensure initial quantity is not less than minimum value (steps or 1)
	const minimumValue = Math.max(props.steps, 1);
	const initialQuantity = Math.max(props.quantity, minimumValue);
	const quantity = ref(initialQuantity);
	const loading = ref(false);
	const status = ref('');
	const {events} = inject('baseWebshopCartData', {events: null});
	model.value = initialQuantity;

	let typingTimer;
	let prevQuantity = props.quantity;

	// This runs on every keyup event
	async function onUpdate(event) {
		const inputValue = event.target.value;
		if (inputValue === '') {
			return;
		}

		// Debounce updateQty and step validation to allow complete input
		clearTimeout(typingTimer);
		typingTimer = setTimeout(async () => {
			// Re-read value from input target at the moment of execution, as it might have changed
			let currentValFromInput = cleanAndParseNumber(event.target.value);
			quantity.value = currentValFromInput;
			// Ensure input field reflects parsed value, in case non-numeric chars were entered during debounce
			if (event.target.value !== quantity.value.toString()) {
				event.target.value = quantity.value;
			}
			checkLimit(quantity.value);

			// Apply step validation if steps are defined
			if (props.steps > 1) {
				validateStepRule(event); // Modifies quantity.value and event.target.value
			}

			// If in cart mode, call updateQty
			if (props.mode == 'cart' && props.item) {
				await updateQty(); // Modifies quantity.value based on API response
			}

			// After all operations, ensure model and emit reflect the final quantity
			model.value = quantity.value;
			emit('update', quantity.value);
		}, 500);
	}

	function onReset(event) {
		if (event.target.value === '') {
			event.target.value = quantity.value;
			quantity.value = quantity.value;
			model.value = quantity.value;
			emit('update', quantity.value);
		}
	}

	// Validate and adjust input value to follow steps rule
	function validateStepRule(event) {
		const currentValue = quantity.value;

		// Calculate the closest valid step value
		const remainder = currentValue % props.steps;
		let adjustedValue;

		if (remainder === 0) {
			// Value is already a valid step
			adjustedValue = currentValue;
		} else {
			// Round to the nearest valid step
			const lowerStep = currentValue - remainder;
			const upperStep = lowerStep + props.steps;

			// Choose the closer step, but ensure it's not below minimum
			const minimumValue = Math.max(props.steps, 1);
			adjustedValue = remainder < props.steps / 2 && lowerStep >= minimumValue ? lowerStep : upperStep;
		}

		// Ensure adjusted value respects limits
		if (props.limit && adjustedValue > +props.limit) {
			// Find the highest valid step that doesn't exceed the limit
			adjustedValue = Math.floor(+props.limit / props.steps) * props.steps;
		}

		// Ensure minimum step value
		adjustedValue = Math.max(adjustedValue, Math.max(props.steps, 1));

		// Update values if adjustment was needed
		if (adjustedValue !== currentValue) {
			quantity.value = adjustedValue;
			event.target.value = adjustedValue;
		}
	}

	function checkLimit(value) {
		if (value <= 0) quantity.value = Math.max(props.steps, 1);

		if (props.mode == 'cart') return;
		if (props.limit && value > +props.limit) quantity.value = +props.limit;
		if (value < Math.max(props.steps, 1)) quantity.value = Math.max(props.steps, 1);
		emit('update', quantity.value);
		model.value = quantity.value;
	}

	// watch if quantity prop has changed and update related values
	watch(
		() => props.quantity,
		async () => {
			// Set quantity to the new prop value, but ensure it's not below minimum
			const minimumValue = Math.max(props.steps, 1);
			const newQuantity = Math.max(props.quantity, minimumValue);
			quantity.value = newQuantity;
			prevQuantity = props.quantity; // Keep original prop value for tracking changes
			model.value = newQuantity;
		}
	);

	// update quantity limit when switching between variations
	watch(
		() => props.limit,
		() => checkLimit(quantity.value)
	);

	// clean input from non-numeric characters
	function cleanAndParseNumber(input) {
		const cleanedInput = input
			.toString()
			.replace(/^-|[^0-9]+/g, '')
			.replace(/^0+/, '');
		return parseInt(cleanedInput, 10) || 1;
	}

	let updateTimer;
	async function updateQty() {
		// Clear status message and timer before updating quantity
		status.value = '';
		clearTimeout(updateTimer);

		// update only if quantity has changed
		if (quantity.value == prevQuantity) return;

		loading.value = true;

		// update quantity
		const res = await webshop.updateProduct([
			{
				shopping_cart_code: props.item.shopping_cart_code,
				quantity: quantity.value,
			},
		]);

		if (res?.productResponse?.data?.labels_name || res?.productResponse?.data?.label_name) {
			// If qty reached the limit, reset to max available quantity. Multiple conditions to cover different HAPI responses
			if (res.productResponse?.data?.labels_name?.[props.item.shopping_cart_code] == 'error_limitqty') {
				quantity.value = res.productResponse.data.available_qty;
			}
			if (res.productResponse?.data?.label_name == 'error_product_maximum_qty') {
				quantity.value = res.productResponse.data.maximum_qty;
			}

			// Show status message for 4 seconds
			status.value = res?.productResponse?.data?.labels_name?.[props.item.shopping_cart_code] || res?.productResponse?.data?.label_name;
			updateTimer = setTimeout(() => (status.value = ''), 4000);
		}

		// gtm tracking
		if (res?.productResponse?.success && gtm) {
			let qty = res.productResponse?.data?.new_qty;
			if (qty < 0) {
				gtm.gtmTrack('removeProduct', {items: props.item, quantity: qty * -1});
			} else {
				gtm.gtmTrack('addProduct', {items: props.item, quantity: qty});
			}
		}

		events.value = {event: 'updateProduct', quantity: quantity.value, prevQuantity: prevQuantity, product: props.item, ...res};
		loading.value = false;
		prevQuantity = quantity.value;
	}

	// Increase by steps amount, but check if limit is reached
	async function increment() {
		const newValue = quantity.value + props.steps;
		if (props.limit && newValue > +props.limit) {
			// If limit is reached, do not increase any more
			return quantity.value;
		}
		quantity.value = newValue;
		checkLimit(quantity.value);
		if (props.mode == 'cart' && props.item) {
			clearTimeout(typingTimer);
			typingTimer = setTimeout(updateQty, 500);
			return;
		}
		emit('update', quantity.value);
		model.value = quantity.value;
		return quantity.value;
	}

	// Decrease by steps amount, but ensure minimum is not below step value
	async function decrement() {
		const newValue = quantity.value - props.steps;
		const minimumValue = Math.max(props.steps, 1);
		if (newValue < minimumValue) {
			// Minimum cannot go below defined step or 1
			return quantity.value;
		}
		quantity.value = newValue;
		checkLimit(quantity.value);
		if (props.mode == 'cart' && props.item) {
			clearTimeout(typingTimer);
			typingTimer = setTimeout(updateQty, 500);
			return;
		}
		emit('update', quantity.value);
		model.value = quantity.value;
		return quantity.value;
	}
</script>
