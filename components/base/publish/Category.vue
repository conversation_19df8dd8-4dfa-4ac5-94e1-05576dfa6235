<template>
	<BaseMetaSeo :data="category" v-if="seo" />
	<slot :item="category" :contentType="contentType" :rootCategory="rootCategory" :parentCategory="parentCategory" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const emit = defineEmits(['data']);
	const publish = usePublish();
	const {generateThumbs} = useImages();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	let hapi;
	if (__HAPI_TRACKING__) hapi = useHapiTracking();

	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();
	const config = useAppConfig();
	const {getLastUrlSegment} = useUrl();
	const {getLastArrayElement} = useArrayUtils();
	const props = defineProps({
		fetch: Object,
		seo: Boolean,
		rootCategory: {
			type: Boolean,
			default: false,
		},
		parentCategory: {
			type: Boolean,
			default: false,
		},
		includeSubcategories: {
			type: Boolean,
			default: false,
		},
		thumbPreset: String,
		dataKey: String,
		gtmTracking: {
			type: Boolean,
			default: true,
		},
		fbTracking: {
			type: Boolean,
			default: true,
		},
		log: {
			type: Boolean,
			default: false,
		},
	});

	const category = ref({});
	const fetchConfig = {};
	if (props.dataKey) {
		fetchConfig.dataKey = props.dataKey;
	}

	// get global content type
	const contentType = computed(() => {
		const globalCt = nuxtApp.$appGlobalData.contentType;
		const globalAction = nuxtApp.$appGlobalData.action;

		// preventing template "flash" when switching from search to category and product details
		if (route.query?.search_q) return 'search';
		if (globalAction == 'detail') return 'detail';

		return globalCt;
	});

	if (!props.fetch) {
		if (route.meta.contentType == 'tag') {
			const {fetchTags} = useTag();
			const lastUrlSegment = getLastUrlSegment(route.path);
			const tagId = getLastArrayElement(lastUrlSegment.split('-'));

			const res = await fetchTags({module: 'publish', id: tagId, single: true});
			const tagData = res?.data?.length ? res.data[0] : null;
			category.value = {
				...tagData,
				seo_h1: tagData?.title,
			};
		} else {
			category.value = publish.getCategory();
		}
		if (props.gtmTracking && category.value?.title && gtm) gtm.gtmTrack('pageView', {url: route.fullPath, title: category.value.title});
		if (props.fbTracking && category.value?.title && fbCapi) fbCapi.sendEvent('pageView', {title: category.value.title});
	} else {
		category.value = await publish.fetchCategories(props.fetch, fetchConfig).then(res => {
			return res?.data?.length ? res.data[0] : null;
		});
	}

	// generate category thumbs
	if (props.thumbPreset) {
		await generateThumbs({
			data: category.value,
			preset: props.thumbPreset,
			dataKey: props.dataKey ? props.dataKey + '-thumbs' : null,
		});
	}

	// root category
	const rootCategory = ref();
	if (props.rootCategory) {
		const rootCategoryPosition = category.value?.position_h ? category.value.position_h.split('.')[0] : null;
		if (rootCategoryPosition) {
			let fetchOptions = {
				position: rootCategoryPosition,
				mode: 'full',
			};
			if (props.includeSubcategories) {
				fetchOptions = {
					start_position_with: rootCategoryPosition,
					hierarhy_by_position: true,
					mode: 'full',
				};
			}
			const rootCategories = await publish.fetchCategories(fetchOptions);
			rootCategory.value = rootCategories?.data?.length ? rootCategories.data[0] : null;
		}
	}

	// parent category
	const parentCategory = ref();
	if (props.parentCategory) {
		let parentCategoryPosition = category.value?.position_h ? category.value.position_h.split('.') : [];
		if (parentCategoryPosition?.length > 1) {
			parentCategoryPosition.pop();

			let parentFetchOptions = {
				position: parentCategoryPosition[0],
				mode: 'full',
			};
			if (props.includeSubcategories) {
				parentFetchOptions = {
					start_position_with: parentCategoryPosition[0],
					hierarhy_by_position: true,
					mode: 'full',
				};
			}
			const parent = await publish.fetchCategories(parentFetchOptions);
			parentCategory.value = parent?.data?.length ? parent.data[0] : null;
		}
	}

	onMounted(() => {
		if (category.value?.id && hapi) hapi.sendEvent('publishcategory', category.value.id);
		if (props.log) console.log('category', category.value);
	});

	// emit data
	emit('data', {category, rootCategory, parentCategory});

	// provide data to child components
	provide('basePublishCategoryData', {category, contentType});
</script>
