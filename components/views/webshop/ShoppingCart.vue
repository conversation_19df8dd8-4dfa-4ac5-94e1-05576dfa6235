<template>
	<BaseCmsPage>
		<Body class="page-cart white-bg" :class="['cart-layout-page']" />
		<ClientOnly>
			<BaseWebshopCart v-slot="{parcels, urls, cart, onFinishShopping}" :remarketing="true">
				<!-- <?php
					if (!empty($products)) {
						$products = Arr::merge($products, $products_status);
						$pickup_products = array_filter(array_map(function ($element) {
							return (in_array($element['type'], Kohana::config('app.catalog.product_type.pickup_only'))) ? $element['shopping_cart_code'] : NULL;
						}, $products));

						$customer_data = Utils::data('customer_data');

						if (!empty($products_status)) {
							$available_for_pickup_products = array_filter(array_map(function ($element) use ($customer_data) {
								return (in_array($element['type'], Kohana::config('app.catalog.product_type.pickup_only')) and in_array(Arr::get($customer_data, 'shipping_pickup_location', 0), array_keys($element['warehouses']))) ? $element : NULL;
							}, $products));

							$not_available_for_pickup_products = array_filter(array_map(function ($element) use ($customer_data) {
								return (in_array($element['type'], Kohana::config('app.catalog.product_type.pickup_only')) and !in_array(Arr::get($customer_data, 'shipping_pickup_location', 0), array_keys($element['warehouses']))) ? $element : NULL;
							}, $products));
						}
					}
				?>

				<?php
					$shopping_cart_items = Webshop::shopping_cart_items();
					$cart_mutual_warehouses = [];
					$cart_product_types = [];
					if (!empty($shopping_cart_items)) {
						$product_ids = [];
						foreach ($shopping_cart_items as $item_sc_code => $qty) {
							list($product_id, $variation_id) = explode('_', $item_sc_code);
							array_push($product_ids, $product_id);
						}

						$item_warehouses = ($product_ids)
							? DB::select_array(['id', 'warehouses_ids', 'type'])
								->from('catalog_products')
								->where('id', 'IN', $product_ids)
								->execute()
								->as_array('id')
							: [];


						if (!empty($item_warehouses)) {
							$warehouses_data = [];
							foreach ($item_warehouses as $item_id => $item_warehouse) {
								array_push($cart_product_types, Arr::get($item_warehouse, 'type', ''));
								$data = Text::db_to_array(Arr::get($item_warehouse, 'warehouses_ids', ''));
								$item_cart_qty = Arr::get($shopping_cart_items, "{$item_id}_0", 0);

								if (!empty($data)) {
									foreach ($data as $item_warehouse_data) {
										list($warehouse_id, $warehouse_qty) = explode('=', $item_warehouse_data);
										if ($warehouse_qty >= $item_cart_qty) {
											$warehouses_data[$item_id][$warehouse_id] = $warehouse_qty;
										}
									}
								}
							}

							$cart_mutual_warehouses = array_reduce($warehouses_data, function ($carry, $item) {
								if (is_null($carry)) {
									return array_keys($item);
								}
								return array_intersect($carry, array_keys($item));
							}, null);
						}
					}
				?> -->
				<BaseWebshopFreeShipping v-slot="{item: toFree}">
					<div class="wrapper" v-if="parcels?.length">
						<template v-if="parcels[0]?.items?.length">
							<div class="df w-row" id="view_cart">
								<div class="w-col w-col1">
									<div class="w-col1-cnt">
										<BaseWebshopCartErrors v-slot="{errorsItems, warningsItems}">
											<template v-if="errorsItems?.length">
												<div class="global-error" v-for="error in errorsItems" :key="error"><BaseCmsLabel :code="error.label_name" /></div>
											</template>
											<template v-if="warningsItems?.length">
												<div class="global-warning" v-for="warning in warningsItems" :key="warning"><BaseCmsLabel :code="warning.label_name" /></div>
											</template>
										</BaseWebshopCartErrors>

										<!-- <?php if (!empty($pickup_products)) : ?>
											<div class="wp-pickup-products">
												<div class="status-info-label"><?php echo Arr::get($cmslabel, 'pickup_info'); ?></div>
												<div class="wp-pickup-products-info"><?php echo Arr::get($cmslabel, 'cart_availability_onlyinoffice'); ?></div>
												<?php $locations = Widget_Location::points(['lang' => $info['lang'], 'available_pickup' => TRUE]); ?>
												<?php if (!empty($locations) and empty($shipping['parcel_locker'])): ?>
													<span class="wp-pickup-select-label">
														<?php echo Arr::get($cmslabel, 'select_store'); ?>
													</span>
													<select data-shipping_pickup_location="1" id="field-shipping_pickup_location" name="shipping_pickup_location">
														<option value=""><?php echo Arr::get($cmslabel, 'select_store_label'); ?></option>
														<?php foreach ($locations as $location): ?>
															<option <?php if (!empty($customer_data['shipping_pickup_location']) AND $customer_data['shipping_pickup_location'] == $location['id']) : ?>selected<?php endif; ?> value="<?php echo $location['id']; ?>"> <?php echo strip_tags($location['title']); ?></option>
														<?php endforeach; ?>
													</select>
												<?php endif; ?>
											</div>
										<?php endif; ?> -->

										<!-- <?php
											// Provjeri da li je kupac B2b bez obzira na to da li je prijavljen ili ne
											$client_email = '';
											$client_is_b2b = false;

											if (!empty($info['user_email'])) {
												$client_email = $info['user_email'];
											} elseif (!empty($user->email)) {
												$client_email = $user->email;
											} elseif (!empty($customer_data['email'])) {
												$client_email = $customer_data['email'];
											}

											if (!empty($client_email)) {
												$client_is_b2b = User::data_single($client_email, 'b2b', 'email');
											}
										?> -->
										<WebshopLoyaltyNew />

										<!-- FIXME - potrebno je doraditi, ovo je samo ugrubo  -->
										<!-- <BaseLocationPoints v-slot="{items: locations}">
											<div class="wp-pickup-products">
												<BaseCmsLabel class="status-info-label" code="pickup_info" tag="div" />
												<BaseCmsLabel class="wp-pickup-products-info" code="cart_availability_onlyinoffice" tag="div" />
												<BaseFormField :item="locations[0]">
													<BaseCmsLabel class="wp-pickup-select-label" code="select_store" tag="span" />
													<BaseFormInput type="select" id="field-shipping_pickup_location">
														<option value="">-</option>
														<option v-for="location in locations" :key="location.name" :value="location.value">{{ location.title }}</option>
													</BaseFormInput>
												</BaseFormField>
											</div>
										</BaseLocationPoints> -->

										<div id="items_shoppingcart">
											<!-- <?php
												if (!empty($available_for_pickup_products)) {
													$products = array_diff_key($products, $available_for_pickup_products);
												}
												if (!empty($not_available_for_pickup_products)) {
													$products = array_diff_key($products, $not_available_for_pickup_products);
												}
											?>
											<?php if (!empty($available_for_pickup_products)) : ?>
												<?php $i=1; ?>
												<?php foreach ($available_for_pickup_products as $product_code => $product_data_available_for_pickup): ?>
													<?php echo View::factory('webshop/shopping_cart_entry', ['product_code' => $product_code, 'product_data' => $product_data_available_for_pickup, 'product_status' => $products_status[$product_code], 'i' => $i, 'locations' => (!empty($locations) ? $locations : [])]); ?>
													<?php $i++; ?>
												<?php endforeach; ?>
											<?php endif; ?> -->

											<!-- PHP POZIV PROIZVODA 
												<?php echo View::factory('webshop/shopping_cart_entry', ['product_code' => $product_code, 'product_data' => $product_data, 'product_status' => $products_status[$product_code], 'i' => $i, 'locations' => (!empty($locations) ? $locations : [])]); ?>
											-->

											<WebshopCartItem v-for="item in parcels[0].items" :data="item" :key="item.shopping_cart_code" />
											<!-- <div class="wp-notavailable-for-pickup-products">
												<div class="wp-pickup-notavailable">
													<span v-html="labels.get('unable_to_complete_order').replace('%TOTAL_PRODUCTS%', `${parcels[0].items.filter(item => item?.type === 'pickup').length}`)"></span>
												</div>
											</div> -->

											<!-- <?php if (!empty($not_available_for_pickup_products)) : ?>
												<div class="wp-notavailable-for-pickup-products">
													<div class="wp-pickup-notavailable"><?php echo str_replace('%TOTAL_PRODUCTS%', count($not_available_for_pickup_products), Arr::get($cmslabel, 'unable_to_complete_order')); ?></div>
													<?php $codes = implode(',', array_keys($not_available_for_pickup_products)); ?>
													<a class="wp-btn wp-btn-delete wp-pickup-btn-delete" href="javascript:cmswebshop.shopping_cart.remove_all('<?php echo $codes; ?>');"><span><?php echo Arr::get($cmslabel, 'remove_products', 'Ukloni ove proizvode iz košarice'); ?></span></a>
													<?php $i=1; ?>
													<?php foreach ($not_available_for_pickup_products as $product_code => $product_data_not_available_for_pickup): ?>
														<?php echo View::factory('webshop/shopping_cart_entry', ['product_code' => $product_code, 'product_data' => $product_data_not_available_for_pickup, 'product_status' => $products_status[$product_code], 'i' => $i, 'locations' => (!empty($locations) ? $locations : [])]); ?>
														<?php $i++; ?>
													<?php endforeach; ?>
												</div>
											<?php endif; ?> -->
										</div>

										<WebshopFreeDeliveryList v-if="cart" :toFree="toFree" :cart="cart" />
										<WebshopCartWishlist mode="cart" :parcels="parcels" />
									</div>
								</div>
								<div class="w-col w-col2">
									<div class="w-col2-cnt-top"></div>
									<div class="w-col-cnt w-col2-cnt">
										<div class="w-col2-all-cnt">
											<WebshopCouponForm mode="cart" />
											<WebshopPriorityOrder />

											<BaseCmsLabel code="cart_total" class="cart-totals-title" tag="div" />
											<WebshopTotal />

											<BaseWebshopCartErrors v-slot="{errors, warnings}">
												<NuxtLink @click="onFinishShopping" v-if="urls.webshop_customer && !errors && !warnings" :to="urls.webshop_customer" class="btn btn-orange w-btn-finish cart-finish-shopping"><BaseCmsLabel code="finish_shopping" tag="span" /></NuxtLink>
												<!-- <a class="btn btn-orange w-btn-finish cart-finish-shopping <?php if ($disable_checkout) : ?>disabled<?php endif;?>"  href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>#webshop_form"><span><?php echo Arr::get($cmslabel, 'finish_shopping', 'Dovrši kupovinu'); ?></span></a> -->
											</BaseWebshopCartErrors>

											<WebshopFreeDeliveryProgressBar :parcels="parcels" :toFree="toFree" />
										</div>
									</div>
								</div>
							</div>
						</template>

						<BaseCmsLabel v-else code="empty_shopping_cart" class="empty-cart" tag="div" />
					</div>
				</BaseWebshopFreeShipping>
			</BaseWebshopCart>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const props = defineProps(['b2b', 'parcels']);
	const labels = useLabels();

	/* const {prependTo, onMediaQuery} = useDom();
		onMediaQuery({
		query: '(max-width: 980px)',
		enter: () => {
			prependTo('.free-delivery-widget', '.w-col2');
		}
	}); */
</script>
