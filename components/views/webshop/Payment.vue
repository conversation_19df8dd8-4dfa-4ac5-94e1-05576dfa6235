<template>
	<Body class="" />
	<BaseCmsPage v-slot="{page}">
		<WebshopCheckoutLayout>
			<template #wcCol1>
				<WebshopStep :step="1" :completed="true" />
				<BaseWebshopCheckout v-slot="{cart}">
					<BaseWebshopPaymentForm class="step3 df wc-row form-label wc-form ajax_siteform ajax_siteform_loading" action="#webshop_form" v-slot="{fields, loading, status, onPaymentUpdate}">
						<div v-if="status?.data?.errors">
							<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
						</div>

						<div class="payment-options">
							<WebshopStep :step="2" :title="true" />
							<div class="field-payment-cnt">
								<template v-for="field in fields" :key="field.value">
									<BaseFormField :item="field" v-slot="{errorMessage}">
										<BaseFormInput :id="field.code" option-class="payment-row">
											<template #default="{item}">
												<span @click="onPaymentUpdate(item)">{{item.title}}</span>
												<div v-if="item.description" class="payment_info" v-html="item.description"></div>
											</template>
										</BaseFormInput>

										<span class="error" v-show="errorMessage" v-html="errorMessage" />
									</BaseFormField>
								</template>
							</div>
							<button class="btn btn-primary btn-checkout-last g-recaptcha" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="goto_step3_button" tag="span" /></button>
						</div>
					</BaseWebshopPaymentForm>

					<WebshopStep :step="3" />
				</BaseWebshopCheckout>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>
