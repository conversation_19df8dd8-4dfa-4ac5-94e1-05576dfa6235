<template>
	<Body class="page-webshop-shipping" />
	<BaseCmsPage v-slot="{page}">
		<WebshopCheckoutLayout>
			<template #wcCol1>
				<div class="wc-col wc-col1 wc-step2-col1">
					<div class="wc-col-cnt">
						<ClientOnly>
							<BaseWebshopCustomerForm class="form step1-form form-animated-label wc-form form-label" v-slot="{loading, fields, errors}" data-autofocus>
								<BaseCmsLabel code="step1" tag="h2" class="wc-title" />
								<div v-html="page.content" v-if="page?.content" v-interpolation />

								<template v-if="Object.keys(errors)?.length">
									<BaseCmsLabel tag="div" class="global-error" code="form_validation_error" data-scroll-to-error />
								</template>
								<!-- FIXME INTEG dodati polja za odabir dr<PERSON>ave (select) -->
								<BaseFormField v-for="field in fields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel, isTouched, value}">
									<div class="field" :class="['field-' + field.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}, {'field-autocomplete': field.name == 'location'}]" v-if="!['zipcode', 'city'].includes(field.name)">
										<BaseFormInput :readonly="loading" />
										<label :for="field.name"><BaseCmsLabel :code="field.name" /></label>
										<BaseCmsLabel tag="span" class="phone-tooltip" code="phone_tooltip" v-if="field.name == 'phone'" />
										<span class="error" v-show="errorMessage" v-html="errorMessage" />
									</div>
								</BaseFormField>

								<button class="btn btn-orange checkout-btn-next-step noarrow" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="next_step" tag="span" /></button>
							</BaseWebshopCustomerForm>
						</ClientOnly>
					</div>

					<WebshopStep :step="2" />
					<WebshopStep :step="3" />
				</div>
			</template>

			<template #wcCol2> </template>
			<!-- 
			<template #header>
				<WebshopCheckoutHeader :step="2" />
			</template>

			<template #wcCol2>
				<div class="step-form-cnt step-form1-cnt">
					<div class="step-form-cnt-inner">
						<WebshopStep :step="1" :title="true" />
						<ClientOnly>
							<BaseWebshopCustomerForm class="form step1-form form-animated-label" v-slot="{loading, fields, errors}" data-autofocus>
								<div class="step1-form-inner">
									<template v-if="Object.keys(errors)?.length">
										<BaseCmsLabel tag="div" class="global-error" code="form_validation_error" data-scroll-to-error />
									</template>
									<BaseFormField v-for="field in fields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel, isTouched, value}">
										<p class="field" :class="['field-' + field.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
											<BaseFormInput :readonly="loading" />
											<label :for="field.name"><BaseCmsLabel :code="field.name" /></label>
											<BaseCmsLabel tag="span" class="phone-note" code="phone_note" v-if="field.name == 'phone'" />
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</p>
									</BaseFormField>
								</div>

								<button class="btn btn-orange checkout-btn-next-step noarrow" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="next_step" /></button>
							</BaseWebshopCustomerForm>
						</ClientOnly>
					</div>
				</div>

				<div class="checkout-steps">
					<WebshopStep :step="2" />
					<WebshopStep :step="3" />
					<WebshopStep :step="4" />
				</div>
			</template>
			-->
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<style lang="less" scoped>
	.field-location{
		:deep(.autocomplete-container){
			position: absolute; top: 52px; width: 100%; border: none!important;
			.ui-autocomplete{
				height: auto; padding: 0!important; margin: 0!important; max-height: 200px; overflow: auto; box-shadow: 0 10px 15px rgba(0,0,0,0.1); border: 1px solid @borderColor/1.2; border-top: 0; border-radius: 0 0 @borderRadius @borderRadius;
				li{
					display: block; padding: 4px 25px!important; border-top: none; border-left: none; border-right: 0;
					&:before{display: none!important;}
					&.active{color: @lightGreen; text-decoration: underline; background: #fff;}
					@media (min-width: @h){
						&:hover{color: @lightGreen; text-decoration: underline; background: #fff;}
					}
				}
			}
		}
		@media (max-width: @m) {
			.autocomplete-container{
				.ui-autocomplete{
					a{padding: 4px 15px;}
				}
			}
		}
	}
</style>
