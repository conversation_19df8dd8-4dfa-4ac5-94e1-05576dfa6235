<template>
	<Body class="page-checkout-login" />
	<BaseCmsPage v-slot="{page}">
		<WebshopCheckoutLayout>
			<template #wcCol1>
				<div class="wc-col wc-col1 wc-step1-col1">
					<BaseCmsLabel code="guest_checkout" tag="h2" class="wc-subtitle wc-subtitle-login" />
					<div v-if="page?.content" v-html="page.content" v-interpolation class="wc-cnt" />

					<!-- 
						FIXME INTEG dodati gtm s linije ispod
						<form data-tracking_gtm_checkout_advanced="1|Nastavi kao gost" class="form-label wc-guest-form ajax_siteform ajax_siteform_loading" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login">
					-->
					<BaseWebshopGuestForm class="form form-animated-label form-label login-form guest-login wc-guest-form" v-slot="{loading, fields}">
						<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
							<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}]">
								<BaseCmsLabel tag="label" for="guest-email" :code="item.name" />
								<BaseFormInput id="guest-email" />
								<span class="error" v-show="errorMessage" v-html="errorMessage" />
							</p>
						</BaseFormField>
						<button class="btn btn-checkout btn-wc-guest" type="submit" :class="{'loading': loading}"><UiLoader size="small" v-if="loading" /><BaseCmsLabel code="continue_without_signup" tag="span" /></button>
					</BaseWebshopGuestForm>
				</div>
			</template>

			<template #wcCol2>
				<BaseCmsLabel code="have_account" tag="h2" class="wc-subtitle wc-subtitle-login" />
				<BaseCmsLabel code="login_to_buy" tag="div" class="wc-cnt" />

				<AuthLoginForm mode="checkout" />
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>
