<template>
	<section class="ln-section ln-section-products-slider" :class="{'spacing': item.elements[0]?.elements.some(element => element.large_space > 0)}" :id="'position'+item.position" v-if="item.elements[0].elements?.length">
		<div class="wrapper">
			<div class="section-header" v-if="item.elements[0]?.features">
				{{item.elements[0].features}}
			</div>
			<BaseUiSwiper
				v-if="item.elements[0]?.elements"
				class="cd-rp-slider ln-products-slider slick-arrow3 blazy-container"
				:options="{
					slidesPerView: 1,
					slidesPerGroup: 1,
					spaceBetween: 0,
					watchSlidesProgress: true,
					enabled: false,
					breakpoints: {
						900: {
							enabled: true,
							spaceBetween: -1,
							slidesPerView: 5,
							slidesPerGroup: 5,
							watchSlidesProgress: true,
						},
						1250: {
							enabled: true,
							spaceBetween: -1,
							slidesPerView: 5,
							slidesPerGroup: 5,
							watchSlidesProgress: true,
						}
					}
				}">
				<BaseUiSwiperSlide v-for="itemElement in item.elements[0].elements" :key="itemElement.id">
					<CatalogIndexEntry :item="itemElement" :extraclass="'no-shadow cp-rp'" />
				</BaseUiSwiperSlide>
			</BaseUiSwiper>
		</div>
	</section>
</template>

<script setup>
	const props = defineProps(['item']);
</script>

<style lang="less" scoped>
	.cd-rp-slider{
		:deep(.swiper){
			overflow: inherit; padding-bottom: 110px;
			@media (max-width: @t){padding-bottom: 50px;}
			@media (max-width: @m){padding-bottom: 0;}
		}
		:deep(.swiper-wrapper){box-sizing: border-box;}
		:deep(.swiper-slide){
			flex-grow: 1; flex-shrink: 0; display: flex; opacity: 0; visibility: hidden; transition: opacity 0.3s, visibility 0.3s; height: auto;
			&.swiper-slide-visible{opacity: 1; visibility: visible;}
			&.swiper-slide-prev{opacity: 0; visibility: hidden;}
			&:hover{z-index: 1; position: relative;}
		}
		.cp{width: 100%;}
		:deep(.cp-addtocart){border: transparent; box-shadow: 0 10px 30px 0 rgba(0,0,0,0.25)}
		@media (max-width: 1670px){
			:deep(.swiper-button-prev){left: -25px;}
			:deep(.swiper-button-next){right: -25px;}
		}
		@media (max-width: @t){
			:deep(.cp-addtocart){box-shadow: none;}
		}
		@media (max-width: @m){
			width: 100%;
			:deep(.swiper-navigation){display: none;}
			:deep(.swiper-wrapper){padding: 1px 16px; overflow-x: auto;}
			:deep(.swiper-slide){opacity: 1; visibility: visible; flex-grow: 0; flex-shrink: 0; width: 152px!important; margin-left: -1px;}
			:deep(.cp){
				.cp-btn-addtocart span:before{display: block;}
			}
		}
	}
</style>
