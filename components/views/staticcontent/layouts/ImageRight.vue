<template>
	<!-- FIXME INTEG ne radi sekcija -->
	<div class="ln-section ln-section-cols ln-section-image ln-section-image-right" :class="{'spacing': item.elements[0]?.elements.some(element => element.large_space > 0)}" :id="'position'+item.position" v-if="item.elements[0].elements?.length">
		<div class="ln-section-col ln-section-col1">
			<BaseUiImage loading="lazy" :data="item.elements[0].image?.['width960-height550-crop1']" default="/images/no-image-800.jpg" />
		</div>
		<div class="ln-section-col ln-section-col2">
			<h2 class="title" v-if="item.elements[0]?.title">{{item.elements[0].title}}</h2>
			<div class="lists cnt" v-if="item.elements[0]?.content" v-html="item.elements[0].content" v-interpolation />
			<NuxtLink class="btn btn-green btn-landing" :to="item.elements[0].url" v-if="item.elements[0]?.url"
				><span>{{item.elements[0].url_text}}</span></NuxtLink
			>
		</div>
	</div>
</template>
<script setup>
	const props = defineProps(['item']);
</script>
