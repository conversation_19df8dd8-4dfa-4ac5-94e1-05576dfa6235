<template>
	<template v-if="instashopData?.items?.length">
		<Body :class="{'instashop-active': instashopData.instashopActive == true}" />
		<div class="instashop-modal" v-if="instashopData.instashopActive == true">
			<div class="instashop-modal-header">
				<BaseCmsLabel code="instashop_related" tag="strong" class="instashop-modal-title" />
				<div class="fancybox-close instashop-modal-close" @click="instashopData.instashopActive = !instashopData.instashopActive"></div>
			</div>
			<div class="instashop-modal-main">
				<div class="fancybox-close instashop-modal-close" @click="instashopData.instashopActive = !instashopData.instashopActive"></div>
				<div class="instashop-modal-items">
					<BaseUiSwiper
						class="instashop-items-slider"
						:options="{
						slidesPerView: 1,
						slidesPerGroup: 1,
						autoHeight: true,
						initialSlide: instashopData.instashopItemIndex,
						loop: true,
						effect: 'fade',
						fadeEffect: {
							crossFade: true
						},
						pagination: {
							enabled: true,
							clickable: true,
						},
					}">
						<BaseUiSwiperSlide v-for="item in instashopData.items" :key="item.id" class="instashop-item">
							<div class="pd-instashop-body">
								<template v-if="item?.instashopIds?.length">
									<BaseCatalogProductsWidget :fetch="{id: item.instashopIds}" v-slot="{items: products}">
										<div class="pd-instashop-col1">
											<figure class="instashop-image" :class="{'no-image': !item?.main_image?.length}">
												<BaseUiImage :data="item.main_image_thumbs?.['width800-height800-crop1']" default="/images/no-image-500.jpg" loading="lazy" />
												<span class="instashop-image-points" v-if="item?.images[0]?.imagemaps?.length">
													<template v-for="(imagemap,index) in [...item.images[0].imagemaps].sort((a, b) => a.id - b.id)" :key="imagemap.id">
														<span
															class="instashop-point pd-instashop-point"
															:style="{ top: `${imagemap.posRelative.y}%`, left: `${imagemap.posRelative.x}%` }"
															:class="{'active': instashopPointActive == index+1}"
															@mouseenter="instashopActive(index+1,imagemap.id)"
															@mouseleave="instashopInactive()">
															<span class="instashop-num pd-instashop-point-num"
																><span>{{index+1}}</span></span
															>
															<template v-for="item in products" :key="item.id">
																<div class="instashop-tip pd-instashop-point-tip" v-if="item.id == imagemap.id">
																	<NuxtLink :to="item.url_without_domain" target="_parnet"><BaseCmsLabel code="instashop_detail" /></NuxtLink>
																</div>
															</template>
														</span>
													</template>
												</span>
											</figure>
										</div>
										<div class="pd-instashop-col2">
											<div class="c-items  pd-instashop-items" v-if="products?.length">
												<CatalogIndexEntry v-for="item in products" :key="item.id" :item="item" list="" class="cp-list" mode="instashop" :class="{'hover': item.id == instashopPointId}" />
											</div>

											<BasePublishPostsWidget :fetch="{related_item_id: item.id, related_code: 'related', limit: 1}" v-slot="{items: publishes}">
												<template v-if="publishes?.length">
													<div class="publish-item" v-for="publish in publishes" :key="publish.id">
														<div class="publish-item-image">
															<BaseUiImage :data="publish.main_image_thumbs?.['width355-height355-crop1']" default="/images/no-image-260.jpg" loading="lazy" />
														</div>
														<div class="publish-item-cnt">
															<div class="publish-item-category">{{publish.category_title}}</div>
															<div class="publish-item-title">{{publish.title}}</div>
															<div class="btn publish-item-btn"><BaseCmsLabel code="view_recipe" tag="span" /></div>
														</div>
													</div>
												</template>
											</BasePublishPostsWidget>

											<template v-if="products?.length">
												<WebshopInstashopAddAll :items="products" />
											</template>
										</div>
									</BaseCatalogProductsWidget>
								</template>
								<template v-else>
									<div class="pd-instashop-col1">
										<figure class="instashop-image">
											<BaseUiImage :data="item.main_image_thumbs?.['width720-height720-crop1']" default="/images/no-image-500.jpg" loading="lazy" />
										</figure>
									</div>
									<div class="pd-instashop-col2">
										<BasePublishPostsWidget :fetch="{related_item_id: item.id, related_code: 'related', limit: 1}" v-slot="{items: publishes}">
											<template v-if="publishes?.length">
												<div class="publish-item" v-for="publish in publishes" :key="publish.id">
													<div class="publish-item-image">
														<BaseUiImage :data="publish.main_image_thumbs?.['width355-height355-crop1']" default="/images/no-image-260.jpg" loading="lazy" />
													</div>
													<div class="publish-item-cnt">
														<div class="publish-item-category">{{publish.category_title}}</div>
														<div class="publish-item-title">{{publish.title}}</div>
														<div class="btn publish-item-btn"><BaseCmsLabel code="view_recipe" tag="span" /></div>
													</div>
												</div>
											</template>
										</BasePublishPostsWidget>
									</div>
								</template>
							</div>
						</BaseUiSwiperSlide>
					</BaseUiSwiper>
				</div>
			</div>
		</div>
	</template>
</template>

<script setup>
	const props = defineProps(['instashopData']);
	const {mobileBreakpointInstashop} = inject('rwd');

	const swiperStartIndex = (props.instashopData?.instashopItemIndex?.length) ? props.instashopData.instashopItemIndex : 0;

	const instashopPointActive = ref();
	const instashopPointId = ref();
	function instashopActive(index, imagemap){
		instashopPointActive.value = index;
		instashopPointId.value = imagemap;
	}
	function instashopInactive(){
		instashopPointActive.value = null;
		instashopPointId.value = null;
	}
</script>

<style lang="less" scoped>
	.instashop-modal{
		position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(28,34,25,0.75); padding: 100px; z-index: 100000; display: flex; align-items: center; justify-content: center;
		@media (max-width: 1700px){padding: 100px 200px;}
		@media (max-width: @l){padding: 0 120px;}
		@media (max-width: 1140px){padding: 0 85px;}
		@media (max-width: @instashopTp){padding: 0;}
		@media (max-width: 800px){display: flex; flex-flow: column; background: #fff; align-items: flex-start; justify-content: flex-start; }
	}
	.instashop-modal-header{
		display: none;
		@media (max-width: 800px){display: flex; position: relative; align-items: center; justify-content: space-between; width: 100%; height: 48px; padding: 0 60px 0 16px;}
	}
	.instashop-modal-main{
		position: relative; display: block; background: #fff;  border-radius: 3px; box-shadow: 0 10px 25px rgba(0,0,0,0.5); width: 1480px; height: 100%;
		@media (max-width: 1700px){width: 100%;}
		@media (max-width: @l){height: 80svh;}
		@media (max-width: @instashopTp){width: 620px;}
		@media (max-width: 800px){
			width: 100%; border-radius: 0; box-shadow: none; height: calc(~"100svh - 48px");
			.instashop-modal-close{display: none;}
		}
	}
	.instashop-modal-close{
		position: absolute; top: 0; right: 0; cursor: pointer; z-index: 10; background: @red; border-radius: 0; border: none; width: 40px; height: 40px;
		&:before{font-size: 16px;}
		@media (max-width: 800px){
			width: 32px; height: 32px; top: auto; right: 16px; border-radius: 2px;
			&:before{font-size: 10px;}
		}
	}
	.instashop-modal-items{display: block; height: 100%;}
	.instashop-items-slider{
		display: block; height: 100%;
		:deep(.swiper){height: 100%;}
		:deep(.swiper-wrapper){max-height: 100%;}
		:deep(.swiper-slide){height: 100%;}
	}
	:deep(.swiper-navigation){
		@media (max-width: 800px){bottom: auto; aspect-ratio: 1; display: flex; align-items: center;}
	}
	:deep(.swiper-button){
		top: calc(~"50% - 40px");
		@media (max-width: 800px){
			width: 48px; height: 48px; background: #fff; border-radius: 2px; box-shadow: 0 15px 20px rgba(0,0,0,0.2); top: auto;
			&:before{color: @textColor; font-size: 18px;}
		}
	}
	:deep(.swiper-button-prev){
		left: -70px;
		@media (max-width: 800px){left: 16px;}
	}
	:deep(.swiper-button-next){
		right: -70px;
		@media (max-width: 800px){right: 16px;}
	}

	.pd-instashop-body{
		height: 100%;
		//@media (max-width: @instashopTp){max-height: 100%; overflow-y: auto;}
	}

	.instashop-image{
		position: relative; display: block; line-height: 0; height: 100%;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%;}
		@media (max-width: 1700px){height: auto;}
		@media (max-width: 800px){height: 100%; display: flex; align-items: center; justify-content: center;}
	}
	.instashop-image-points{position: absolute; top: 0; right: 0; bottom: 0; left: 0;}
	.instashop-point{position: absolute; display: flex; flex-flow: column; align-items: center;}
	.pd-instashop-col2{
		max-height: 792px; overflow-y: auto;
		@media (max-width: @l){max-height: 100%;}
		@media (max-width: @instashopTp){max-height: calc(~"100svh - 100vw - 48px");}
	}
	.pd-instashop-items{
		display: flex; flex-flow: column;
		:deep(.cp){
			&:hover{box-shadow: none; border-color: @lightGreen;}
		}
	}

	.instashop-bottom{
		position: relative; display: flex; justify-content: flex-end; align-items: center; margin-top: 24px; gap: 32px;
		@media (max-width: @t){margin-top: 16px; gap: 8px;}
		@media (max-width: @instashopTp){padding-bottom: 20px;}
		@media (max-width: 800px){margin: 0; padding: 20px 16px; flex-flow: column; gap: 12px;}
	}
	.instashop-total-products{
		display: flex; flex-flow: column; align-items: flex-end; font-size: 16px; line-height: 24px; gap: 3px;
		@media (max-width: @l){font-size: 14px; line-height: 20px;}
		@media (max-width: 800px){flex-flow: row; align-items: center; justify-content: space-between; width: 100%;}
	}
	.btn-instashop-add{
		width: 280px; cursor: pointer;
		span{
			display: flex; align-items: center;
			&:before{.icon-cart(); font: 24px/1 @fonti; color: #fff; margin-right: 12px;}
		}
		@media (max-width: @l){width: auto;}
		@media (max-width: 800px){width: 100%;}
	}

	.publish-item{
		position: relative; display: flex; border: 1px solid @borderColor; margin-top: -1px; padding: 12px 16px 12px 8px;
		@media (max-width: 800px){padding: 12px 16px 12px 12px;}
	}
	.publish-item-image{
		width: 120px; height: 120px; display: flex; align-items: center; justify-content: center; flex-grow: 0; flex-shrink: 0; margin-right: 15px;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%;}
		@media (max-width: 800px){width: 112px; height: 112px; margin-right: 12px;}
	}
	.publish-item-category{display: block; font-size: 12px; font-weight: bold; color: @lightGreen; text-transform: uppercase; margin-bottom: 4px;}
	.publish-item-title{display: block; font-size: 14px; line-height: 20px; margin-bottom: 8px;}
	.publish-item-btn{height: 44px; font-size: 14px; padding: 0 33px; font-weight: bold; cursor: pointer;}
</style>
