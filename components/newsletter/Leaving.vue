<template>
	<BaseThemeUiModal name="newsletter-leaving" v-slot="{onClose}" :cookie="true" cookie-expire="1y" :page-leave="pageLeave" :scroll-trigger="isMobile ? 3 : 0" :key-closable="false">
		<div class="nw-leaving-container" :style="labelImage.length ? { backgroundImage: `url(${labelImage[0].url})` } : {}">
			<div class="nw-leaving">
				<div class="nw-leaving-logo"></div>
				<div class="nw-leaving-cnt">
					<div class="nw-leaving-title" v-html="labels.get('newsletter_widget_title')"></div>
					<div class="nw-leaving-subtitle" v-html="labels.get('newsletter_widget_description')"></div>

					<BaseNewsletterSignupForm class="nw-leaving-form" event="leave" v-slot="{fields, gdprFields, status}">
						<template v-if="!status?.success">
							<div class="nw-fields nw-leaving-fields">
								<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage}">
									<template v-if="item.type != 'hidden'">
										<BaseFormInput class="nw-input" :placeholder="labels.get('enter_email')" />
										<span class="error nw-error nw-leaving-error" v-show="errorMessage" v-html="errorMessage" />
									</template>
									<BaseFormInput v-else />
								</BaseFormField>

								<button class="btn btn-red nw-button" type="submit"><BaseCmsLabel code="newsletter_signup" /></button>
							</div>
							<div class="nw-checkbox nw-leaving-checkbox" v-if="gdprFields">
								<BaseFormField v-for="field in gdprFields" :key="field.name" :item="field">
									<BaseFormInput v-if="field.type == 'hidden'" />
									<p class="field" v-else v-interpolation>
										<BaseFormInput :id="`nl-leaving-${field.name}`" />
										<label :for="`nl-leaving-${field.name}`"><span v-html="labels.get('newsletter_gpdr_link')"></span></label>
									</p>
								</BaseFormField>
							</div>
						</template>
						<BaseCmsLabel v-show="status?.success" tag="div" class="nw-success" code="success_subscribe" />
					</BaseNewsletterSignupForm>
				</div>
			</div>
		</div>
	</BaseThemeUiModal>
</template>

<script setup>
	const labels = useLabels();
	const {onMediaQuery} = useDom();
	const {matches: isMobile} = onMediaQuery({
		query: '(max-width: 990px)',
	});
	const {gdprApproved} = useGdpr();
	const pageLeave = computed(() => {
		return (!isMobile.value && gdprApproved()?.length) ? true : false;
	});

	const { extractImageUrls } = useImages();
	const labelImage = computed(() => {
		const label = isMobile.value ? labels.get('newsletter_popup_image') : labels.get('newsletter_popup_image');
		return (label) ? extractImageUrls(label) : [];
	})
</script>

<style lang="less" scoped>
	:deep(.base-modal-cnt-close){
		background: none; text-decoration: none; width: 38px; height: 38px; line-height: 38px; border-radius: 100px; background: @darkGreen; border: 3px solid #fff; box-sizing: content-box; display: flex; align-items: center; justify-content: center; top: -18px!important; right: -18px!important; .transition(all);
		svg{display: none;}
		&:before{.icon-cross(); font: 14px/14px @fonti; color: #fff;}
		&:hover{background: @darkGreen/1.2; text-decoration: none;}
		@media (max-width: @l){
			width: 30px; height: 30px;
			&:before{font-size: 12px;}
		}
		@media (max-width: @tp){
			width: 25px; height: 25px; top: 13px!important; right: 15px!important;
			&:before{font-size: 10px;}
		}
	}

	:deep(.base-modal-body){
		@media (max-width: @tp){width: 100%;}
	}
	:deep(.base-modal-cnt){
		width: 900px;
		@media (max-width: @tp){width: 100%; max-width: 100%;}
	}
	:deep(.newsletter-leaving){
		@media (max-width: @tp){
			align-items: flex-end; justify-content: inherit;
		}
	}
	.nw-leaving-container{
		background-size: cover; background-repeat: no-repeat; background-position: center; min-height: 420px;
		@media (max-width: @tp){background: #f9f9f9!important; min-height: inherit;}
	}
</style>
